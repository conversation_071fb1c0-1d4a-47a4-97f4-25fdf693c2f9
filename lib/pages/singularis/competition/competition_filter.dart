import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/domain_filter_list.dart';
import 'package:masterg/data/models/response/home_response/domain_list_response.dart';
import 'package:masterg/pages/singularis/competition/competition_filter_search_result_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import '../../custom_pages/custom_widgets/NextPageRouting.dart';

class CompetitionFilter extends StatefulWidget {
  final DomainListResponse? domainList;
  const CompetitionFilter({Key? key, this.domainList}) : super(key: key);

  @override
  State<CompetitionFilter> createState() => _CompetitionFilterState();
}

class _CompetitionFilterState extends State<CompetitionFilter> {
  List<int> selectedIdList = <int>[];
  String seletedIds = '';
  String selectedDifficulty = '';
  DomainFilterListResponse? domainFilterList;
  List<String> difficulty = ['Easy', 'Medium', 'Hard'];
  String domainId = '';
  int selectedIndex = 0;

  @override
  void initState() {
    getFilterList(widget.domainList!.data!.list![0].id.toString());
    // getFilterList(widget.domainList!.data!.list[0].id.toString());

    if (widget.domainList!.data!.list!.length != 0) {
      domainId = widget.domainList!.data!.list![0].id.toString();
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: BlocManager(
            initState: (BuildContext context) {},
            child: BlocListener<HomeBloc, HomeState>(
              listener: (context, state) {
                if (state is DomainFilterListState) {
                  handleDomainFilterListResponse(state);
                }
              },
              child: SafeArea(
                child: Container(
                  height: double.infinity,
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: ColorConstants.WHITE,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(8))),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            children: [
                              Text(
                                'filter_by',
                                style: Styles.semibold(size: 16),
                              ).tr(),
                              Spacer(),
                              IconButton(
                                  onPressed: () {
                                    selectedIndex = 0;
                                    seletedIds = '';
                                    selectedIdList = [];
                                    Navigator.pop(context);
                                  },
                                  icon: Icon(Icons.close))
                            ],
                          ),
                        ),
                        Divider(
                          color: ColorConstants.GREY_4,
                        ),
                        Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 4),
                                  child: Text(
                                    'domain',
                                    style: Styles.bold(size: 14),
                                  ).tr()),
                              Container(
                                child: Wrap(
                                  direction: Axis.horizontal,
                                  children: List.generate(
                                      widget.domainList!.data!.list!.length,
                                      (i) => InkWell(
                                            onTap: () {
                                              setState(() {
                                                selectedIndex = i;
                                                seletedIds = '';
                                                selectedIdList = [];
                                                domainId = widget.domainList!
                                                    .data!.list![i].id
                                                    .toString();
                                              });
                                              getFilterList(widget
                                                  .domainList!.data!.list![i].id
                                                  .toString());
                                            },
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 10, right: 5),
                                              child: Chip(
                                                side: i == selectedIndex
                                                    ? BorderSide(
                                                        color: ColorConstants()
                                                            .gradientRight())
                                                    : null,
                                                backgroundColor:
                                                    i == selectedIndex
                                                        ? ColorConstants()
                                                            .gradientRight()
                                                            .withOpacity(0.08)
                                                        : Color(0xffF2F2F2),
                                                label: Container(
                                                  child: Text(
                                                    '${widget.domainList!.data!.list?[i].name}',
                                                    style: Styles.semibold(
                                                        size: 12,
                                                        color: ColorConstants
                                                            .BLACK),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          )),
                                ),
                              ),
                              Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 4),
                                  child: Text(
                                    "job_roles",
                                    style: Styles.bold(size: 14),
                                  ).tr()),
                              if (domainFilterList != null)
                                Container(
                                  child: Wrap(
                                    direction: Axis.horizontal,
                                    children: List.generate(
                                        domainFilterList!.data!.list.length,
                                        (i) => InkWell(
                                              onTap: () {
                                                if (selectedIdList.contains(
                                                    domainFilterList!
                                                        .data!.list[i].id)) {
                                                  selectedIdList.remove(
                                                      domainFilterList!
                                                          .data!.list[i].id);
                                                } else {
                                                  selectedIdList.add(
                                                      domainFilterList!
                                                          .data!.list[i].id!);
                                                }

                                                setState(() {});
                                              },
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10, right: 5),
                                                child: Chip(
                                                  side: selectedIdList.contains(
                                                          domainFilterList!
                                                              .data!.list[i].id)
                                                      ? BorderSide(
                                                          color: ColorConstants()
                                                              .gradientRight())
                                                      : null,
                                                  backgroundColor:
                                                      selectedIdList.contains(
                                                              domainFilterList!
                                                                  .data!
                                                                  .list[i]
                                                                  .id)
                                                          ? ColorConstants()
                                                              .gradientRight()
                                                              .withOpacity(0.08)
                                                          : Color(0xffF2F2F2),
                                                  label: Container(
                                                    child: Text(
                                                        '${domainFilterList!.data!.list[i].title}',
                                                        style: Styles.regular(
                                                          size: 12,
                                                          color: ColorConstants
                                                              .BLACK,
                                                        )),
                                                  ),
                                                ),
                                              ),
                                            )),
                                  ),
                                ),
                              Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 4),
                                  child: Text(
                                    'difficulty_level',
                                    style: Styles.bold(size: 14),
                                  ).tr()),
                              Container(
                                child: Wrap(
                                  direction: Axis.horizontal,
                                  children: List.generate(
                                      difficulty.length,
                                      (i) => InkWell(
                                            onTap: () {
                                              if (selectedDifficulty ==
                                                  difficulty[i])
                                                selectedDifficulty = '';
                                              else
                                                selectedDifficulty =
                                                    difficulty[i];
                                              setState(() {});
                                            },
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 10, right: 5),
                                              child: Chip(
                                                side: selectedDifficulty ==
                                                        difficulty[i]
                                                    ? BorderSide(
                                                        color: ColorConstants()
                                                            .gradientRight())
                                                    : null,
                                                backgroundColor:
                                                    selectedDifficulty ==
                                                            difficulty[i]
                                                        ? ColorConstants()
                                                            .gradientRight()
                                                            .withOpacity(0.08)
                                                        : Color(0xffF2F2F2),
                                                label: Container(
                                                  child: Text(
                                                      '${tr('${difficulty[i].toLowerCase()}')}',
                                                      style: Styles.regular(
                                                        size: 12,
                                                        color: ColorConstants
                                                            .BLACK,
                                                      )),
                                                ),
                                              ),
                                            ),
                                          )),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  bool isFilter;
                                  String conSelectValue;
                                  if (selectedIdList.length != 0) {
                                    seletedIds = selectedIdList
                                        .toString()
                                        .replaceAll("[", "")
                                        .replaceAll("]", "");
                                  }

                                  if (selectedIdList.length == 0 &&
                                      selectedDifficulty != '') {
                                    isFilter = true;
                                    conSelectValue =
                                        '&competition_level=${selectedDifficulty.toLowerCase()}';
                                  } else if (selectedIdList.length == 0) {
                                    if (domainId != '') {
                                      isFilter = true;
                                      conSelectValue =
                                          '&competition_level=${selectedDifficulty.toLowerCase()}';
                                    } else {
                                      isFilter = false;
                                      conSelectValue =
                                          '&competition_level=${selectedDifficulty.toLowerCase()}';
                                    }
                                  } else
                                    isFilter = true;
                                  conSelectValue = seletedIds +
                                      '&competition_level=${selectedDifficulty.toLowerCase()}';

                                  print('isFilter==${isFilter}');
                                  print('conSelectValue==${conSelectValue}');
                                  print('domainId==${domainId}');

                                  Navigator.push(
                                          context,
                                          NextPageRoute(
                                              CompetitionFilterSearchResultPage(
                                                appBarTitle:
                                                    tr('search_competitions'),
                                                isSearchMode: isFilter,
                                                jobRolesId: conSelectValue,
                                                domainId: domainId,
                                              ),
                                              isMaintainState: true))
                                      .then((value) => null);
                                },
                                child: Container(
                                  height: 40,
                                  margin: EdgeInsets.only(
                                      left: 50, top: 20, right: 50, bottom: 20),
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(50),
                                    gradient: LinearGradient(colors: [
                                      ColorConstants().gradientLeft(),
                                      ColorConstants().gradientRight(),
                                    ]),
                                  ),
                                  child: Align(
                                    alignment: Alignment.center,
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: Text(
                                        'search_competitions',
                                        style: Styles.regular(
                                          size: 13,
                                          color: ColorConstants.WHITE,
                                        ),
                                      ).tr(),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            )));
  }

  void getFilterList(String ids) {
    BlocProvider.of<HomeBloc>(context).add(DomainFilterListEvent(ids: ids));
  }

  void handleDomainFilterListResponse(DomainFilterListState state) {
    var popularCompetitionState = state;
    setState(() {
      switch (popularCompetitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");

          break;
        case ApiStatus.SUCCESS:
          Log.v("Filter list State....................");
          domainFilterList = state.response;

          setState(() {});

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Filter list CompetitionListIDState ..........................${popularCompetitionState.error}");

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
