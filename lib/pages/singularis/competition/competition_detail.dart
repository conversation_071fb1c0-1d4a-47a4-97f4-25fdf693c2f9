import 'dart:convert';
import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/competition_content_list_resp.dart';
import 'package:masterg/data/models/response/home_response/competition_response.dart';
import 'package:masterg/data/models/response/home_response/training_detail_response.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/providers/assessment_detail_provider.dart';
import 'package:masterg/data/providers/assignment_detail_provider.dart';
import 'package:masterg/pages/ghome/widget/read_more.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_notes.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_session.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_video.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_youtube.dart';
import 'package:masterg/pages/singularis/competition/event_certificate.dart';
import 'package:masterg/pages/singularis/leaderboard_page.dart';
import 'package:masterg/pages/training_pages/assessment_page.dart';
import 'package:masterg/pages/training_pages/assignment_detail_page.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../utils/Log.dart';

enum CardType { assignment, assessment, session, video, note, youtube }

class CompetitionDetail extends StatefulWidget {
  // final Competition? competition;
  final int? competitionId;
  final bool? isEvent;
  const CompetitionDetail(
      {super.key, this.competitionId, this.isEvent = false});

  @override
  State<CompetitionDetail> createState() => _CompetitionDetailState();
}

class _CompetitionDetailState extends State<CompetitionDetail> {
  TrainingModuleResponse? competitionDetail;
  TrainingDetailResponse? programDetail;
  CompetitionContentListResponse? contentList;
  bool competitionDetailLoading = true;
  Competition? competition;
  dynamic data;

  @override
  void initState() {
    getCompetitionContentList();
    getCompetitionDetail();
    super.initState();
  }

  void getCompetitionContentList() {
    log("make api call");
    BlocProvider.of<HomeBloc>(context)
        .add(CompetitionContentListEvent(competitionId: widget.competitionId));
  }

  void getCompetitionDetail() {
    BlocProvider.of<HomeBloc>(context)
        .add(TrainingDetailEvent(programId: widget.competitionId));
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    // String startDate = '${widget.competition?.startDate?.split(' ').first}';
    // DateTime start = DateFormat("yyyy-MM-dd").parse(startDate);
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) {
              if (state is CompetitionDetailState) {
                handlecompetitionDetailResponse(state);
              } else if (state is TrainingDetailState)
                handleTrainingDetailState(state);
              else if (state is AppJobListCompeState) {
                log('handle the state is now');
                handleCompetitionListState(state);
              }
            },
            child: Scaffold(
              backgroundColor: Color(0xffF2F2F2),
              appBar: AppBar(
                  backgroundColor: Color(0xffF2F2F2),
                  elevation: 0,
                  leading: IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.arrow_back,
                        color: Color(0xff0E1638),
                      )),
                  title: Text(
                    '${competition?.name ?? ''}',
                    style: Styles.semibold(),
                  )),
              body: SingleChildScrollView(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (competitionDetailLoading == false &&
                      competition != null) ...[
                    SizedBox(
                      width: double.infinity,
                      height: size.height * 0.3,
                      child: ClipRRect(
                        child: CachedNetworkImage(
                          imageUrl: '${competition?.image}',
                          width: double.infinity,
                          errorWidget: (context, url, error) => Image.asset(
                            'assets/images/comp_emp.png',
                          ),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${competition?.name}',
                            style: Styles.bold(color: Color(0xff0E1638)),
                          ),
                          SizedBox(
                            height: 3,
                          ),
                          if (competition?.organizedBy != null)
                            Wrap(
                              children: [
                                Text(
                                  'conducted_by',
                                  style: Styles.regular(
                                      size: 12, color: Color(0xff929BA3)),
                                ).tr(),
                                SizedBox(
                                  child: Text(
                                    ' ${competition?.organizedBy}',
                                    style: Styles.semibold(size: 12),
                                  ),
                                ),
                              ],
                            ),
                          SizedBox(
                            height: 4,
                          ),
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 3, horizontal: 6),
                                margin: EdgeInsets.symmetric(vertical: 8),
                                decoration: BoxDecoration(
                                    color: ColorConstants.WHITE,
                                    borderRadius: BorderRadius.circular(4)),
                                child: Text(
                                    '${competition?.competitionLevel?.toLowerCase()}',
                                    style: Styles.semibold(
                                      size: 12,
                                      color: ColorConstants.GREEN_1,
                                    )).tr(),
                              ),
                              if ((int.tryParse('${competition?.gScore}') ??
                                      0) !=
                                  0) ...[
                                SizedBox(
                                  width: 4,
                                ),
                                SizedBox(
                                  width: 4,
                                ),
                                Container(
                                    padding: EdgeInsets.symmetric(
                                        vertical: 2, horizontal: 6),
                                    margin: EdgeInsets.symmetric(
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                        color: ColorConstants.WHITE,
                                        borderRadius: BorderRadius.circular(4)),
                                    child: Row(
                                      children: [
                                        SizedBox(
                                            height: 15,
                                            child: Image.asset(
                                                'assets/images/coin.png')),
                                        SizedBox(
                                          width: 4,
                                        ),
                                        Text(
                                            '${competition?.gScore} ${tr('points')}',
                                            style: Styles.semibold(
                                              size: 12,
                                              color: ColorConstants.ORANGE_4,
                                            )),
                                      ],
                                    )),
                              ],
                              Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 3, horizontal: 6),
                                margin: EdgeInsets.only(
                                    left: 8, right: 5, top: 8, bottom: 8),
                                decoration: BoxDecoration(
                                    color: ColorConstants.WHITE,
                                    borderRadius: BorderRadius.circular(4)),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.calendar_month,
                                      size: 20,
                                    ),
                                    SizedBox(
                                      width: 4,
                                    ),
                                    competition!.startDate != null
                                        ? Text('${Utility.convertCourseTime(int.tryParse(competition!.startDate!) ?? 0, "dd MMM yyyy")}',
                                      style: Styles.regular(
                                        size: 12,
                                        color: Color(0xff5A5F73),
                                      ),)
                                     /*StrToTime(
                                            time:
                                                '${Utility.convertCourseTime(int.tryParse(competition!.startDate!) ?? 0, "dd MMM")}',
                                            dateFormat: 'dd MMM yyyy',
                                            appendString: '',
                                            textStyle: Styles.regular(
                                              size: 14,
                                              color: Color(0xff5A5F73),
                                            ),
                                          )*/
                                        : SizedBox(),

                                    //Change 14 Aug 24

                                    competition!.startDate != null
                                        ? Text(', ',
                                      style: Styles.regular(
                                          size: 12,
                                          color: ColorConstants.BODY_TEXT),)
                                        : SizedBox(),

                                    competition!.startDate != null
                                        ? Padding(
                                        padding:
                                        const EdgeInsets.only(top: 4.0),
                                        child: Text('${Utility.convertCourseTime(int.tryParse(competition!.startDate!) ?? 0, "hh:mm a")}',
                                          style: Styles.regular(
                                              size: 12,
                                              lineHeight: 1,
                                              color: ColorConstants.BODY_TEXT),),)

                                    /*competition!.endDate != null
                                        ? Text(' - ')
                                        : SizedBox(), //singh
                                    competition!.endDate != null
                                        ? Text('${Utility.convertCourseTime(int.tryParse(competition!.endDate!) ?? 0, "dd MMM yyyy")}',
                                      style: Styles.regular(
                                        size: 12,
                                        color: Color(0xff5A5F73),
                                      ),)*/


                                    /*StrToTime(
                                            time:
                                                '${Utility.convertCourseTime(int.tryParse(competition!.endDate!) ?? 0, "dd MMM yyyy")}',
                                            dateFormat: 'dd MMM yyyy',
                                            appendString: '',
                                            textStyle: Styles.regular(
                                              size: 14,
                                              color: Color(0xff5A5F73),
                                            ),
                                          )*/
                                        : SizedBox(),
                                    // competition!.startDate != null
                                    //     ? Text(
                                    //         '${Utility.convertCourseTime(int.tryParse(competition!.startDate!) ?? 0, "dd MMM")}')
                                    //     : SizedBox(),

                                    // competition!.endDate != null
                                    //     ? Text(
                                    //         ' - ${Utility.convertCourseTime(int.tryParse(competition!.endDate!) ?? 0, "dd MMM yyyy")}')
                                    // : SizedBox(),
                                  ],
                                ),
                              ),
                              //Text('• '),
                              Text('- ', style: TextStyle(fontWeight: FontWeight.bold),),
                              Container(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 3, horizontal: 6),
                                  margin: EdgeInsets.only(
                                      left: 1, right: 4, top: 8, bottom: 8),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.WHITE,
                                      borderRadius: BorderRadius.circular(4)),
                                  child: Row(children: [
                                    // Text(' •',
                                    //     style: Styles.regular(
                                    //         color: ColorConstants.GREY_2,
                                    //         size: 12)),
                                    // SizedBox(
                                    //   width: 5,
                                    // ),

                                    /*competition!.startDate != null
                                        ? Padding(
                                            padding:
                                                const EdgeInsets.only(top: 4.0),
                                            child: Text('${Utility.convertCourseTime(int.tryParse(competition!.startDate!) ?? 0, "hh:mm a")}',
                                              style: Styles.regular(
                                                  size: 12,
                                                  lineHeight: 1,
                                                  color: ColorConstants.BODY_TEXT),)

                                            )
                                        : SizedBox(),*/

                                    competition!.endDate != null
                                        ? Text('${Utility.convertCourseTime(int.tryParse(competition!.endDate!) ?? 0, "dd MMM yyyy")}',
                                      style: Styles.regular(
                                        size: 12,
                                        color: Color(0xff5A5F73),
                                      ),):SizedBox(),


                                    competition!.endDate != null
                                        ? Text(', ',
                                      style: Styles.regular(
                                          size: 12,
                                          color: ColorConstants.BODY_TEXT),)
                                        : SizedBox(),

                                    competition!.endDate != null
                                        ? Padding(
                                            padding:
                                                const EdgeInsets.only(top: 4.0),
                                            child: Text('${Utility.convertCourseTime(int.tryParse(competition!.endDate!) ?? 0, "hh:mm a ")}',
                                              style: Styles.regular(
                                                  size: 12,
                                                  lineHeight: 1,
                                                  color: ColorConstants.BODY_TEXT),)

                                      /*StrToTime(
                                              time:
                                                  '${Utility.convertCourseTime(int.tryParse(competition!.endDate!) ?? 0, "hh:mm a")}',
                                              dateFormat: ' hh:mm a ',
                                              appendString: Utility()
                                                      .isRTL(context)
                                                  ? competition!.endDate != null
                                                      ? tr('to')
                                                      : ''
                                                  : '',
                                              textStyle: Styles.regular(
                                                  size: 12,
                                                  lineHeight: 1,
                                                  color: ColorConstants.BODY_TEXT),
                                            )*/
                                            // Text(
                                            //   '${Utility.convertCourseTime(int.tryParse(competition!.endDate!) ?? 0, "hh:mm a")}',
                                            //   style: Styles.regular(
                                            //       size: 12,
                                            //       lineHeight: 1,
                                            //       color: ColorConstants.GREY_2),
                                            // ),
                                            )
                                        : SizedBox(),
                                  ])),

                            ],
                          ),

                          ReadMoreText(
                            text: '${competition?.description}',
                            color: Color(0xff5A5F73),
                            viewMore: tr('view_more'),
                          ),
                         //TODO: hide for show certificate an leaderBord  //widget.isEvent == true ? SizedBox(height: 20,) : Row(
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Center(
                                child: Container(
                                  width: size.width * 0.45,
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  margin: EdgeInsets.symmetric(vertical: 10),
                                  decoration: BoxDecoration(
                                      border:
                                          Border.all(color: Color(0xffFF2452)),
                                      borderRadius: BorderRadius.circular(8)),
                                  child: Center(
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                            'assets/images/leaderboard.png'),
                                        SizedBox(width: 8),
                                        InkWell(
                                          onTap: () {
                                            Navigator.of(context).push(
                                                MaterialPageRoute(
                                                    builder: (context) =>
                                                        LeaderboardPage(
                                                          competitionId: widget
                                                              .competitionId,
                                                        )));
                                          },
                                          child: Text(tr('view_leaderboard'),
                                              style: Styles.semibold(
                                                  size: 12,
                                                  color: Color(0xff5A5F73))),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 10),
                              // Spacer(),
                              programDetail?.data?.list?.first.certificateId !=
                                      null
                                  ? Center(
                                      child: InkWell(
                                        onTap: () {
                                          Navigator.of(context).push(
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      EventParticipationCertificate(
                                                          competitionId: widget
                                                              .competitionId)));
                                        },
                                        child: Container(
                                          width: size.width * 0.45,
                                          padding: EdgeInsets.symmetric(
                                            vertical: 8,
                                          ),
                                          margin: EdgeInsets.symmetric(
                                              vertical: 10),
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  color: Color(0xffFF2452)),
                                              borderRadius:
                                                  BorderRadius.circular(8)),
                                          child: Center(
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SvgPicture.asset(
                                                    'assets/images/certificate_icon.svg',
                                                    color: ColorConstants.BLACK,
                                                    height: 20),
                                                SizedBox(width: 8),
                                                Text(tr('view_certificate'),
                                                        style: Styles.semibold(
                                                            size: 12,
                                                            color: Color(
                                                                0xff5A5F73)))
                                                    .tr(),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  : SizedBox(),
                            ],
                          ),
                          SizedBox(
                            height: 4,
                          ),
                          if (competitionDetailLoading == false &&
                              contentList?.data?.list?.length != 0)
                            Text(tr('activities'),
                                style: Styles.bold(
                                    size: 14, color: Color(0xff0E1638)))
                        ],
                      ),
                    ),
                    ListView.builder(
                        physics: BouncingScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: contentList?.data?.list?.length,
                        itemBuilder: (context, index) {
                          bool isLocked = index != 0;

                          bool isTick = false;

                          if (contentList?.data?.list?[index]?.perCompletion !=
                                  0.0 &&
                              (contentList?.data?.list?[index]?.contentType ==
                                      'assignment' ||
                                  contentList
                                          ?.data?.list?[index]?.contentType ==
                                      'assessment') &&
                              double.parse(
                                      '${contentList?.data?.list?[index]?.overallScore ?? 0}') >=
                                  double.parse(
                                      '${contentList?.data?.list?[index]?.perCompletion ?? 0}')) {
                            isTick = true;
                          } else if (contentList
                                      ?.data?.list?[index]?.perCompletion !=
                                  0.0 &&
                              contentList?.data?.list?[index]
                                      ?.completionPercentage !=
                                  0.0 &&
                              !(contentList?.data?.list?[index]?.contentType ==
                                      'assignment' ||
                                  contentList
                                          ?.data?.list?[index]?.contentType ==
                                      'assessment') &&
                              double.parse(
                                      '${contentList?.data?.list?[index]?.completionPercentage ?? 0}') >=
                                  double.parse(
                                      '${contentList?.data?.list?[index]?.perCompletion ?? 0}')) {
                            isTick = true;
                          }
                          if (contentList?.data?.list?[index]?.activityStatus == 2) {
                            isTick = true;
                          }

                          if (contentList?.data?.list?[index]?.assesStatus == 'Completed') {
                            isTick = true;
                          }


                          if (index != 0) {
                            CompetitionContent? data = contentList?.data?.list?[index - 1];

                            if (data?.activityStatus != 0 && data?.activityStatus != null) {
                            //if (data?.activityStatus != 0) {
                              if (data?.perCompletion != 0.0 &&
                                  (data?.contentType == 'assignment' ||
                                      data?.contentType == 'assessment') &&
                                  double.parse('${data?.overallScore ?? 0}') >=
                                      double.parse('${data?.perCompletion}')) {
                                isLocked = false;

                              } else if (data?.perCompletion != 0.0 &&
                                  (data?.completionPercentage != null ||
                                      data?.completionPercentage != 0.0) &&
                                  !(data?.contentType == 'assignment' ||
                                      data?.contentType == 'assessment') &&
                                  double.parse(
                                          '${data?.completionPercentage}') >=
                                      double.parse('${data?.perCompletion}')) {
                                isLocked = false;

                              } else if (data?.perCompletion >= 0.0 &&
                                  (data?.contentType == 'assignment' ||
                                      data?.contentType == 'assessment') &&
                                  double.parse('${data?.overallScore ?? 0}') >=
                                      double.parse('${data?.perCompletion}')) {

                                isLocked = false;
                              }
                              log('activityStatus5:---${data?.activityStatus}');
                              if (data?.activityStatus == 2) {
                                isLocked = false;
                              }
                            }
                          }
                          /*if (programDetail?.data?.list?.first.type
                                  ?.toLowerCase() ==
                              'free') {
                            isLocked = false;
                          }*/

                          ///new code hide for not code available on production
                          if (programDetail?.data?.list?.first.enableContentLock == 0) {
                            isLocked = false;
                          }

                          return competitionCard(
                              contentList?.data?.list![index],
                              index ==
                                  ((contentList?.data?.list?.length ?? 1) - 1),
                              isLocked: isLocked,
                              isTick: isTick);
                        }),
                    SizedBox(
                      height: 3,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          //what's in for you
                          if (contentList
                                  ?.data?.competitionInstructions?.whatsIn !=
                              null)
                            Text(
                              tr('whats_foryou'),
                              style: Styles.bold(
                                  size: 14, color: Color(0xff5A5F73)),
                            ),
                          SizedBox(
                            height: 8,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 6),
                            child: Text(
                              '${contentList?.data?.competitionInstructions?.whatsIn ?? ''}',
                              style: Styles.regular(
                                  size: 14, color: Color(0xff5A5F73)),
                            ),
                          ),
                          SizedBox(
                            height: 20,
                          ),

                          if (contentList?.data?.competitionInstructions
                                  ?.instructions !=
                              null)
                            Text(
                              tr('instructions'),
                              style: Styles.bold(
                                  size: 14, color: Color(0xff5A5F73)),
                            ),
                          SizedBox(
                            height: 4,
                          ),

                          Padding(
                            padding: const EdgeInsets.only(left: 6),
                            child: Text(
                              '${contentList?.data?.competitionInstructions?.instructions ?? ''}',
                              style: Styles.regular(
                                  size: 14, color: Color(0xff5A5F73)),
                            ),
                          ),
                          SizedBox(
                            height: 20,
                          ),

                          widget.isEvent == true
                              ? SizedBox()
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (contentList?.data
                                            ?.competitionInstructions?.faq !=
                                        null)
                                      Text(
                                        tr('FAQs'),
                                        style: Styles.bold(
                                            size: 14, color: Color(0xff5A5F73)),
                                      ),
                                    SizedBox(
                                      height: 4,
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 6),
                                      child: Text(
                                        '${contentList?.data?.competitionInstructions?.faq ?? ''}',
                                        style: Styles.regular(
                                            size: 14, color: Color(0xff5A5F73)),
                                      ),
                                    ),
                                  ],
                                ),
                        ],
                      ),
                    )
                  ] else
                    ListView.builder(
                        shrinkWrap: true,
                        itemCount: 3,
                        itemBuilder: (BuildContext context, int index) =>
                            Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                height:
                                    MediaQuery.of(context).size.height * 0.1,
                                margin: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 20),
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(6)),
                              ),
                            )),
                ],
              )),
            )));
  }

  Widget competitionCard(CompetitionContent? data, bool isLast,
      {bool? isLocked, bool? isTick}) {
    CardType? cardType;

    switch (data?.contentType) {
      case "video_yts":
        cardType = CardType.youtube;
        break;
      case "video":
        cardType = CardType.video;
        break;
      case "notes":
        cardType = CardType.note;
        break;
      case "assessment":
        cardType = CardType.assessment;

        break;
      case "assignment":
        cardType = CardType.assignment;

        break;
      case "teamsclass":
      case "zoomclass":
      case "otherclass":
        cardType = CardType.session;
        // isLocked = false;
        break;
      case "liveclass":
        cardType = CardType.session;
    }
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start,
          // mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  isTick == true
                      ? Container(
                          padding: EdgeInsets.all(1),
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: ColorConstants.GREEN_1),
                          child: Icon(
                            Icons.done,
                            size: 20,
                            color: ColorConstants.WHITE,
                          ))
                      : SvgPicture.asset(
                          isLocked == true
                              ? 'assets/images/lock_content.svg'
                              : 'assets/images/circular_border.svg',
                          width: 22,
                          height: 22,
                        ),
                  if (!isLast)
                    Container(
                      margin: EdgeInsets.only(top: 4),
                      height: data?.completionPercentage == 100.0 &&
                              data?.overallScore != null &&
                              (cardType == CardType.assignment ||
                                  cardType == CardType.assessment)
                          ? 100
                          : 75,
                      width: 4,
                      decoration: BoxDecoration(
                          color: Color(0xffCECECE),
                          borderRadius: BorderRadius.circular(14)),
                    )
                ],
              ),
            ),
            Container(
              width: MediaQuery.of(context).size.width * 0.86,
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                  color: ColorConstants.WHITE,
                  borderRadius: BorderRadius.circular(10)),
              child: card(data!, cardType, isLocked),
            )
          ]),
    );
  }

  Widget card(CompetitionContent data, CardType? cardType, bool? isLocked) {
    return InkWell(
      onTap: () async {
        if (isLocked == true) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('content_locked').tr(),
          ));
          return;
        }
        if (cardType == CardType.video) {
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: CompetitionVideoPlayer(
                    id: data.id!,
                    videoUrl: data.content!,
                  )));
        }
        if (cardType == CardType.youtube) {
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: CompetitionYoutubePlayer(
                    id: data.id,
                    videoUrl: data.content,
                  )));
        } else if (cardType == CardType.note) {
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: CompetitionNotes(
                    id: data.id,
                    notesUrl: data.content,
                    title: data.title,
                  )));
        } else if (cardType == CardType.assignment)
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: ChangeNotifierProvider<AssignmentDetailProvider>(
                      create: (c) => AssignmentDetailProvider(
                          TrainingService(ApiService()), data,
                          fromCompletiton: true, id: data.programContentId),
                      child: AssignmentDetailPage(
                        id: data.id,
                        fromCompetition: true,
                        difficultyLevel: '${data.difficultyLevel?.capital()}',
                      ))));
        else if (cardType == CardType.assessment) {
          /*Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: ChangeNotifierProvider<AssessmentDetailProvider>(
                      create: (context) => AssessmentDetailProvider(
                          TrainingService(ApiService()), data,
                          fromCompletiton: true,
                          id: data.programContentId,
                          programId: data.programId),
                      child: AssessmentDetailPage(fromCompetition: true, isEvent: widget.isEvent)))).then((value) => (){
                        print('value111');
                        setState(() {});
          });*/

          Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: ChangeNotifierProvider<AssessmentDetailProvider>(
                      create: (context) => AssessmentDetailProvider(
                          TrainingService(ApiService()), data,
                          fromCompletiton: true,
                          id: data.programContentId,
                          programId: data.programId
                      ),
                      child: AssessmentDetailPage(fromCompetition: true, isEvent: widget.isEvent)
                  )
              )
          ).then((value) {
            if (value == 1) {// This will log when back button is pressed
              getCompetitionContentList();
              setState(() {});  // Update the state if necessary
            }
          });

        } else if (cardType == CardType.session) {
          await Navigator.push(
              context,
              PageTransition(
                  duration: Duration(milliseconds: 300),
                  reverseDuration: Duration(milliseconds: 300),
                  type: PageTransitionType.bottomToTop,
                  child: CompetitionSession(
                    data: data,
                  )));
        }
        getCompetitionContentList();
      },
      child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${data.contentTypeLabel ?? ''}',
                style: Styles.regular(size: 12, color: ColorConstants.GREY_3)),
            if (cardType != CardType.session) ...[
              SizedBox(height: 8),
              Text('${data.title}', style: Styles.bold(size: 12)),
            ],
            SizedBox(height: 8),
            if (cardType == CardType.session)
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.network('${data.baseFileUrl}/${data.presenterImage}',
                      height: height(context) * 0.06,
                      width: height(context) * 0.06,
                      errorBuilder: (_, __, ___) {
                    return SizedBox();
                  }),
                  SizedBox(
                    width: 8,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Utility().decrypted128('${data.presenter}'),
                        style: Styles.bold(size: 14),
                      ),
                      Text(
                        '${data.title}',
                        style:
                            Styles.regular(size: 10, color: Color(0xff5A5F73)),
                      ),
                    ],
                  )
                ],
              ),
            // SizedBox(
            //   height: 8,
            // ),
            Row(
              children: [
                Text(
                    cardType == CardType.note
                        ? '${data.pageCount ?? ''} ${tr('pages')}'
                        : cardType == CardType.video ||
                                cardType == CardType.youtube
                            ? '${data.duration ?? data.expectedDuration} ${tr('mins')}'
                            : data.difficultyLevel?.capital() == 'Null'
                                ? ''
                                : '${data.difficultyLevel?.capital()}',
                    style: Styles.regular(
                        color: ColorConstants.GREEN_1, size: 12)),
                SizedBox(
                  width: 4,
                ),
                if (data.gScore != null) ...[
                  Row(children: [
                    Text('•',
                        style: Styles.regular(
                            color: ColorConstants.GREY_2, size: 12)),
                    SizedBox(
                      width: 4,
                    ),
                    SizedBox(
                        height: 15,
                        child: Image.asset('assets/images/coin.png')),
                    SizedBox(
                      width: 4,
                    ),
                    Text('${data.gScore ?? 0} ${tr('points')}',
                        style: Styles.regular(
                            color: ColorConstants.ORANGE_4, size: 12)),
                  ])
                ],

                // data.gScore == '0'
                //     ? SizedBox()
                //     : Text('•',
                //         style: Styles.regular(
                //             color: ColorConstants.GREY_2, size: 12)),
                // data.gScore == '0'
                //     ? SizedBox()
                //     : SizedBox(
                //         width: 4,
                //       ),
                // data.gScore == '0'
                //     ? SizedBox()
                //     : SizedBox(
                //         height: 15,
                //         child: Image.asset('assets/images/coin.png')),
                // data.gScore == '0'
                //     ? SizedBox()
                //     : SizedBox(
                //         width: 4,
                //       ),
                // data.gScore == '0'
                //     ? SizedBox()
                //     : Text('${data.gScore ?? 0} ${tr('points')}',
                //         style: Styles.regular(
                //             color: ColorConstants.ORANGE_4, size: 12)),
                // SizedBox(
                //   width: 4,
                // ),
                /*Text('•',
                    style:
                        Styles.regular(color: ColorConstants.GREY_2, size: 12)),*/
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 10.0, bottom: 8.0),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_month,
                    size: 18,
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  /*data.startDate != null
                      ? StrToTime(
                          time: data.startDate!,
                          dateFormat: 'dd MMM yy',
                          appendString: '',
                          textStyle: Styles.regular(
                            size: 12,
                            color: Color(0xff5A5F73),
                          ),
                        )
                      : SizedBox(),*/

                  data.startDate != null
                      ? StrToTime(
                          time: data.startDate!,
                          dateFormat: 'dd MMM yy',
                          appendString: '',
                          textStyle: Styles.regular(
                            size: 12,
                            color: Color(0xff5A5F73),
                          ),
                        )
                      : SizedBox(),
                  data.startDate != null ? Text(',') : SizedBox(),
                  data.startDate != null
                      ? StrToTime(
                    time: data.startDate!,
                    dateFormat: ' hh:mm a ',
                    //appendString: Utility().isRTL(context) ? '' : data.endDate != null ? tr('to') : '',
                    appendString: '',
                    textStyle: Styles.regular(
                        size: 12,
                        lineHeight: 1,
                        color: ColorConstants.BODY_TEXT),
                  )
                      : SizedBox(),

                  data.endDate != null ? Text(' - ',
                      style: Styles.bold(
                          color: ColorConstants.GREY_2, size: 12)):SizedBox(),

                  /*data.startDate != null
                      ? StrToTime(
                          time: data.startDate!,
                          dateFormat: ' hh:mm a ',
                          appendString: Utility().isRTL(context)
                              ? ''
                              : data.endDate != null
                                  ? tr('to')
                                  : '',
                          textStyle: Styles.regular(
                              size: 12,
                              lineHeight: 1,
                              color: ColorConstants.BODY_TEXT),
                        )
                      : SizedBox(),*/

                  data.endDate != null
                      ? StrToTime(
                    time: data.endDate!,
                    dateFormat: 'dd MMM yy',
                    appendString: '',
                    textStyle: Styles.regular(
                      size: 12,
                      color: Color(0xff5A5F73),
                    ),
                  )
                      : SizedBox(),
                  data.endDate != null ? Text(',') : SizedBox(),
                  data.endDate != null
                      ? StrToTime(
                          time: data.endDate!,
                          dateFormat: ' hh:mm aa ',
                          appendString:
                              Utility().isRTL(context) ? tr('to') : '',
                          textStyle: Styles.regular(
                              size: 12,
                              lineHeight: 1,
                              color: Color.fromARGB(255, 4, 6, 16)),
                        )
                      : SizedBox(),
                ],
              ),
            ),
            if (data.isGraded == 1 &&
                data.completionPercentage == 100.0 &&
                data.overallScore != null &&
                (cardType == CardType.assignment ||
                    cardType == CardType.assessment))
              Divider(),
            if (data.isGraded == 1 &&
                data.completionPercentage == 100.0 &&
                data.overallScore != null &&
                (cardType == CardType.assignment ||
                    cardType == CardType.assessment))
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                        text: '${tr('report')}: ',
                        style: Styles.regular(size: 12)),
                    TextSpan(
                        text: cardType == CardType.assignment ||
                                cardType == CardType.assessment
                            ? '${data.overallScore}'
                            : '${data.completionPercentage}',
                        style: Styles.bold(
                            size: 12, color: ColorConstants().gradientRight())),
                    TextSpan(
                      text: cardType == CardType.assignment
                          ? ' / ${data.marks} ${tr('score')}'
                          : ' / ${data.maximumMarks} ${tr('score')}',
                      style: Styles.bold(size: 12),
                    )
                  ],
                ),
              ),
          ]),
    );
  }

  void handlecompetitionDetailResponse(CompetitionDetailState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          competitionDetailLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Competition Detail State....................");
          competitionDetail = state.response;
          competitionDetailLoading = false;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Competition Detail IDState ..........................${competitionState.error}");
          competitionDetailLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleTrainingDetailState(TrainingDetailState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          competitionDetailLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "Training Competition State value .................... ${state.response?.data?.list?.first.toJson()}");
          data = state.response?.data?.list;
          programDetail = state.response;

          competition = Competition(
            name: programDetail?.data?.list?.first.name,
            image: programDetail?.data?.list?.first.image,
            organizedBy: programDetail?.data?.list?.first.organizedBy,
            competitionLevel: programDetail?.data?.list?.first.competitionLevel,
            //gScore: programDetail?.data?.list?.first.score,
            gScore: programDetail?.data?.list?.first.gscore,
            startDate: programDetail?.data?.list?.first.startDate.toString(),
            endDate: programDetail?.data?.list?.first.endDate.toString(),
            description: programDetail?.data?.list?.first.description,
          );

          Log.v(
              "Training Competition State.................... ${competition}");
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Training Competition  ..........................${competitionState.error}");
          competitionDetailLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleCompetitionListState(AppJobListCompeState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Competition....................");
          competitionDetailLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Competition Content List State....................");
          contentList = competitionState.response;
          competitionDetailLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Competition Content ..........................${competitionState.response?.error}");
          competitionDetailLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  List<String> listOfMonths = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + this.substring(1);
  }
}
