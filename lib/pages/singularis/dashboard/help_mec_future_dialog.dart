
import 'package:flutter/material.dart';

class HelpMecFutureDialog extends StatelessWidget {
  final String? title;
  final String? content;
  final List<Widget> actions;

  HelpMecFutureDialog({
    this.title,
    this.content,
    this.actions = const [],
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        this.title!,
        style: Theme.of(context).textTheme.titleMedium,
      ),
      actions: this.actions,
      content: Text('${this.content}',
        //style: Theme.of(context).textTheme.bodyText1,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }
}