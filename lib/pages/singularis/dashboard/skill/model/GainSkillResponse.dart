class GainSkillMatchingResponse {
  final int status;
  final GainSkillMatchingData data;

  GainSkillMatchingResponse({
    required this.status,
    required this.data,
  });

  factory GainSkillMatchingResponse.fromJson(Map<String, dynamic> json) {
    return GainSkillMatchingResponse(
      status: json['status'] ?? 0,
      data: GainSkillMatchingData.fromJson(json['data'] as Map<String, dynamic>),
    );
  }
}

class GainSkillMatchingData {
  final List<GainSkillMatchingProgram> gainskillMatchingPrograms;
  final List<GainSkillMatchingComp> gainskillMatchingComp;
  final List<GainSkillMatchingJobs> gainskillMatchingJobs;
  final List<GainSkillMatchingInternships> gainskillMatchingInternships;
  final List<Mentorship> gainskillMentorship; // This is an empty list in the JSON response
  final SkillDetail skillDetail;

  GainSkillMatchingData({
    required this.gainskillMatchingPrograms,
    required this.gainskillMatchingComp,
    required this.gainskillMatchingJobs,
    required this.gainskillMatchingInternships,
    required this.gainskillMentorship,
    required this.skillDetail,
  });

  factory GainSkillMatchingData.fromJson(Map<String, dynamic> json) {
    return GainSkillMatchingData(
      gainskillMatchingPrograms: (json['gainskill_matching_programs'] as List<dynamic>?)
          ?.map((program) => GainSkillMatchingProgram.fromJson(program as Map<String, dynamic>))
          .toList() ?? [],
      gainskillMatchingComp: (json['gainskill_matching_comp'] as List<dynamic>?)
          ?.map((comp) => GainSkillMatchingComp.fromJson(comp as Map<String, dynamic>))
          .toList() ?? [],
      gainskillMatchingJobs: (json['gainskill_matching_jobs'] as List<dynamic>?)
          ?.map((job) => GainSkillMatchingJobs.fromJson(job as Map<String, dynamic>))
          .toList() ?? [],
      gainskillMatchingInternships: (json['gainskill_matching_internships'] as List<dynamic>?)
          ?.map((job) => GainSkillMatchingInternships.fromJson(job as Map<String, dynamic>))
          .toList() ?? [],
      gainskillMentorship: (json['gainskill_mentorship'] as List<dynamic>?)
          ?.map((job) => Mentorship.fromJson(job as Map<String, dynamic>))
          .toList() ?? [],

      skillDetail: json['skill_detail'] != null && json['skill_detail'] is Map<String, dynamic>
          ? SkillDetail.fromJson(json['skill_detail'] as Map<String, dynamic>)
          : SkillDetail(name: '', description: '', id: 0, organizationId: 0, status: '', createdAt: '', updatedAt: '', parentId: 0),
    );
  }
}

class GainSkillMatchingProgram {
  final int? id;
  final int? parentId;
  final int? categoryId;
  final int? sessionId;
  final String? level;
  final String? name;
  final String? description;
  final String? image;
  final String? startDate;
  final String? endDate;
  final int? duration;
  final int? createdBy;
  final String? status;
  final String? createdAt;
  final String? updatedAt;
  final int? organizationId;
  final int? isGlobalProgram;
  final int? registrationNeedApproval;
  final int? assignedRuleId;
  final int? weightage;
  final int? certificateId;
  final String? certificateNumberPattern;
  final int? certificateLatestNumber;
  final String? type;
  final int? shortCode;
  final int? gScore;
  final String? subscriptionType;
  final int? isStructured;
  final int? isCompetition;
  final int? terminationDays;
  final String? organizedBy;
  final String? competitionLevel;
  final int? isPopular;
  final int? isPublished;
  final int? isJob;
  final int? isRecommended;
  final int? stepNo;
  final int? isInternship;
  final int? organizedById;
  final int? sisRefModuleId;
  final int? languageId;
  final int? sisModuleId;
  final int? contentApproval;
  final int? departmentId;
  final String? contentApprovalRule;
  final int? emailTemplateId;
  final int? order;
  final int? matchingSkillCount;
  final String? qualifications;
  final String? workMode;
  final int? skillCatCnt;
  final int? pgScore;
  final String? orgName;

  GainSkillMatchingProgram({
    this.id,
    this.parentId,
    this.categoryId,
    this.sessionId,
    this.level,
    this.name,
    this.description,
    this.image,
    this.startDate,
    this.endDate,
    this.duration,
    this.createdBy,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.organizationId,
    this.isGlobalProgram,
    this.registrationNeedApproval,
    this.assignedRuleId,
    this.weightage,
    this.certificateId,
    this.certificateNumberPattern,
    this.certificateLatestNumber,
    this.type,
    this.shortCode,
    this.gScore,
    this.subscriptionType,
    this.isStructured,
    this.isCompetition,
    this.terminationDays,
    this.organizedBy,
    this.competitionLevel,
    this.isPopular,
    this.isPublished,
    this.isJob,
    this.isRecommended,
    this.stepNo,
    this.isInternship,
    this.organizedById,
    this.sisRefModuleId,
    this.languageId,
    this.sisModuleId,
    this.contentApproval,
    this.departmentId,
    this.contentApprovalRule,
    this.emailTemplateId,
    this.order,
    this.matchingSkillCount,
    this.qualifications,
    this.workMode,
    this.skillCatCnt,
    this.pgScore,
    this.orgName,
  });

  factory GainSkillMatchingProgram.fromJson(Map<String, dynamic> json) {
    return GainSkillMatchingProgram(
      id: json['id'] ?? 0,
      parentId: json['parent_id'],
      categoryId: json['category_id'],
      sessionId: json['session_id'],
      level: json['level'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      image: json['image'],
      startDate: json['start_date'],
      endDate: json['end_date'],
      duration: json['duration'],
      createdBy: json['created_by'] ?? 0,
      status: json['status'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      organizationId: json['organization_id'] ?? 0,
      isGlobalProgram: json['is_global_program'] ?? 0,
      registrationNeedApproval: json['registration_need_approval'] ?? 0,
      assignedRuleId: json['assigned_rule_id'],
      weightage: json['weightage'],
      certificateId: json['certificate_id'] ?? 0,
      certificateNumberPattern: json['certificate_number_pattern'] ?? '',
      certificateLatestNumber: json['certificate_latest_number'] ?? 0,
      type: json['type'],
      shortCode: json['short_code'],
      gScore: json['g_score'],
      subscriptionType: json['subscription_type'] ?? '',
      isStructured: json['is_structured'],
      isCompetition: json['is_competition'],
      terminationDays: json['termination_days'],
      organizedBy: json['organized_by'],
      competitionLevel: json['competition_level'],
      isPopular: json['is_popular'] ?? 0,
      isPublished: json['is_published'] ?? 0,
      isJob: json['is_job'],
      isRecommended: json['is_recommended'],
      stepNo: json['step_no'] ?? 0,
      isInternship: json['is_internship'],
      organizedById: json['organized_by_id'],
      sisRefModuleId: json['sis_ref_module_id'],
      languageId: json['language_id'],
      sisModuleId: json['sis_module_id'],
      contentApproval: json['content_approval'] ?? 0,
      departmentId: json['department_id'],
      contentApprovalRule: json['content_approval_rule'] ?? '',
      emailTemplateId: json['email_template_id'],
      order: json['order'],
      matchingSkillCount: json['matching_skill_count'] ?? 0,
      qualifications: json['qualifications'] ?? '',
      workMode: json['work_mode'],
      skillCatCnt: json['skill_cat_cnt'] ?? 0,
      pgScore: json['pg_score'],
      orgName: json['org_name'] ?? '',
    );
  }
}

class GainSkillMatchingComp {
  final int id;
  final int? parentId;
  final int? categoryId;
  final int? sessionId;
  final String level;
  final String name;
  final String description;
  final String? image;
  final String? startDate;
  final String? endDate;
  final int? duration;
  final int createdBy;
  final String status;
  final String createdAt;
  final String updatedAt;
  final int organizationId;
  final int isGlobalProgram;
  final int registrationNeedApproval;
  final int? assignedRuleId;
  final int? weightage;
  final int certificateId;
  final String certificateNumberPattern;
  final int certificateLatestNumber;
  final String? type;
  final String? shortCode;
  final int? gScore;
  final String? subscriptionType;
  final int? isStructured;
  final int? isCompetition;
  final int? terminationDays;
  final String? organizedBy;
  final String? competitionLevel;
  final int isPopular;
  final int isPublished;
  final int? isJob;
  final int? isRecommended;
  final int stepNo;
  final int? isInternship;
  final int? organizedById;
  final int? sisRefModuleId;
  final int? languageId;
  final int? sisModuleId;
  final int contentApproval;
  final int? departmentId;
  final String? contentApprovalRule;
  final int? emailTemplateId;
  final int? order;
  final int matchingSkillCount;
  final String? qualifications;
  final String? workMode;
  final int skillCatCnt;
  final int? pgScore;
  final String orgName;

  GainSkillMatchingComp({
    required this.id,
    this.parentId,
    this.categoryId,
    this.sessionId,
    required this.level,
    required this.name,
    required this.description,
    this.image,
    this.startDate,
    this.endDate,
    this.duration,
    required this.createdBy,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.organizationId,
    required this.isGlobalProgram,
    required this.registrationNeedApproval,
    this.assignedRuleId,
    this.weightage,
    required this.certificateId,
    required this.certificateNumberPattern,
    required this.certificateLatestNumber,
    this.type,
    this.shortCode,
    this.gScore,
    this.subscriptionType,
    this.isStructured,
    this.isCompetition,
    this.terminationDays,
    this.organizedBy,
    this.competitionLevel,
    required this.isPopular,
    required this.isPublished,
    this.isJob,
    this.isRecommended,
    required this.stepNo,
    this.isInternship,
    this.organizedById,
    this.sisRefModuleId,
    this.languageId,
    this.sisModuleId,
    required this.contentApproval,
    this.departmentId,
    this.contentApprovalRule,
    this.emailTemplateId,
    this.order,
    required this.matchingSkillCount,
    this.qualifications,
    this.workMode,
    required this.skillCatCnt,
    this.pgScore,
    required this.orgName,
  });

  factory GainSkillMatchingComp.fromJson(Map<String, dynamic> json) {
    return GainSkillMatchingComp(
      id: json['id'] ?? 0,
      parentId: json['parent_id'],
      categoryId: json['category_id'],
      sessionId: json['session_id'],
      level: json['level'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      image: json['image'],
      startDate: json['start_date'] ?? '',
      endDate: json['end_date'] ?? '',
      duration: json['duration'],
      createdBy: json['created_by'] ?? 0,
      status: json['status'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      organizationId: json['organization_id'] ?? 0,
      isGlobalProgram: json['is_global_program'] ?? 0,
      registrationNeedApproval: json['registration_need_approval'] ?? 0,
      assignedRuleId: json['assigned_rule_id'],
      weightage: json['weightage'],
      certificateId: json['certificate_id'] ?? 0,
      certificateNumberPattern: json['certificate_number_pattern'] ?? '',
      certificateLatestNumber: json['certificate_latest_number'] ?? 0,
      type: json['type'] ?? '',
      shortCode: json['short_code'],
      gScore: json['g_score'],
      subscriptionType: json['subscription_type'],
      isStructured: json['is_structured'],
      isCompetition: json['is_competition'],
      terminationDays: json['termination_days'],
      organizedBy: json['organized_by'] ?? '',
      competitionLevel: json['competition_level'] ?? '',
      isPopular: json['is_popular'] ?? 0,
      isPublished: json['is_published'] ?? 0,
      isJob: json['is_job'],
      isRecommended: json['is_recommended'],
      stepNo: json['step_no'] ?? 0,
      isInternship: json['is_internship'],
      organizedById: json['organized_by_id'],
      sisRefModuleId: json['sis_ref_module_id'],
      languageId: json['language_id'],
      sisModuleId: json['sis_module_id'],
      contentApproval: json['content_approval'] ?? 0,
      departmentId: json['department_id'],
      contentApprovalRule: json['content_approval_rule'] ?? '',
      emailTemplateId: json['email_template_id'],
      order: json['order'],
      matchingSkillCount: json['matching_skill_count'] ?? 0,
      qualifications: json['qualifications'] ?? '',
      workMode: json['work_mode'] ?? '',
      skillCatCnt: json['skill_cat_cnt'] ?? 0,
      pgScore: json['pg_score'],
      orgName: json['org_name'] ?? '',
    );
  }
}

class GainSkillMatchingJobs {
  final int? id;
  final int? parentId;
  final int? categoryId;
  final int? sessionId;
  final String? level;
  final String? name;
  final String? description;
  final String? image;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? duration;
  final int? createdBy;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? organizationId;
  final bool? isGlobalProgram;
  final bool? registrationNeedApproval;
  final int? assignedRuleId;
  final int? weightage;
  final int? certificateId;
  final String? certificateNumberPattern;
  final int? certificateLatestNumber;
  final String? type;
  final int? shortCode;
  final int? gScore;
  final String? subscriptionType;
  final bool? isStructured;
  final bool? isCompetition;
  final int? terminationDays;
  final String? organizedBy;
  final String? competitionLevel;
  final bool? isPopular;
  final bool? isPublished;
  final bool? isJob;
  final bool? isRecommended;
  final int? stepNo;
  final bool? isInternship;
  final int? organizedById;
  final int? sisRefModuleId;
  final int? languageId;
  final int? sisModuleId;
  final bool? contentApproval;
  final int? departmentId;
  final String? contentApprovalRule;
  final int? emailTemplateId;
  final int? order;
  final int? matchingSkillCount;
  final String? qualifications;
  final String? workMode;
  final int? skillCatCnt;
  final int? pgScore;
  final String? orgName;
  final int? jobId;
  final String? compType;
  final String? landingPageUrl;
  final int? domainId;
  final String? nameAr;
  final String? descriptionAr;
  final String? whatsInAr;
  final String? instructionsAr;
  final int? numOfVacancy;
  final String? domainName;
  final String? location;
  final String? experience;
  final double? minExperience;
  final double? maxExperience;
  final String? workAddress;
  final String? skillNames;
  final String? jobStatus;
  final int? jobStatusNumeric;

  GainSkillMatchingJobs({this.id,
    this.parentId,
    this.categoryId,
    this.sessionId,
    this.level,
    this.name,
    this.description,
    this.image,
    this.startDate,
    this.endDate,
    this.duration,
    this.createdBy,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.organizationId,
    this.isGlobalProgram,
    this.registrationNeedApproval,
    this.assignedRuleId,
    this.weightage,
    this.certificateId,
    this.certificateNumberPattern,
    this.certificateLatestNumber,
    this.type,
    this.shortCode,
    this.gScore,
    this.subscriptionType,
    this.isStructured,
    this.isCompetition,
    this.terminationDays,
    this.organizedBy,
    this.competitionLevel,
    this.isPopular,
    this.isPublished,
    this.isJob,
    this.isRecommended,
    this.stepNo,
    this.isInternship,
    this.organizedById,
    this.sisRefModuleId,
    this.languageId,
    this.sisModuleId,
    this.contentApproval,
    this.departmentId,
    this.contentApprovalRule,
    this.emailTemplateId,
    this.order,
    this.matchingSkillCount,
    this.qualifications,
    this.workMode,
    this.skillCatCnt,
    this.pgScore,
    this.orgName,
    this.jobId,
    this.compType,
    this.landingPageUrl,
    this.domainId,
    this.nameAr,
    this.descriptionAr,
    this.whatsInAr,
    this.instructionsAr,
    this.numOfVacancy,
    this.domainName,
    this.location,
    this.experience,
    this.minExperience,
    this.maxExperience,
    this.workAddress,
    this.skillNames,
    this.jobStatus,
    this.jobStatusNumeric,
  });

  factory GainSkillMatchingJobs.fromJson(Map<String, dynamic> json) {
    return GainSkillMatchingJobs(
      id: json['id'],
      parentId: json['parent_id'],
      categoryId: json['category_id'],
      sessionId: json['session_id'],
      level: json['level'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      image: json['image'] ?? '',
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      duration: json['duration'],
      createdBy: json['created_by'],
      status: json['status'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      organizationId: json['organization_id'],
      isGlobalProgram: json['is_global_program'] == 1,
      registrationNeedApproval: json['registration_need_approval'] == 1,
      assignedRuleId: json['assigned_rule_id'],
      weightage: json['weightage'],
      certificateId: json['certificate_id'],
      certificateNumberPattern: json['certificate_number_pattern'] ?? '',
      certificateLatestNumber: json['certificate_latest_number'],
      type: json['type'] ?? '',
      shortCode: json['short_code'],
      gScore: json['g_score'],
      subscriptionType: json['subscription_type'] ?? '',
      isStructured: json['is_structured'] == 1,
      isCompetition: json['is_competition'] == 1,
      terminationDays: json['termination_days'],
      organizedBy: json['organized_by'] ?? '',
      competitionLevel: json['competition_level'] ?? '',
      isPopular: json['is_popular'] == 1,
      isPublished: json['is_published'] == 1,
      isJob: json['is_job'] == 1,
      isRecommended: json['is_recommended'],
      stepNo: json['step_no'],
      isInternship: json['is_internship'],
      organizedById: json['organized_by_id'],
      sisRefModuleId: json['sis_ref_module_id'],
      languageId: json['language_id'],
      sisModuleId: json['sis_module_id'],
      contentApproval: json['content_approval'] == 1,
      departmentId: json['department_id'],
      contentApprovalRule: json['content_approval_rule'] ?? '',
      emailTemplateId: json['email_template_id'],
      order: json['order'],
      matchingSkillCount: json['matching_skill_count'],
      qualifications: json['qualifications'] ?? '',
      workMode: json['work_mode'] ?? '',
      skillCatCnt: json['skill_cat_cnt'],
      pgScore: json['pg_score'],
      orgName: json['org_name'],
      jobId: json['job_id'],
      compType: json['comp_type'] ?? '',
      landingPageUrl: json['landing_page_url'] ?? '',
      domainId: json['domain_id'],
      nameAr: json['name_ar'],
      descriptionAr: json['description_ar'] ?? '',
      whatsInAr: json['whats_in_ar'] ?? '',
      instructionsAr: json['instructions_ar'] ?? '',
      numOfVacancy: json['num_of_vacancy'],
      domainName: json['domain_name'] ?? '',
      location: json['location'] ?? '',
      experience: json['experience'] ?? '',
      minExperience: json['min_experience'],
      maxExperience: json['max_experience'],
      workAddress: json['work_address'] ?? '',
      skillNames: json['skill_names'] ?? '',
      jobStatus: json['job_status'] ?? '',
      jobStatusNumeric: json['job_status_numeric'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parent_id': parentId,
      'category_id': categoryId,
      'session_id': sessionId,
      'level': level,
      'name': name,
      'description': description,
      'image': image,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'duration': duration,
      'created_by': createdBy,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'organization_id': organizationId,
      'is_global_program': isGlobalProgram ?? 0,
      'registration_need_approval': registrationNeedApproval ??0,
      'assigned_rule_id': assignedRuleId,
      'weightage': weightage,
      'certificate_id': certificateId,
      'certificate_number_pattern': certificateNumberPattern,
      'certificate_latest_number': certificateLatestNumber,
      'type': type,
      'short_code': shortCode,
      'g_score': gScore,
      'subscription_type': subscriptionType,
      'is_structured': isStructured == true ? 1 : 0,
      'is_competition': isCompetition == true ? 1 : 0,
      'termination_days': terminationDays,
      'organized_by': organizedBy,
      'competition_level': competitionLevel,
      'is_popular': isPopular,
      'is_published': isPublished ,
      'is_job': isJob ?? 0,
      'is_recommended': isRecommended,
      'step_no': stepNo,
      'is_internship': isInternship == true ? 1 : 0,
      'organized_by_id': organizedById,
      'sis_ref_module_id': sisRefModuleId,
      'language_id': languageId,
      'sis_module_id': sisModuleId,
      'content_approval': contentApproval ?? 0,
      'department_id': departmentId,
      'content_approval_rule': contentApprovalRule,
      'email_template_id': emailTemplateId,
      'order': order,
      'matching_skill_count': matchingSkillCount,
      'qualifications': qualifications,
      'work_mode': workMode,
      'skill_cat_cnt': skillCatCnt,
      'pg_score': pgScore,
      'org_name': orgName,
      'job_id': jobId,
      'comp_type': compType,
      'landing_page_url': landingPageUrl,
      'domain_id': domainId,
      'name_ar': nameAr,
      'description_ar': descriptionAr,
      'whats_in_ar': whatsInAr,
      'instructions_ar': instructionsAr,
      'num_of_vacancy': numOfVacancy,
      'domain_name': domainName,
      'location': location,
      'experience': experience,
      'min_experience': minExperience,
      'max_experience': maxExperience,
      'work_address': workAddress,
      'skill_names': skillNames,
      'job_status': jobStatus,
      'job_status_numeric': jobStatusNumeric,
    };
  }
}


class GainSkillMatchingInternships {
  final int id;
  final int? parentId;
  final int? categoryId;
  final int? sessionId;
  final String level;
  final String name;
  final String description;
  final String image;
  final DateTime startDate;
  final DateTime endDate;
  final int? duration;
  final int createdBy;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int organizationId;
  final bool isGlobalProgram;
  final bool registrationNeedApproval;
  final int? assignedRuleId;
  final int? weightage;
  final int? certificateId;
  final String certificateNumberPattern;
  final int certificateLatestNumber;
  final String? type;
  final String? shortCode;
  final String? gScore;
  final String? subscriptionType;
  final bool? isStructured;
  final bool? isCompetition;
  final int? terminationDays;
  final String organizedBy;
  final String? competitionLevel;
  final bool isPopular;
  final bool isPublished;
  final bool? isJob;
  final bool? isRecommended;
  final int stepNo;
  final bool isInternship;
  final int organizedById;
  final int? sisRefModuleId;
  final int? languageId;
  final int? sisModuleId;
  final bool contentApproval;
  final int? departmentId;
  final String? contentApprovalRule;
  final int? emailTemplateId;
  final int? order;
  final int matchingSkillCount;
  final String qualifications;
  final String workMode;
  final int skillCatCnt;
  final String? pgScore;
  final String orgName;

  GainSkillMatchingInternships({
    required this.id,
    this.parentId,
    this.categoryId,
    this.sessionId,
    required this.level,
    required this.name,
    required this.description,
    required this.image,
    required this.startDate,
    required this.endDate,
    this.duration,
    required this.createdBy,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.organizationId,
    required this.isGlobalProgram,
    required this.registrationNeedApproval,
    this.assignedRuleId,
    this.weightage,
    this.certificateId,
    required this.certificateNumberPattern,
    required this.certificateLatestNumber,
    this.type,
    this.shortCode,
    this.gScore,
    this.subscriptionType,
    this.isStructured,
    this.isCompetition,
    this.terminationDays,
    required this.organizedBy,
    this.competitionLevel,
    required this.isPopular,
    required this.isPublished,
    this.isJob,
    this.isRecommended,
    required this.stepNo,
    required this.isInternship,
    required this.organizedById,
    this.sisRefModuleId,
    this.languageId,
    this.sisModuleId,
    required this.contentApproval,
    this.departmentId,
    this.contentApprovalRule,
    this.emailTemplateId,
    this.order,
    required this.matchingSkillCount,
    required this.qualifications,
    required this.workMode,
    required this.skillCatCnt,
    this.pgScore,
    required this.orgName,
  });

  factory GainSkillMatchingInternships.fromJson(Map<String, dynamic> json) {
    return GainSkillMatchingInternships(
      id: json['id'],
      parentId: json['parent_id'],
      categoryId: json['category_id'],
      sessionId: json['session_id'],
      level: json['level'],
      name: json['name'],
      description: json['description'],
      image: json['image'],
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      duration: json['duration'],
      createdBy: json['created_by'],
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      organizationId: json['organization_id'],
      isGlobalProgram: json['is_global_program'] == 1,
      registrationNeedApproval: json['registration_need_approval'] == 1,
      assignedRuleId: json['assigned_rule_id'],
      weightage: json['weightage'],
      certificateId: json['certificate_id'],
      certificateNumberPattern: json['certificate_number_pattern'],
      certificateLatestNumber: json['certificate_latest_number'],
      type: json['type'] ?? '',
      shortCode: json['short_code'],
      gScore: json['g_score'],
      subscriptionType: json['subscription_type'],
      isStructured: json['is_structured'] == 1,
      isCompetition: json['is_competition'] == 1,
      terminationDays: json['termination_days'],
      organizedBy: json['organized_by'],
      competitionLevel: json['competition_level'],
      isPopular: json['is_popular'] == 1,
      isPublished: json['is_published'] == 1,
      isJob: json['is_job'],
      isRecommended: json['is_recommended'],
      stepNo: json['step_no'],
      isInternship: json['is_internship'] == 1,
      organizedById: json['organized_by_id'],
      sisRefModuleId: json['sis_ref_module_id'],
      languageId: json['language_id'],
      sisModuleId: json['sis_module_id'],
      contentApproval: json['content_approval'] == 1,
      departmentId: json['department_id'],
      contentApprovalRule: json['content_approval_rule'],
      emailTemplateId: json['email_template_id'],
      order: json['order'],
      matchingSkillCount: json['matching_skill_count'],
      qualifications: json['qualifications'],
      workMode: json['work_mode'],
      skillCatCnt: json['skill_cat_cnt'],
      pgScore: json['pg_score'],
      orgName: json['org_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parent_id': parentId,
      'category_id': categoryId,
      'session_id': sessionId,
      'level': level,
      'name': name,
      'description': description,
      'image': image,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'duration': duration,
      'created_by': createdBy,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'organization_id': organizationId,
      'is_global_program': isGlobalProgram ? 1 : 0,
      'registration_need_approval': registrationNeedApproval ? 1 : 0,
      'assigned_rule_id': assignedRuleId,
      'weightage': weightage,
      'certificate_id': certificateId,
      'certificate_number_pattern': certificateNumberPattern,
      'certificate_latest_number': certificateLatestNumber,
      'type': type,
      'short_code': shortCode,
      'g_score': gScore,
      'subscription_type': subscriptionType,
      'is_structured': isStructured == true ? 1 : 0,
      'is_competition': isCompetition == true ? 1 : 0,
      'termination_days': terminationDays,
      'organized_by': organizedBy,
      'competition_level': competitionLevel,
      'is_popular': isPopular ? 1 : 0,
      'is_published': isPublished ? 1 : 0,
      'is_job': isJob,
      'is_recommended': isRecommended,
      'step_no': stepNo,
      'is_internship': isInternship ? 1 : 0,
      'organized_by_id': organizedById,
      'sis_ref_module_id': sisRefModuleId,
      'language_id': languageId,
      'sis_module_id': sisModuleId,
      'content_approval': contentApproval ? 1 : 0,
      'department_id': departmentId,
      'content_approval_rule': contentApprovalRule,
      'email_template_id': emailTemplateId,
      'order': order,
      'matching_skill_count': matchingSkillCount,
      'qualifications': qualifications,
      'work_mode': workMode,
      'skill_cat_cnt': skillCatCnt,
      'pg_score': pgScore,
      'org_name': orgName,
    };
  }
}


class SkillDetail {
  final int id;
  final String name;
  final String description;
  final int organizationId;
  final String status;
  final String createdAt;
  final String updatedAt;
  final int parentId;
  final String? keywords;
  final int? approvalNeeded;

  SkillDetail({
    required this.id,
    required this.name,
    required this.description,
    required this.organizationId,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.parentId,
    this.keywords,
    this.approvalNeeded,
  });

  factory SkillDetail.fromJson(Map<String, dynamic> json) {
    return SkillDetail(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      organizationId: json['organization_id'] ?? 0,
      status: json['status'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      parentId: json['parent_id'] ?? 0,
      keywords: json['keywords'],
      approvalNeeded: json['approval_needed'],
    );
  }
}

class Mentorship {
  final int? id;
  final int? wordpressUserId;
  final String? name;
  final String? email;
  final String? emailVerifiedAt;
  final String? password;
  final String? rememberToken;
  final String? createdAt;
  final String? updatedAt;
  final String? role;
  final String? status;
  final int? organizationId;
  final int? locationId;
  final int? departmentId;
  final int? designationId;
  final int? reportsToId;
  final int? mobileNo;
  final int? pincode;
  final String? permanentAddress;
  final String? dateOfBirth;
  final int? employeeCode;
  final String? profileImage;
  final String? fatherName;
  final String? motherName;
  final int? isDemoUser;
  final String? demoUrl;
  final int? demoStatus;
  final String? functionalArea;
  final String? dateOfJoining;
  final int? designationLevelId;
  final String? username1;
  final String? username;
  final String? description;
  final String? score;
  final String? locale;
  final String? userType;
  final String? empType;
  final String? actualRole;
  final String? mecUserSisRefId;
  final String? mecRegdId;
  final String? ssoToken;
  final String? sisUserActualRole;
  final String? sisUserType;
  final String? loginType;
  final String? tagline;
  final String? ageGroup;
  final String? experience;
  final String? qrCode;
  final String? countryCode;
  final String? locationCountry;
  final String? educationQualification;
  final int? experienceYear;

  Mentorship({
    this.id,
    this.wordpressUserId,
    this.name,
    this.email,
    this.emailVerifiedAt,
    this.password,
    this.rememberToken,
    this.createdAt,
    this.updatedAt,
    this.role,
    this.status,
    this.organizationId,
    this.locationId,
    this.departmentId,
    this.designationId,
    this.reportsToId,
    this.mobileNo,
    this.pincode,
    this.permanentAddress,
    this.dateOfBirth,
    this.employeeCode,
    this.profileImage,
    this.fatherName,
    this.motherName,
    this.isDemoUser,
    this.demoUrl,
    this.demoStatus,
    this.functionalArea,
    this.dateOfJoining,
    this.designationLevelId,
    this.username1,
    this.username,
    this.description,
    this.score,
    this.locale,
    this.userType,
    this.empType,
    this.actualRole,
    this.mecUserSisRefId,
    this.mecRegdId,
    this.ssoToken,
    this.sisUserActualRole,
    this.sisUserType,
    this.loginType,
    this.tagline,
    this.ageGroup,
    this.experience,
    this.qrCode,
    this.countryCode,
    this.locationCountry,
    this.educationQualification,
    this.experienceYear,
  });

  // Factory constructor to create a Mentorship instance from JSON
  factory Mentorship.fromJson(Map<String, dynamic> json) {
    return Mentorship(
      id: json['id'],
      wordpressUserId: json['wordpress_user_id'],
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      emailVerifiedAt: json['email_verified_at'] ?? '',
      password: json['password'] ?? '',
      rememberToken: json['remember_token'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      role: json['role'] ?? '',
      status: json['status'] ?? '',
      organizationId: json['organization_id'],
      locationId: json['location_id'],
      departmentId: json['department_id'],
      designationId: json['designation_id'],
      reportsToId: json['reports_to_id'],
      mobileNo: json['mobile_no'],
      pincode: json['pincode'],
      permanentAddress: json['permanent_address'] ?? '',
      dateOfBirth: json['date_of_birth'] ?? '',
      employeeCode: json['employee_code'],
      profileImage: json['profile_image'] ?? '',
      fatherName: json['father_name'] ?? '',
      motherName: json['mother_name'] ?? '',
      isDemoUser: json['is_demo_user'],
      demoUrl: json['demo_url'] ?? '',
      demoStatus: json['demo_status'],
      functionalArea: json['functional_area'] ?? '',
      dateOfJoining: json['date_of_joining'] ?? '',
      designationLevelId: json['designation_level_id'],
      username1: json['username1'] ?? '',
      username: json['username'] ?? '',
      description: json['description'] ?? '',
      score: json['score'] ?? '',
      locale: json['locale'] ?? '',
      userType: json['user_type'] ?? '',
      empType: json['emp_type'] ?? '',
      actualRole: json['actual_role'] ?? '',
      mecUserSisRefId: json['mec_user_sis_ref_id'] ?? '',
      mecRegdId: json['mec_regd_id'] ?? '',
      ssoToken: json['sso_token'] ?? '',
      sisUserActualRole: json['sis_user_actual_role'] ?? '',
      sisUserType: json['sis_user_type'] ?? '',
      loginType: json['login_type'] ?? '',
      tagline: json['tagline'] ?? '',
      ageGroup: json['age_group'] ?? '',
      experience: json['experience'] ?? '',
      qrCode: json['qr_code'] ?? '',
      countryCode: json['country_code'] ?? '',
      locationCountry: json['location_country'] ?? '',
      educationQualification: json['education_qualification'] ?? '',
      experienceYear: json['experience_year'],
    );
  }

  // Convert a Mentorship instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wordpress_user_id': wordpressUserId,
      'name': name,
      'email': email,
      'email_verified_at': emailVerifiedAt,
      'password': password,
      'remember_token': rememberToken,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'role': role,
      'status': status,
      'organization_id': organizationId,
      'location_id': locationId,
      'department_id': departmentId,
      'designation_id': designationId,
      'reports_to_id': reportsToId,
      'mobile_no': mobileNo,
      'pincode': pincode,
      'permanent_address': permanentAddress,
      'date_of_birth': dateOfBirth,
      'employee_code': employeeCode,
      'profile_image': profileImage,
      'father_name': fatherName,
      'mother_name': motherName,
      'is_demo_user': isDemoUser,
      'demo_url': demoUrl,
      'demo_status': demoStatus,
      'functional_area': functionalArea,
      'date_of_joining': dateOfJoining,
      'designation_level_id': designationLevelId,
      'username1': username1,
      'username': username,
      'description': description,
      'score': score,
      'locale': locale,
      'user_type': userType,
      'emp_type': empType,
      'actual_role': actualRole,
      'mec_user_sis_ref_id': mecUserSisRefId,
      'mec_regd_id': mecRegdId,
      'sso_token': ssoToken,
      'sis_user_actual_role': sisUserActualRole,
      'sis_user_type': sisUserType,
      'login_type': loginType,
      'tagline': tagline,
      'age_group': ageGroup,
      'experience': experience,
      'qr_code': qrCode,
      'country_code': countryCode,
      'location_country': locationCountry,
      'education_qualification': educationQualification,
      'experience_year': experienceYear,
    };
  }
}


class ApiResponse {
  final int status;
  final List<dynamic> error;
  final Map<String, dynamic> data;

  ApiResponse({
    required this.status,
    required this.error,
    required this.data,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json) {
    return ApiResponse(
      status: json['status'] ?? 0,
      error: json['error'] ?? [],
      data: json['data'] ?? {},
    );
  }
}
