
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/ghome/home_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/widget_size.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../data/models/response/auth_response/oraganization_program_resp.dart';
import '../../../../data/models/response/home_response/goal_intrest_area_responce.dart';


class SetGoalIntresetAreaPage extends StatefulWidget {
  final bool? backEnable;
  final bool? moveToHome;
  final bool? singleSelection;
  final bool? returnValue;
  final int? fetchGoalList;

  SetGoalIntresetAreaPage({
    Key? key,
    this.backEnable,
    this.moveToHome = false,
    this.singleSelection = false,
    this.returnValue = false,
    this.fetchGoalList = 1,
  }) : super(key: key);

  @override
  State<SetGoalIntresetAreaPage> createState() =>
      _SetGoalIntresetAreaPageState();
}

class _SetGoalIntresetAreaPageState
    extends State<SetGoalIntresetAreaPage> {
  bool isInterestMapping = false;

  List<String>? interestMapResponse;
  List<int?> selectProgramId = [];
  List<int?> selectProgramParentId = [];
  List<int> selectedPrograms = [];
  bool isUpdating = false;
  List<Menu>? menuList;
  Color foregroundColor = ColorConstants.BLACK;
  int? isParentLanguage =
      Preference.getInt(Preference.IS_PRIMARY_LANGUAGE) ?? 1;

  List<GoalInterestArea>? response;

  List<GoalInterestArea>? displayInterestList = [];
  String? selectInst;
  List<String?> listInterest = <String>[];
  bool isFetchGoalList = false;

  @override
  void initState() {
    super.initState();
    foregroundColor = ColorConstants().primaryForgroundColor();
    _getInterestPrograms();
  }

  void _getInterestPrograms() {
    BlocProvider.of<HomeBloc>(context).add(OrganizationProgramListEvent(widget.fetchGoalList));
  }

  void _mapInterest(param) {
    BlocProvider.of<HomeBloc>(context)
        .add(MapInterestEvent(param: param, mapType: 'set_goal'));
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {},
        child: MultiBlocListener(
            listeners: [
              BlocListener<HomeBloc, HomeState>(
                  listener: (BuildContext context, state) {
                    if (state is MapInterestState)
                      _handleMapInterestResponse(state);
                  }),
              BlocListener<HomeBloc, HomeState>(
                listener: (BuildContext context, state) {
                  if (state is GetBottomBarState) {
                    _handelBottomNavigationBar(state);
                  }
                  if (state is PiDetailState) {
                    handlePiDetail(state);
                  }
                  if (state is OrganizationProgramListState) {
                    setState(() {
                      switch (state.apiState) {
                        case ApiStatus.LOADING:
                          Log.v("Loading....................");
                          break;
                        case ApiStatus.SUCCESS:
                          Log.v(
                              "Success....................interest area list ");
                          response = state.responseSetGoal?.data;
                          displayInterestList = response;

                          isFetchGoalList = true;
                          break;
                        case ApiStatus.ERROR:
                          Log.v("Error..........................${state.error}");

                          break;
                        case ApiStatus.INITIAL:
                          break;
                      }
                    });
                  }
                },
              ),
            ],
            child: Scaffold(
              backgroundColor: ColorConstants.WHITE,
              appBar: AppBar(
                backgroundColor: ColorConstants.WHITE,
                leading: widget.moveToHome == false
                    ? BackButton(color: ColorConstants.BLACK)
                    : SizedBox(),
                title: Text(
                  'select_your_job_goal',
                  style: Styles.semibold(color: ColorConstants.BLACK),
                ).tr(),
                elevation: 0,
                automaticallyImplyLeading:
                widget.backEnable == true ? true : false,
              ),
              body: SafeArea(
                child: ScreenWithLoader(
                  isLoading: isUpdating,
                  body: SingleChildScrollView(
                    child: (displayInterestList!.length != 0 &&
                        displayInterestList?.length != 0)
                        ? Padding(
                      padding: const EdgeInsets.only(
                          left: 20.0,
                          top: 0.0,
                          right: 20.0,
                          bottom: 20.0),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('select_categories',
                                style: Styles.bold())
                                .tr(),
                            SizedBox(height: 2),
                            Text('select_1_catg', style: Styles.regular())
                                .tr(),
                            SizedBox(height: 20),
                            Container(
                              height: MediaQuery.of(context).size.height *
                                  0.69,
                              child: techChips(),
                            ),
                            Visibility(
                              visible: true,
                              child: InkWell(
                                  onTap: () {
                                    var selectedCategoryIds = '';
                                    displayInterestList
                                        ?.forEach((element) {
                                      if ((int.tryParse(
                                          '${element.isMapped}') ??
                                          0) >
                                          0) {
                                        selectedCategoryIds +=
                                            element.id.toString() + ',';
                                      }
                                    });

                                    selectedCategoryIds =
                                        selectedCategoryIds.substring(
                                            0,
                                            selectedCategoryIds.length -
                                                1);
                                    _mapInterest(selectedCategoryIds);

                                    return;

                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        left: 5.0,
                                        top: 10.0,
                                        right: 5.0,
                                        bottom: 10.0),
                                    width: double.infinity,
                                    height: MediaQuery.of(context)
                                        .size
                                        .height *
                                        WidgetSize.AUTH_BUTTON_SIZE,
                                    decoration: BoxDecoration(
                                        gradient: LinearGradient(colors: [
                                          ColorConstants().gradientLeft(),
                                          ColorConstants()
                                              .gradientRight(),
                                        ]),
                                        color: ColorConstants()
                                            .buttonColor(),
                                        borderRadius:
                                        BorderRadius.circular(5)),
                                    child: Center(
                                        child: Text('confirm_interest',
                                            style: Styles.regular(
                                              color: foregroundColor,
                                            )).tr()),
                                  )),
                            ),
                          ]),
                    )
                        : Container(
                      height: MediaQuery.of(context).size.height,
                      child: SingleChildScrollView(
                        child: isFetchGoalList == false ? Wrap(
                            direction: Axis.horizontal,
                            children: shimmerChips.toList())
                            : Container(
                            height: MediaQuery.of(context).size.height,
                            padding: EdgeInsets.only(bottom: 150),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset('assets/images/br_empty.png'),
                                Padding(
                                  padding: const EdgeInsets.only(top: 10.0, left: 50.0, right: 50),
                                  child: Text('choose_not_interest', textAlign: TextAlign.center).tr(),
                                ),
                              ],
                            )
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            )));
  }

  void _handleMapInterestResponse(MapInterestState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isInterestMapping = true;
          isUpdating = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          interestMapResponse = state.response!.data;
          isUpdating = false;
          isInterestMapping = true;
          //Preference.SAVE_INTEREST
          _getInterestPrograms();
          if (widget.moveToHome == true) {
            getBottomNavigationBar();
          } else {
            AlertsWidget.alertWithOkBtn(
              context: context,
              onOkClick: () {
                if(widget.returnValue == false){
                  Navigator.pop(context, true);
                }else{
                  //Navigator.pop(context, selectInst);
                  Navigator.pop(context, listInterest);
                }
              },
              text: "${state.response?.data?.first}",
            );
          }

          break;
        case ApiStatus.ERROR:
          isUpdating = false;
          isInterestMapping = false;
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'select_interest', parameters: {
            "map_interest_error": loginState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          isUpdating = false;
          break;
      }
    });
  }

  void getBottomNavigationBar() {
    BlocProvider.of<HomeBloc>(context).add((GetBottomNavigationBarEvent()));
  }

  void getPiDetail() {
    BlocProvider.of<HomeBloc>(context)
        .add(PiDetailEvent(userId: Preference.getInt(Preference.USER_ID)));
  }

  void handlePiDetail(PiDetailState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PI Detail Loading....................");
          //isPortfolioLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PI Detail Success....................${portfolioState.response?.data?.toJson()}");

          if (portfolioState.response?.data?.name != '' &&
              portfolioState.response?.data?.name != null)
            Preference.setString(Preference.FIRST_NAME,
                '${portfolioState.response?.data?.name}');
          if (portfolioState.response?.data?.email != '' &&
              portfolioState.response?.data?.email != null)
            Preference.setString(Preference.USER_EMAIL,
                '${portfolioState.response?.data?.email}');

          if (portfolioState.response?.data?.mobile != '' &&
              portfolioState.response?.data?.mobile != null)
            Preference.setString(
                Preference.PHONE, '${portfolioState.response?.data?.mobile}');

          setState(() {});
          getBottomNavigationBar(); //hide 18 jun 2024
          break;

        case ApiStatus.ERROR:
          getBottomNavigationBar(); //hide 18 jun 2024
          Log.v(
              "PI Detail Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'select_interest', parameters: {
            "map_pi_interest_error": portfolioState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }


  void _handelBottomNavigationBar(GetBottomBarState state) {
    var getBottomBarState = state;
    setState(() {
      switch (getBottomBarState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading.................... bottom ");
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success.................... bottom");
          Log.v("bottom...${state.response?.data?.menuSide?.length}");
          Log.v("bottom...${state.response?.data?.menuSide?[0].label}");
          Log.v("bottom role...${Preference.getString(Preference.ROLE)?.toLowerCase()}");

          menuList = state.response!.data!.menu;
          menuList =  menuList!.where((element) {
            bool containRole = element.role.toString().toLowerCase().contains(
                '${Preference.getString(Preference.ROLE)?.toLowerCase()}');
            return containRole;
          }).toList();
          if (menuList?.length == 0) {
            AlertsWidget.alertWithOkBtn(
                context: context,
                text:tr('menu_not_found_msg'),
                onOkClick: () {
                  FocusScope.of(context).unfocus();
                });
          } else {
            // menuList?.sort((a, b) => a.inAppOrder!.compareTo(b.inAppOrder!));
            menuList?.sort((a, b) => (int.tryParse('${a.inAppOrder}') ?? 0).compareTo(int.tryParse('${b.inAppOrder}') ?? 0));

            int index = 0;
            for (var item in menuList!) {
              if (item.url == '/g-home') {
                index = menuList!.indexOf(item);
                break;
              }
            }

            Navigator.pushAndRemoveUntil(
                context,
                NextPageRoute(
                    homePage(
                      bottomMenu: menuList,
                      index: index,
                    ),
                    isMaintainState: true),
                    (route) => false);
          }

          break;

        case ApiStatus.ERROR:
          Log.v("Error..........................");
          FirebaseAnalytics.instance
              .logEvent(name: 'select_interest', parameters: {
            "map_interest_bottom_bar_error": getBottomBarState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
        // TODO: Handle this case.
          break;
      }
    });
  }

  Iterable<Widget> get shimmerChips sync* {
    for (int i = 0; i < 15; i++) {
      yield Padding(
        padding: const EdgeInsets.only(top: 20),
        child: Shimmer.fromColors(
          baseColor: Color(0xffe6e4e6),
          highlightColor: Color(0xffeaf0f3),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 10.0),
                child: Container(
                  height: width(context) * 0.4,
                  width: width(context) * 0.4,
                  decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 10.0),
                child: Container(
                  height: 10,
                  width: width(context) * 0.25,
                  margin: const EdgeInsets.only(top: 10),
                  decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget techChips() {
    return GridView.builder(
        shrinkWrap: true,
        physics: ScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          mainAxisSpacing: 4,
          crossAxisSpacing: 2,
          childAspectRatio: 1,
          crossAxisCount: 2,
        ),
        itemCount: displayInterestList?.length,
        itemBuilder: (context, i) {
          return InkWell(
            onTap: () {
              Log.v('map is ${displayInterestList?[i].isMapped}');

              if(widget.singleSelection  == false) {
                setState(() {
                  if ((int.tryParse('${displayInterestList![i].isMapped}') ??
                      0) > 0) {
                    displayInterestList![i].isMapped = 0;
                  } else {
                    displayInterestList![i].isMapped = 1;
                  }
                  setState(() {
                    if (selectProgramId.contains(displayInterestList![i].id)) {
                      selectProgramId.remove(displayInterestList![i].id);
                    } else {
                      selectProgramId.add(displayInterestList![i].id);
                    }
                  });

                  listInterest.clear();
                  listInterest.add(displayInterestList![i].jobRole);
                  listInterest.add(displayInterestList![i].id.toString());
                  //selectInst = displayInterestList![i].interestarea;
                });

              }else{
                setState(() {
                  if ((int.tryParse('${displayInterestList![i].isMapped}') ?? 0) == 1) {
                    displayInterestList![i].isMapped = 0;
                  } else {
                    displayInterestList![i].isMapped = 1;
                  }
                  setState(() {
                    if (selectProgramId.contains(displayInterestList![i].id)) {
                      selectProgramId.remove(displayInterestList![i].id);
                    } else {
                      selectProgramId.add(displayInterestList![i].id);
                    }
                  });
                });
              }
            },

            child: Column(
              children: [
                Stack(
                  children: [
                    Container(
                      margin: EdgeInsets.all(0),
                      //width: width(context),
                      //height: width(context),
                      width: 120,
                      height: 120,
                      //color: Colors.grey[200],
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: '${displayInterestList?[i].bannerImage}'.contains('.svg') ?
                        SvgPicture.network(
                            '${displayInterestList?[i].bannerImage}',
                        ) : Image.network(
                          '${displayInterestList?[i].bannerImage}',
                          fit: BoxFit.cover,
                          errorBuilder: (context, url, error) {
                            return Image.asset(
                              'assets/images/blank.png',
                              height: 50,
                              width: 50,
                            );
                          },
                          loadingBuilder: (BuildContext context, Widget child,
                              ImageChunkEvent? loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                  height: 45,
                                  margin: EdgeInsets.only(left: 2),
                                  width: 45,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  )),
                            );
                          },
                        ),
                      ),
                    ),


                    if(widget.singleSelection == false)...[
                      if ((int.tryParse('${displayInterestList![i].isMapped}') ?? 0) > 0)
                        Positioned(
                            right: -3,
                            top: -3,
                            child: SvgPicture.asset('assets/images/interest_selected.svg')),
                    ]else...[
                      if ((int.tryParse('${displayInterestList![i].isMapped}') ?? 0) == 1)
                        Positioned(
                            right: -3,
                            top: -3,
                            child: SvgPicture.asset('assets/images/interest_selected.svg')),
                    ]
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 0, right: 0, top: 5),
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Text('${displayInterestList![i].jobRole ?? ""}',
                        style: Styles.bold(
                          size: 11,
                          //lineHeight: 1.2,
                          color: ColorConstants.GREY_3,
                        ),
                        maxLines: 2,
                        textAlign: TextAlign.center),
                  ),
                ),
              ],
            ),
          );
        });
  }
}
