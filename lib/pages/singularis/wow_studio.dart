import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/pages/singularis/community/tab2/alumni_voice.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import '../../data/models/response/home_response/joy_category_response.dart';
import '../../utils/config.dart';

class WowStudio extends StatefulWidget {
  final int? postId;
  const WowStudio({Key? key, this.postId}) : super(key: key);

  @override
  State<WowStudio> createState() => _WowStudioState();
}

class _WowStudioState extends State<WowStudio> {
  List<ListElement>? joyCategoryList = [];

  @override
  void initState() {
    BlocProvider.of<HomeBloc>(context).add(JoyCategoryEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: AppBar(
            automaticallyImplyLeading: false,
            elevation: 0,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                color: ColorConstants.WHITE,
                gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: <Color>[
                      ColorConstants().gradientLeft(),
                      ColorConstants().gradientRight()
                    ]),
              ),
            )),
      ),
      body: ListView(
        physics: NeverScrollableScrollPhysics(),
        primary: false,
        children: [
          Container(
            height: height(context) * 0.18,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20)),
              gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: <Color>[
                    ColorConstants().gradientLeft(),
                    ColorConstants().gradientRight()
                  ]),
            ),
            child: Column(children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    width: width(context) * 0.1,
                    child: IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(
                          Icons.arrow_back_ios,
                          color: ColorConstants.WHITE,
                        )),
                  ),
                  SizedBox(width: width(context) * 0.3),
                  Padding(
                    padding: EdgeInsets.only(
                        top:
                            APK_DETAILS["package_name"] == "com.singularis.mesc"
                                ? 8.0
                                : 0.0),
                    child: APK_DETAILS["package_name"] == "com.singularis.mesc"
                        ? SvgPicture.asset(
                            'assets/images/mesc_wow_studio.svg',
                            height: height(context) * 0.06,
                          )
                        : SvgPicture.asset(
                            'assets/images/wow_studio_w.svg',
                            height: height(context) * 0.06,
                          ),
                  ),
                ],
              ),
              SizedBox(height: 10),
              Text(
                      APK_DETAILS["package_name"] == "com.singularis.mesc"
                          ? 'mesc_Welcome_wowstudio'
                          : 'Welcome_wowstudio',
                      textAlign: TextAlign.center,
                      style: Styles.bold(color: ColorConstants.WHITE, size: 16))
                  .tr(),
              SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.only(left: 0.0, right: 0.0),
                child: Text(
                        APK_DETAILS["package_name"] == "com.singularis.mesc"
                            ? 'mesc_wow_description'
                            : 'wow_description',
                        textAlign: TextAlign.center,
                        style: Styles.regular(
                            color: ColorConstants.WHITE, size: 12))
                    .tr(),
              )
            ]),
          ),
          AlumniVoice(postId: widget.postId)
        ],
      ),
    );
  }
}
