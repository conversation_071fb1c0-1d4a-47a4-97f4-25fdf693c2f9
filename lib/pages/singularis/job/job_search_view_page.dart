import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/singularis/job/widgets/blank_widget_page.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/utility.dart';
import 'package:material_floating_search_bar_2/material_floating_search_bar_2.dart';
//import 'package:material_floating_search_bar/material_floating_search_bar.dart';
import 'package:provider/provider.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/response/home_response/competition_response.dart';
import '../../../utils/Log.dart';
import '../../../utils/Styles.dart';
import '../../../utils/resource/colors.dart';
import '../../../utils/resource/size_constants.dart';
import '../../custom_pages/custom_widgets/NextPageRouting.dart';
import 'job_details_page.dart';
import 'package:masterg/data/models/response/home_response/user_jobs_list_response.dart';

class JobSearchViewPage extends StatefulWidget {
  final String? appBarTitle;
  final bool? isSearchMode;
  final String? jobRolesId;
  final String? domainId;

  const JobSearchViewPage(
      {Key? key,
      this.appBarTitle,
      this.isSearchMode = true,
      this.jobRolesId,
      this.domainId})
      : super(key: key);

  @override
  State<JobSearchViewPage> createState() => _JobSearchViewPageState();
}

class _JobSearchViewPageState extends State<JobSearchViewPage> {
  bool? isJobLoading;
  bool? myJobLoading = false;
  List<ListElement>? jobList;
  CompetitionResponse? allJobListResponse;
  CompetitionResponse? allJobListRecomResponse;
  bool? applyJobLoader = false;

  static const historyLength = 5;

  List<String> _searchHistory = [
    'fuchsia',
    'flutter',
    'widgets',
    'resocoder',
  ];

  late List<String> filteredSearchHistory;
  late String selectedTerm = tr('search_skills');

  List<String> filterSearchTerms({
    required String filter,
  }) {
    if (filter.isNotEmpty) {
      return _searchHistory.reversed
          .where((term) => term.startsWith(filter))
          .toList();
    } else {
      return _searchHistory.reversed.toList();
    }
  }

  void addSearchTerm(String term) {
    if (_searchHistory.contains(term)) {
      putSearchTermFirst(term);
      return;
    }

    _searchHistory.add(term);
    if (_searchHistory.length > historyLength) {
      _searchHistory.removeRange(0, _searchHistory.length - historyLength);
    }

    filteredSearchHistory = filterSearchTerms(filter: 'null');
  }

  void deleteSearchTerm(String term) {
    _searchHistory.removeWhere((t) => t == term);
    filteredSearchHistory = filterSearchTerms(filter: 'null');
  }

  void putSearchTermFirst(String term) {
    deleteSearchTerm(term);
    addSearchTerm(term);
  }

  late FloatingSearchBarController controller;

  @override
  void initState() {
    super.initState();
    controller = FloatingSearchBarController();
    filteredSearchHistory = filterSearchTerms(filter: 'null');
    getMyJobList(false);
    // if(widget.jobRolesId != ''){
    getMyJobListRecommended(false);
    //}
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void getMyJobList(bool jobType) {
    BlocProvider.of<HomeBloc>(context).add(JobCompListFilterEvent(
        isPopular: false,
        isFilter: true,
        ids: widget.jobRolesId,
        domainId: widget.domainId,
        jobTypeMyJob: jobType));
  }

  void getMyJobListRecommended(bool jobType) {
    BlocProvider.of<HomeBloc>(context).add(JobCompListFilterEvent(
        isPopular: false,
        isFilter: true,
        ids: widget.jobRolesId,
        domainId: widget.domainId,
        widgetType: 'recomJob',
        jobTypeMyJob: jobType));
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
        providers: [
          ChangeNotifierProvider<CompetitionResponseProvider>(
            create: (context) =>
                CompetitionResponseProvider(allJobListResponse?.data),
          ),
        ],
        child: Consumer<CompetitionResponseProvider>(
            builder: (context, competitionProvider, child) => BlocManager(
                  initState: (context) {},
                  child: BlocListener<HomeBloc, HomeState>(
                    listener: (context, state) {
                      if (state is JobCompListFilterState) {
                        _handlecompetitionListResponse(
                            state, competitionProvider);
                      }
                    },
                    child: Scaffold(
                      appBar: AppBar(
                        iconTheme: IconThemeData(
                          color: Colors.black,
                        ),
                        elevation: 0.0,
                        backgroundColor: ColorConstants.WHITE,
                        title: Text(
                          widget.appBarTitle!,
                          style: TextStyle(color: Colors.black),
                        ),
                      ),
                      backgroundColor: ColorConstants.JOB_BG_COLOR,
                      body: ScreenWithLoader(
                          isLoading: applyJobLoader,
                          body: _makeBody(competitionProvider)),
                    ),
                  ),
                )));
  }

  Widget _makeBody(competitionProvider) {
    return SingleChildScrollView(
      child: Container(
        margin: EdgeInsets.only(
            left: SizeConstants.JOB_SEARCH_PAGE_MGN,
            right: SizeConstants.JOB_SEARCH_PAGE_MGN,
            bottom: SizeConstants.JOB_SEARCH_PAGE_MGN),
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            SizedBox(
              height: 10.0,
            ),

            ///Job List
            widget.isSearchMode == true
                ? SizedBox(
                    height: 30,
                  )
                : SizedBox(),
            allJobListResponse?.data != null
                ? MultiProvider(providers: [
                    ChangeNotifierProvider<CompetitionResponseProvider>(
                      create: (context) =>
                          CompetitionResponseProvider(allJobListResponse?.data),
                    ),
                  ], child: renderJobList(competitionProvider))
                : BlankWidgetPage(),
          ],
        ),
      ),
    );
  }

  Widget renderJobList(competitionProvider) {
    return competitionProvider.list.length != 0
        ? ListView.builder(
            itemCount: competitionProvider.list.length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (BuildContext context, int index) {
              return Column(
                children: [
                  Container(
                    color: Colors.white,
                    width: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Container(
                                padding: EdgeInsets.only(
                                  right: 10.0,
                                ),
                                //child: Image.asset('assets/images/google.png'),
                                child: competitionProvider.list[index]?.image !=
                                        null
                                    ? Image.network(
                                        '${competitionProvider.list[index]!.image}')
                                    : Image.asset('assets/images/pb_2.png')),
                          ),
                          Expanded(
                            flex: 10,
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    NextPageRoute(JobDetailsPage(
                                      title:
                                          competitionProvider.list[index]!.name,
                                      description: competitionProvider
                                          .list[index]!.description,
                                      location: competitionProvider
                                          .list[index]!.location,
                                      skillNames: competitionProvider
                                          .list[index]!.skillNames,
                                      companyName: competitionProvider
                                          .list[index]!.organizedBy,
                                      domain: competitionProvider
                                          .list[index]!.domainName,
                                      companyThumbnail: competitionProvider
                                          .list[index]!.image,
                                      experience: competitionProvider
                                          .list[index]!.experience,
                                      jobStatusNumeric: competitionProvider
                                          .list[index]!.jobStatusNumeric,
                                      id: competitionProvider.list[index]!.id,
                                      jobStatus: competitionProvider
                                          .list[index]!.jobStatus,
                                    ))).then((value) {
                                  competitionProvider.resetValue();
                                  controller = FloatingSearchBarController();
                                  filteredSearchHistory =
                                      filterSearchTerms(filter: 'null');
                                  getMyJobList(false);
                                  getMyJobListRecommended(false);
                                });
                              },
                              child: Container(
                                padding: EdgeInsets.only(left: 5.0, right: 5),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        '${competitionProvider.list[index]!.name}',
                                        style: Styles.bold(
                                            size: 15,
                                            color: ColorConstants.BLACK)),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Text(
                                          '${competitionProvider.list[index]!.organizedBy}',
                                          style: Styles.regular(
                                              size: 12,
                                              color: ColorConstants.GREY_3)),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 5.0),
                                      child: competitionProvider.list[index]!
                                                          .minExperience !=
                                                      null &&
                                                  competitionProvider
                                                          .list[index]!
                                                          .minExperience !=
                                                      0.0 ||
                                              competitionProvider.list[index]!
                                                          .maxExperience !=
                                                      null &&
                                                  competitionProvider
                                                          .list[index]!
                                                          .maxExperience !=
                                                      0.0
                                          ? SizedBox(
                                              child: Row(
                                                children: [
                                                  Icon(Icons.work_outline,
                                                      size: 16,
                                                      color: ColorConstants
                                                          .BODY_TEXT),
                                                  Padding(
                                                    padding: EdgeInsets.only(
                                                        left: Utility()
                                                                .isRTL(context)
                                                            ? 0
                                                            : 5.0,
                                                        right: Utility()
                                                                .isRTL(context)
                                                            ? 5.0
                                                            : 0.0),
                                                    child: Text(
                                                        '${tr('exp')}: ',
                                                        style: Styles.regular(
                                                            size: 12,
                                                            color: ColorConstants
                                                                .BODY_TEXT)),
                                                  ),
                                                  Text(
                                                      '${allJobListResponse?.data?[index]?.minExperience != 0.0 ? allJobListResponse?.data![index]?.minExperience : "0"}' +
                                                          '-${allJobListResponse?.data![index]?.maxExperience != 0.0 ? allJobListResponse?.data![index]?.maxExperience : "0"} ${tr('yrs')} ',
                                                      style: Styles.regular(
                                                          size: 12,
                                                          color: ColorConstants
                                                              .GREY_3)),
                                                  if (competitionProvider
                                                              .list[index]!
                                                              .location !=
                                                          null &&
                                                      competitionProvider
                                                              .list[index]
                                                              .location !=
                                                          "") ...[
                                                    Icon(
                                                      Icons
                                                          .location_on_outlined,
                                                      size: 16,
                                                      color:
                                                          ColorConstants.GREY_3,
                                                    ),
                                                    Flexible(
                                                      child: Text(
                                                          '${competitionProvider.list[index]!.location ?? ''}',
                                                          style: Styles.regular(
                                                              size: 12,
                                                              color:
                                                                  ColorConstants
                                                                      .GREY_3)),
                                                    )
                                                  ],
                                                ],
                                              ),
                                            )
                                          : SizedBox(),
                                    ),
                                    // SizedBox(height: 2),
                                    // if (competitionProvider
                                    //             .list[index]!.location !=
                                    //         null &&
                                    //     competitionProvider
                                    //             .list[index].location !=
                                    //         "") ...[
                                    //   Row(
                                    //     children: [
                                    //       Icon(
                                    //         Icons.location_on_outlined,
                                    //         size: 16,
                                    //         color: ColorConstants.GREY_3,
                                    //       ),
                                    //       Flexible(
                                    //         child: Text(
                                    //             '${competitionProvider.list[index]!.location ?? ''}',
                                    //             style: Styles.regular(
                                    //                 size: 12,
                                    //                 color:
                                    //                     ColorConstants.GREY_3)),
                                    //       ),
                                    //     ],
                                    //   )
                                    // ],
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Container(
                              // color: Colors.red,
                              padding: EdgeInsets.only(left: 0.0),
                              child: InkWell(
                                onTap: () async {
                                  setState(() {
                                    applyJobLoader = true;
                                  });

                                  jobApply(
                                      int.parse(
                                        '${competitionProvider.list[index]!.id}',
                                      ),
                                      1);
                                  await Future.delayed(Duration(seconds: 2));
                                  Get.rawSnackbar(
                                    messageText: Text(
                                      'application_submitted',
                                      style: Styles.regular(
                                          size: 14,
                                          color: ColorConstants.WHITE),
                                    ).tr(),
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 20),
                                    snackPosition: SnackPosition.BOTTOM,
                                    backgroundColor: ColorConstants.BLACK,
                                    borderRadius: 4,
                                    duration: Duration(seconds: 3),
                                    boxShadows: [
                                      BoxShadow(
                                          color: Color(0xff898989)
                                              .withOpacity(0.1),
                                          offset: Offset(0, 4.0),
                                          blurRadius: 11)
                                    ],
                                  );
                                  competitionProvider
                                      .updateAppliedStatus(index);
                                  setState(() {
                                    applyJobLoader = false;
                                  });
                                },
                                child: GradientText(
                                  competitionProvider.list[index]?.jobStatus !=
                                          null
                                      ? tr('applied')
                                      : tr('apply_buttn'),
                                  style: Styles.bold(
                                      size: competitionProvider
                                                  .list[index]?.jobStatus ==
                                              null
                                          ? 12
                                          : 11),
                                  colors: [
                                    competitionProvider
                                                .list[index]?.jobStatus ==
                                            null
                                        ? ColorConstants().gradientLeft()
                                        : ColorConstants.GREEN,
                                    competitionProvider
                                                .list[index]?.jobStatus ==
                                            null
                                        ? ColorConstants().gradientRight()
                                        : ColorConstants.GREEN,
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: ColorConstants.GREY_3,
                  ),
                ],
              );
            })
        : SizedBox(
            height: height(context) * 0.85,
            child: Center(
                child: Text('no_jobs_found', style: Styles.regular()).tr()));
  }

  void jobApply(int jobId, int? isApplied) {
    BlocProvider.of<HomeBloc>(context).add(CompetitionContentListEvent(
        competitionId: jobId, isApplied: isApplied));
  }

  void _handlecompetitionListResponse(
      JobCompListFilterState state, CompetitionResponseProvider provider) {
    var jobCompState = state;
    setState(() {
      switch (jobCompState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          myJobLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("_handlecompetitionListResponses....................");
          allJobListResponse = state.jobListResponse;
          //provider.resetList(allJobListResponse!.data!);
          if (allJobListResponse!.data != null) {
            provider.addItemList(allJobListResponse!.data!);
          } else {
            provider.resetList(allJobListResponse!.data!);
          }

          myJobLoading = false;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CompetitionListIDState .....................${jobCompState.error}");
          myJobLoading = false;
          FirebaseAnalytics.instance.logEvent(name: 'job_search', parameters: {
            "ERROR": '${jobCompState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleJobResponse(UserJobListState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isJobLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserJobListState....................");
          jobList = state.response!.list!;
          isJobLoading = false;
          break;
        case ApiStatus.ERROR:
          isJobLoading = false;
          Log.v("Error..........................");
          Log.v(
              "ErrorUserJobListState..........................${loginState.error}");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
