
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/config.dart';

class PortfoliosMenuPage extends StatelessWidget {
  const PortfoliosMenuPage({super.key});

  @override
  Widget build(BuildContext context) {
     print('webview url is ${APK_DETAILS['portfolios']}' +
        Preference.getInt(
            Preference.USER_ID)
            .toString());
    
    return Container(
        child: TermsAndCondition(
          url:
          '${APK_DETAILS['portfolios']}' +
              Preference.getInt(
                  Preference.USER_ID)
                  .toString(),
          title: tr('portfolio'),
          appBarEnable: true,
        )
    );
  }
}
