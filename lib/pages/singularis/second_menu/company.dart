import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/config.dart';

class CompanyMenuPage extends StatelessWidget {
  const CompanyMenuPage({super.key});

  @override
  Widget build(BuildContext context) {
    print('webview url is${APK_DETAILS['company']}' +
        Preference.getInt(Preference.USER_ID).toString());
    return Container(
        child: TermsAndCondition(
      url: '${APK_DETAILS['company']}' +
          Preference.getInt(Preference.USER_ID).toString(),
      title: tr('company'),
      appBarEnable: true,
    ));
  }
}
