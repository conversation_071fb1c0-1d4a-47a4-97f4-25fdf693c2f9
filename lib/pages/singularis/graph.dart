import 'dart:math' as math;
import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/job_domain_detail_resp.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

class LineChartWidget extends StatefulWidget {
  final int? domainid;

  const LineChartWidget({super.key, required this.domainid});

  @override
  State<LineChartWidget> createState() => _LineChartWidgetState();
}

class _LineChartWidgetState extends State<LineChartWidget> {
  final List<Color> gradientColors = [
    Color(0xff3EBDA0),
    Color.fromARGB(255, 169, 214, 204),
  ];
  bool _isLoading = true;
  JobDomainResponse? domainData;
  double maxX = 0;
  double maxY = 0;
  double minX = 2090;
  double minY = 2090;

  @override
  void initState() {
    getDomainData(widget.domainid!);
    super.initState();
  }

  @override
  Widget build(BuildContext context) => BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is JobDomainDetailState) {
            handleDomainDetial(state);
          }
        },
        child: ScreenWithLoader(
          isLoading: _isLoading,
          body: _isLoading
              ? SizedBox()
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                        height: height(context) * 0.08,
                      ),
                      SizedBox(
                        height: height(context) * 0.4,
                        width: width(context) * 0.82,
                        child: LineChart(
                          LineChartData(
                            minX: minX,
                            maxX: maxX,
                            minY: minY,
                            maxY: maxY,
                            baselineX: 200,
                            lineTouchData: LineTouchData(touchTooltipData:
                                LineTouchTooltipData(getTooltipItems:
                                    (List<LineBarSpot> touchedBarSpots) {
                              return touchedBarSpots.map((barSpot) {
                                return LineTooltipItem(
                                  '${domainData?.data?.graphArr[barSpot.spotIndex][0]}\n',
                                  TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  children: [
                                    TextSpan(
                                      text:
                                          '${domainData?.data?.graphArr[barSpot.spotIndex][1]}',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w900,
                                      ),
                                    ),
                                    TextSpan(
                                      text: ' Jobs',
                                      style: TextStyle(
                                        fontWeight: FontWeight.normal,
                                      ),
                                    ),
                                  ],
                                  textAlign: TextAlign.left,
                                );
                              }).toList();
                            })),
                            titlesData: FlTitlesData(
                                topTitles: AxisTitles(),
                                rightTitles: AxisTitles(
                                    sideTitles: SideTitles(showTitles: false)),
                                show: true,
                                leftTitles: AxisTitles(
                                    sideTitles: SideTitles(
                                        showTitles: false, interval: 100),
                                    axisNameWidget: Transform.translate(
                                      offset: Offset(0, -20),
                                      child: Text(
                                        'number_jobs',
                                        style: Styles.regular(
                                            lineHeight: 0.1, size: 12),
                                      ).tr(),
                                    )),
                                bottomTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                  axisNameWidget: Text(
                                      '${domainData?.data?.graphText}',
                                      style: Styles.regular(size: 12)),
                                )),
                            gridData: FlGridData(
                              // verticalInterval: 10,
                              show: true,
                              getDrawingHorizontalLine: (value) {
                                return FlLine(
                                  color: const Color(0xff37434d),
                                  strokeWidth: 0.3,
                                );
                              },
                              drawVerticalLine: false,
                              getDrawingVerticalLine: (value) {
                                return FlLine(
                                  color: const Color(0xff37434d),
                                  strokeWidth: 0.3,
                                );
                              },
                            ),
                            borderData: FlBorderData(
                              show: false,
                              border: Border.all(
                                  color: const Color(0xff37434d), width: 1),
                            ),
                            lineBarsData: [
                              LineChartBarData(
                                spots: domainData!.data!.graphArr
                                    .map((e) => FlSpot(double.parse(e[0]),
                                        double.parse('${e[1]}')))
                                    .toList(),
                                isCurved: true,
                                belowBarData: BarAreaData(
                                  show: true,
                                  gradient: LinearGradient(
                                    colors: gradientColors
                                        .map((color) => color.withOpacity(0.4))
                                        .toList(),
                                  ),
                                ),
                                color: gradientColors[0],
                                barWidth: 1.2,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Table(
                        // defaultColumnWidth: FixedColumnWidth(130.0),
                        columnWidths: {
                          0: FixedColumnWidth(width(context) * 0.33),
                          1: FixedColumnWidth(width(context) * 0.33),
                          2: FixedColumnWidth(width(context) * 0.33),
                        },
                        children: [
                          TableRow(children: [
                            Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 6, horizontal: 12),
                                child: Text(
                                  'job_roles',
                                  style: Styles.bold(size: 14),
                                ).tr()),
                            Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 6,
                                ),
                                child: Text(
                                  'est_annual_salary',
                                  style: Styles.bold(size: 14),
                                  textAlign: TextAlign.center,
                                ).tr()),
                            Padding(
                                padding: EdgeInsets.only(
                                    top: 6,
                                    bottom: 6,
                                    left: Utility().isRTL(context) ? 12 : 0,
                                    right: Utility().isRTL(context) ? 50 : 12),
                                child: Text(
                                  'annual_growth',
                                  style: Styles.bold(size: 14),
                                  textAlign: TextAlign.end,
                                ).tr()),
                          ]),
                          ...domainData!.data!.list
                              .map((e) => TableRow(
                                      decoration: BoxDecoration(
                                        border: Border(
                                          bottom: BorderSide(
                                              width: 1.0,
                                              color: ColorConstants
                                                  .DIVIDER), // Adjust border properties as needed
                                        ),
                                      ),
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 6, horizontal: 12),
                                          child: Text(
                                            '${e.title}',
                                            style: Styles.bold(size: 12),
                                          ),
                                        ),
                                        Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 6),
                                            child: Text(
                                              '${e.salary ?? ''}',
                                              style: Styles.regular(size: 12),
                                              textAlign: TextAlign.center,
                                            )),
                                        Padding(
                                            padding: EdgeInsets.only(
                                                top: 6,
                                                bottom: 6,
                                                left: Utility().isRTL(context)
                                                    ? 12
                                                    : 0,
                                                right: Utility().isRTL(context)
                                                    ? 50
                                                    : 12),
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                Text(
                                                    e.growthType != 'up'
                                                        ? e.growth != null
                                                            ? '- ${e.growth ?? '0'}%'
                                                            : '${e.growth ?? '0'}%'
                                                        : '+ ${e.growth ?? '0'}%',
                                                    textAlign: TextAlign.end,
                                                    style: Styles.regular(
                                                        size: 12,
                                                        color: e.growthType !=
                                                                'up'
                                                            ? ColorConstants.RED
                                                            : gradientColors[
                                                                0])),
                                                if (Utility().isRTL(context))
                                                  SizedBox(
                                                    width: 10,
                                                  ),
                                                Transform(
                                                  alignment: Alignment.center,
                                                  transform: e.growthType !=
                                                          'up'
                                                      ? Matrix4.rotationX(
                                                          math.pi)
                                                      : Matrix4.rotationY(0),
                                                  child: SvgPicture.asset(
                                                    'assets/images/growth_up.svg',
                                                    color: e.growthType != 'up'
                                                        ? ColorConstants.RED
                                                        : null,
                                                  ),
                                                )
                                              ],
                                            ))
                                      ]))
                              .toList()
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 18),
                        child: Text(
                          '${domainData?.data?.disclaimer ?? ''}',
                          style: Styles.textItalic(),
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      );

  Widget card(String jobrole, String salaryRange, String annualGrowth,
      String growthType) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                  width: width(context) * 0.4,
                  child: Text(
                    '$jobrole',
                    style: Styles.bold(size: 12),
                  )),
              SizedBox(
                width: width(context) * 0.25,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '$salaryRange',
                      style: Styles.regular(size: 12),
                    ),
                    Spacer(),
                  ],
                ),
              )
            ],
          ),
          Divider()
        ],
      ),
    );
  }

  void getDomainData(int domainId) {
    BlocProvider.of<HomeBloc>(context)
        .add(JobDomainDetailEvent(domainId: domainId));
  }

  void handleDomainDetial(JobDomainDetailState state) {
    var loginState = state;
    Log.v("Loading....................GetCoursesState ${state.apiState}");

    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          _isLoading = true;
          Log.v("Loading....................GetCoursesState");
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................GetCoursesState ${state.response}");
          _isLoading = false;
          domainData = state.response;
          if (domainData?.data?.graphArr.length != 0) {
            for (int i = 0; i < domainData!.data!.graphArr.length; i++) {
              maxX = max(maxX, double.parse(domainData!.data!.graphArr[i][0]));
              maxY = max(maxY,
                  double.parse(domainData!.data!.graphArr[i][1].toString()));
              minX = min(minX, double.parse(domainData!.data!.graphArr[i][0]));
              minY = min(minY,
                  double.parse(domainData!.data!.graphArr[i][1].toString()));
            }
          }
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................GetCoursesState");
          Log.v("Error..........................${loginState.error}");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
