
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:masterg/utils/utility.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

import '../../local/pref/Preference.dart';
import '../../utils/Styles.dart';
import '../../utils/config.dart';
import '../../utils/resource/colors.dart';

class MeCateLoginStatePage extends StatefulWidget {
  final String? title;
  MeCateLoginStatePage({Key? key, this.title}) : super(key: key);

  @override
  State<MeCateLoginStatePage> createState() => _MeCateLoginStatePageState();
}

class _MeCateLoginStatePageState extends State<MeCateLoginStatePage> {

  final _key = UniqueKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(
          color: Colors.black, //change your color here
        ),
          title: Text(
            '${widget.title}'
                .replaceAll('Singularis', '${APK_DETAILS['app_name']}'),
            style: Styles.semibold(
              color: ColorConstants.BLACK,
            ),
          ),
      ),
      body: FutureBuilder(
        future: fetchUrl(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            print('URL==== ${snapshot.data}');
            return WebViewWidget(
              key: _key,
              //initialUrl: 'http://app.mecat.in/ssologin/'+snapshot.data.toString(),
              controller: WebViewController()
                ..setJavaScriptMode(JavaScriptMode.unrestricted)
                ..loadRequest(Uri.parse('http://app.mecat.in/ssologin/'+snapshot.data.toString())),
            );
          }
        },
      ),
    );
  }

  void myDartFunction() {
    print('Dart function called from HTML page!');
  }

  Future<String> fetchUrl() async {
    String encEmail = '';
    if(Preference.getString(Preference.USER_EMAIL).toString().contains('@')){
      encEmail = Utility().encrypted128(Preference.getString(Preference.USER_EMAIL).toString());
    }else{
      encEmail = Preference.getString(Preference.USER_EMAIL).toString();
    }

    final String apiUrl = 'https://app.mecat.in/api/generate-token';
    final Map<String, dynamic> payload = {
      'encrypted_params': encEmail,
    };
    final http.Response response = await http.post(
      Uri.parse(apiUrl),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: jsonEncode(payload),
    );
    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);
      final ssoToken = jsonData['data']['sso_token'];
      return ssoToken;
    } else {
      throw Exception('Failed to load URL');
    }
  }
}