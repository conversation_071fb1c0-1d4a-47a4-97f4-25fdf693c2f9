import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:dio/dio.dart' as Dio;
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injector/injector.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/create_post_response.dart';
import 'package:masterg/data/models/response/home_response/gcarvaan_post_reponse.dart';
import 'package:masterg/data/providers/reel_controller.dart';
import 'package:masterg/data/repositories/home_repository.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/gcarvaan/createpost/create_post_provider.dart';
import 'package:masterg/pages/gcarvaan/createpost/pdf_view.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:video_thumbnail/video_thumbnail.dart' as Thumbnail;

import '../../../main.dart';
import '../../../utils/click_picker.dart';
import '../../../utils/config.dart';

List<String?>? croppedList;
String? thumnailUrl;

class CreateGCarvaanPage extends StatefulWidget {
  final List<Dio.MultipartFile>? fileToUpload;
  List<String?>? filesPath;
  final bool isReelsPost;
  final CreatePostProvider? provider;

  CreateGCarvaanPage(
      {Key? key,
      this.fileToUpload,
      this.filesPath,
      this.isReelsPost = false,
      this.provider})
      : super(key: key);

  @override
  _CreateGCarvaanPageState createState() => _CreateGCarvaanPageState();
}

class _CreateGCarvaanPageState extends State<CreateGCarvaanPage> {
  bool isPostedLoading = false;
  CreatePostResponse? responseData;
  TextEditingController postDescriptionController = TextEditingController();
  List<GCarvaanPostElement>? gcarvaanPosts;

  bool isRTL = false;
  double? screenWidth;
  dynamic hashTags;

  void getHashTags() async {
    try {
      ApiService api = ApiService();
      dynamic response =
          await api.dio.get('${APK_DETAILS["domain_url"]}event_tags.json');
      setState(() {
        hashTags = jsonDecode('$response');
      });
    } catch (e) {
      Log.v(
        "unable to fetch hashtags $e",
      );
    }
  }

  @override
  void initState() {
    super.initState();
    getHashTags();
  }

  @override
  void dispose() {
    //flickManager.dispose();
    _cleanupTemporaryFiles();
    super.dispose();
  }

  /// Clean up any temporary files created during the session
  void _cleanupTemporaryFiles() async {
    try {
      final Directory tempDir = await getTemporaryDirectory();
      final List<FileSystemEntity> files = tempDir.listSync();

      for (FileSystemEntity file in files) {
        if (file is File && file.path.contains('crop_source_')) {
          try {
            await file.delete();
          } catch (e) {
            Log.v("Failed to delete temp file: ${file.path}, error: $e");
          }
        }
      }
    } catch (e) {
      Log.v("Error during temp file cleanup: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    isRTL = Utility().isRTL(context);
    screenWidth = width(context);

    return MultiProvider(
        providers: [
          ChangeNotifierProvider<CreatePostProvider>(
            create: (context) => CreatePostProvider(widget.filesPath, false),
          ),
          ChangeNotifierProvider<MenuListProvider>(
            create: (context) => MenuListProvider([]),
          ),
          ChangeNotifierProvider<GCarvaanListModel>(
            create: (context) => GCarvaanListModel(gcarvaanPosts),
          ),
        ],
        child: WillPopScope(
            // ignore: missing_return
            onWillPop: () async {
              widget.provider?.clearList();
              return true;
            },
            child: GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              child: Scaffold(
                backgroundColor: Colors.white,
                appBar: AppBar(
                  backgroundColor: Colors.white,
                  elevation: 0,
                  automaticallyImplyLeading: false,
                  leading: IconButton(
                      onPressed: () {
                        widget.provider?.clearList();
                        Navigator.pop(context);
                        //Navigator.pop(context);
                      },
                      icon: Icon(Icons.close, color: ColorConstants.BLACK)),
                  title: Column(children: [
                    SizedBox(
                      height: 10,
                    ),
                    Text(
                      '${tr('create_post')} ',
                      style: Styles.bold(size: 14, color: ColorConstants.BLACK),
                    )
                  ]),
                  centerTitle: true,
                ),
                body: Consumer2<CreatePostProvider, GCarvaanListModel>(
                  builder: (context, value, gcarvaanListModel, child) =>
                      BlocManager(
                          initState: (BuildContext context) {
                            //createPost();
                          },
                          child: BlocListener<HomeBloc, HomeState>(
                            listener: (context, state) {
                              if (state is CreatePostState)
                                _handleCreatePostResponse(state, value);
                            },
                            child: ScreenWithLoader(
                                isLoading: isPostedLoading,
                                body: _content(value)),
                          )),
                ),
              ),
            )));
  }

  Widget _content(CreatePostProvider value) {
    Size size = MediaQuery.of(context).size;

    return SafeArea(
      child: Container(
        margin: EdgeInsets.only(top: 4),
        height: size.height,
        child: Stack(children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: SingleChildScrollView(
              child: Form(
                child: Column(
                  children: [
                    if (APK_DETAILS["package_name"] == "com.singulariswow.mec")
                      if (hashTags != null && hashTags.length != 0)
                        SizedBox(
                          height: 60,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: hashTags['tags'].length,
                            itemBuilder: (context, index) => InkWell(
                              onTap: () {
                                print('hashTags==${hashTags.length}');
                                postDescriptionController.text =
                                    postDescriptionController.text +
                                        ' ${hashTags['tags'][index]} ';
                                postDescriptionController.selection =
                                    TextSelection.collapsed(
                                        offset: postDescriptionController
                                            .text.length);
                              },
                              child: Container(
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 5),
                                  padding: const EdgeInsets.only(
                                      top: 8, bottom: 2, left: 2, right: 2),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4)),
                                  child: Text('${hashTags['tags'][index]}',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: ColorConstants.PRIMARY_BLUE,
                                        decoration: TextDecoration.underline,
                                      ))),
                            ),
                          ),
                        ),
                    Container(
                      margin: EdgeInsets.only(top: size.height * 0.02),
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.05),
                      height: size.height * 0.2,
                      width: size.width * 0.9,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: ColorConstants.GREY,
                      ),
                      child: TextFormField(
                        maxLength: 1000,
                        keyboardType: TextInputType.multiline,
                        maxLines: null,
                        controller: postDescriptionController,
                        onChanged: (value) {
                          setState(() {});
                        },
                        validator: (value) {
                          if (value!.isEmpty) {
                            return tr('write_your_post');
                          }
                          return null;
                        },
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            counterText: '',
                            hintText: '${tr('write_your_post')}....',
                            hintStyle: Styles.regular(
                                size: 14, color: ColorConstants.GREY_3),
                            helperMaxLines: 4),
                      ),
                    ),
                    Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        child: Text(
                          '${1000 - postDescriptionController.text.length} /1000',
                          style: Styles.semibold(
                              size: 12,
                              color:
                                  postDescriptionController.text.length == 1000
                                      ? ColorConstants.RED
                                      : ColorConstants.BLACK),
                        ),
                      )
                    ]),
                    SizedBox(height: size.height * 0.03),
                    if (!widget.isReelsPost)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: () async {
                              _initFilePiker(value);
                            },
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/images/image.svg',
                                  color: ColorConstants().primaryColor(),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 5.0, right: 5.0),
                                  child: Text('photo_video',
                                          style: Styles.regular(
                                              size: 14,
                                              color: ColorConstants.BLACK))
                                      .tr(),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          InkWell(
                            onTap: () async {
                              if (value.files!.length >= 4) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content:
                                      Text('only_four_files_are_allowed').tr(),
                                ));
                                return;
                              }
                              final cameras = await availableCameras();
                              final firstCamera = cameras.first;
                              print('the value is $firstCamera');

                              await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => TakePictureScreen(
                                            camera: firstCamera,
                                            cameras: cameras,
                                          ))).then((files) async {
                                if (files != null) {
                                  value.addToList(files);
                                  croppedList = value.files?.toList();
                                }
                              });
                            },
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/images/camera_y.svg',
                                  color: ColorConstants().primaryColor(),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 5.0, right: 5),
                                  child: Text('camera',
                                          style: Styles.regular(
                                              size: 14,
                                              color: ColorConstants.BLACK))
                                      .tr(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    value.files != null
                        ? ShowReadyToPost(
                            provider: value,
                            isReelPost: widget.isReelsPost,
                          )
                        : SizedBox(),
                  ],
                ),
              ),
            ),
          ),
          Consumer<MenuListProvider>(
              builder: (context, menuProvider, child) => Positioned(
                    bottom: 0,
                    child: Container(
                      width: size.width,
                      height: size.height * 0.09,
                      decoration: BoxDecoration(
                        color: ColorConstants.WHITE,
                      ),
                      child: InkWell(
                        onTap: () {
                          widget.provider?.updateList(croppedList);
                          if (value.files!.length != 0) {
                            String? firstExtension = value.files?.first
                                ?.split('/')
                                .last
                                .split('.')
                                .last
                                .toString();
                            bool isVideo = true;
                            if (firstExtension == 'mp4' ||
                                firstExtension == 'mov') isVideo = true;
                            createPost(menuProvider, isVideo, thumnailUrl);
                          } else {
                            AlertsWidget.showCustomDialog(
                                context: context,
                                title: '',
                                text: tr('please_upload_file'),
                                icon: 'assets/images/circle_alert_fill.svg',
                                showCancel: false,
                                oKText: tr('ok'),
                                onOkClick: () async {});
                          }
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(
                              horizontal: size.width * 0.05,
                              vertical: size.width * 0.03),
                          decoration: BoxDecoration(
                            color: ColorConstants().buttonColor(),
                            borderRadius: BorderRadius.circular(10),
                            gradient: LinearGradient(colors: [
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight(),
                            ]),
                          ),
                          child: Center(
                            child: Text('${tr('share')} ',
                                style: Styles.regular(
                                    size: 16,
                                    color: ColorConstants()
                                        .primaryForgroundColor())),
                          ),
                        ),
                      ),
                    ),
                  ))
        ]),
      ),
    );
  }

  void createPost(MenuListProvider provider, bool isVideo, String? thumbnail) {
    final ReelUploadController controller = Get.put(ReelUploadController());
    controller.reset();

    setState(() {
      widget.filesPath = widget.provider?.getFiles();
    });

    if (!widget.isReelsPost) {
      try {
        Get.rawSnackbar(
          padding: const EdgeInsets.all(0),
          messageText: Card(
            child: StreamBuilder<double>(
                stream: uploadProgressController.stream,
                builder: (context, snapshot) {
                  double progress = (snapshot.data ?? 0.0) / 100;
                  return Column(
                    children: [
                      Container(
                        width: screenWidth!,
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(
                            Radius.circular(4),
                          ),
                          gradient: LinearGradient(colors: [
                            ColorConstants().gradientLeft(),
                            ColorConstants().gradientRight(),
                          ]),
                        ),
                        child: Align(
                            alignment: isRTL
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                            child: Text('uploading_post',
                                    style: Styles.regular(
                                        color: ColorConstants.WHITE))
                                .tr()),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Icon(
                              Icons.file_copy,
                              color: ColorConstants.GREY_3,
                            ),
                            Text('${(progress * 100).toStringAsFixed(0)}%')
                          ],
                        ).reverse(isRTL),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Transform.rotate(
                          angle: isRTL ? -math.pi : 0,
                          child: LinearProgressIndicator(
                            value: progress,
                            color: ColorConstants().gradientRight(),
                          ),
                        ),
                      ),
                    ],
                  );
                }),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          snackPosition: SnackPosition.TOP,
          duration: Duration(minutes: 20),
          isDismissible: false,
          backgroundColor: Colors.transparent,
          borderRadius: 4,
          boxShadows: [
            BoxShadow(
                color: Color(0xff898989).withOpacity(0.1),
                offset: Offset(0, 4.0),
                blurRadius: 11)
          ],
        );

        final homeRepository = Injector.appInstance.get<HomeRepository>();
        controller.changeStatus(UploadingStatus.process);
        homeRepository.CreatePost(
          null,
          isVideo == true ? 2 : 1,
          'caravan',
          '',
          postDescriptionController.value.text,
          widget.filesPath,
        ).then((CreatePostResponse value) {
          controller.changeStatus(UploadingStatus.end);

          Get.closeAllSnackbars();
          Get.rawSnackbar(
            messageText: Align(
              alignment: isRTL ? Alignment.centerRight : Alignment.centerLeft,
              child: Text(
                value.status == 1
                    ? tr('post_uploaded')
                    : tr('error_post_uploading'),
                style: Styles.regular(size: 14),
              ),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
            snackPosition: SnackPosition.TOP,
            backgroundColor: ColorConstants.WHITE,
            borderRadius: 4,
            boxShadows: [
              BoxShadow(
                  color: Color(0xff898989).withOpacity(0.1),
                  offset: Offset(0, 4.0),
                  blurRadius: 11)
            ],
          );
        });
      } catch (e) {
        Get.closeAllSnackbars();
        setState(() {
          isPostedLoading = false;
        });
        Get.rawSnackbar(
          messageText: Align(
            alignment: isRTL ? Alignment.centerRight : Alignment.centerLeft,
            child: Text(
              'error_post_uploading',
              style: Styles.regular(size: 14),
            ).tr(),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          snackPosition: SnackPosition.TOP,
          backgroundColor: ColorConstants.WHITE,
          borderRadius: 4,
          boxShadows: [
            BoxShadow(
                color: Color(0xff898989).withOpacity(0.1),
                offset: Offset(0, 4.0),
                blurRadius: 11)
          ],
        );
      }

      Future.delayed(Duration(seconds: 1))
          .then((value) => Navigator.pop(context));
    } else {
      try {
        Get.rawSnackbar(
          padding: const EdgeInsets.all(0),
          messageText: Card(
            child: StreamBuilder<double>(
                stream: uploadProgressController.stream,
                builder: (context, snapshot) {
                  double progess = (snapshot.data ?? 0.0) / 100;
                  return Column(
                    children: [
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        width: screenWidth,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(
                            Radius.circular(4),
                          ),
                          gradient: LinearGradient(colors: [
                            ColorConstants().gradientLeft(),
                            ColorConstants().gradientRight(),
                          ]),
                        ),
                        child: Align(
                            alignment: isRTL
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                            child: Text('uploading_your_reel_please_wait',
                                    style: Styles.regular(
                                        color: ColorConstants.WHITE))
                                .tr()),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Icon(
                              Icons.file_copy,
                              color: ColorConstants.GREY_3,
                            ),
                            Text('${(progess * 100).toStringAsFixed(0)}%')
                          ],
                        ).reverse(isRTL),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Transform.rotate(
                          angle: isRTL ? -math.pi : 0,
                          child: LinearProgressIndicator(
                            value: progess,
                            color: ColorConstants().gradientRight(),
                          ),
                        ),
                      ),
                    ],
                  );
                }),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          snackPosition: SnackPosition.TOP,
          duration: Duration(minutes: 20),
          isDismissible: false,
          backgroundColor: Colors.transparent,
          borderRadius: 4,
          boxShadows: [
            BoxShadow(
                color: Color(0xff898989).withOpacity(0.1),
                offset: Offset(0, 4.0),
                blurRadius: 11)
          ],
        );

        final homeRepository = Injector.appInstance.get<HomeRepository>();
        homeRepository.CreatePost(
          thumnailUrl,
          2,
          'reels',
          '',
          postDescriptionController.value.text,
          widget.filesPath,
        ).then((CreatePostResponse value) {
          Get.closeAllSnackbars();
          Get.rawSnackbar(
            messageText: Align(
              alignment: isRTL ? Alignment.centerRight : Alignment.centerLeft,
              child: Text(
                value.status == 1
                    ? tr('reel_uploaded')
                    : tr('error__uploading_reel'),
                style: Styles.regular(size: 14),
              ),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
            snackPosition: SnackPosition.TOP,
            backgroundColor: ColorConstants.WHITE,
            borderRadius: 4,
            boxShadows: [
              BoxShadow(
                  color: Color(0xff898989).withOpacity(0.1),
                  offset: Offset(0, 4.0),
                  blurRadius: 11)
            ],
          );
        });
      } catch (e) {
        Get.closeAllSnackbars();
        setState(() {
          isPostedLoading = false;
        });
        Get.rawSnackbar(
          messageText: Text(
            tr('error__uploading_reel'),
            style: Styles.regular(size: 14),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          snackPosition: SnackPosition.TOP,
          backgroundColor: ColorConstants.WHITE,
          borderRadius: 4,
          boxShadows: [
            BoxShadow(
                color: Color(0xff898989).withOpacity(0.1),
                offset: Offset(0, 4.0),
                blurRadius: 11)
          ],
        );
      }

      Navigator.pop(context);
      Navigator.pop(context);
    }
  }

  void _handleCreatePostResponse(
      CreatePostState state, CreatePostProvider provider) {
    var loginState = state;
    setState(() async {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isPostedLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success.................... create post");

          isPostedLoading = false;
          responseData = state.response;
          widget.provider?.clearList();

          if (responseData!.status == 1) {
            if (widget.isReelsPost == true) Navigator.pop(context);
            Navigator.pop(context);
          }

          break;
        case ApiStatus.ERROR:
          isPostedLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<String?> _getImages(CreatePostProvider provider) async {
    try {
      final pickedFileC = await ImagePicker().pickImage(
        source: ImageSource.camera,
        maxWidth: 1920, // Increased resolution for better quality
        maxHeight: 1920,
        imageQuality: 85, // Good balance between quality and file size
      );

      if (pickedFileC != null) {
        // Validate the captured image
        if (await _isValidImageFile(pickedFileC.path)) {
          provider.addToList(pickedFileC.path);
          croppedList = provider.files?.toList();
          Log.v("Successfully captured and added image: ${pickedFileC.path}");
        } else {
          // Provide specific error message
          File capturedFile = File(pickedFileC.path);
          String errorMessage;

          if (!await capturedFile.exists()) {
            errorMessage = 'Camera failed to save the image. Please try again.';
          } else if (await capturedFile.length() == 0) {
            errorMessage = 'Captured image is corrupted. Please try again.';
          } else {
            errorMessage =
                'Captured image format is not supported. Please try again.';
          }

          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ));
          Log.v("Failed to capture valid image: $errorMessage");
        }
      } else {
        Log.v("User cancelled camera capture");
      }
    } catch (e) {
      Log.v("Error during camera capture: $e");
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(
            'Camera error occurred. Please check camera permissions and try again.'),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 3),
      ));
    }
    return null;
  }

  /// Validates if a file is a valid image file with comprehensive checks
  Future<bool> _isValidImageFile(String filePath) async {
    try {
      final File file = File(filePath);
      if (!await file.exists()) {
        Log.v("File does not exist: $filePath");
        return false;
      }

      final int fileSize = await file.length();
      if (fileSize == 0) {
        Log.v("File is empty: $filePath");
        return false;
      }

      // Check if file is too large (over 50MB)
      if (fileSize > 50 * 1024 * 1024) {
        Log.v("File is too large: ${fileSize / (1024 * 1024)}MB");
        return false;
      }

      // Check file extension
      final String extension = filePath.toLowerCase().split('.').last;
      final List<String> validExtensions = [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'bmp',
        'webp'
      ];

      if (!validExtensions.contains(extension)) {
        Log.v("Invalid file extension: $extension");
        return false;
      }

      // Additional validation: try to read the file header to verify it's actually an image
      try {
        final List<int> bytes = await file.readAsBytes();
        if (bytes.length < 10) {
          Log.v("File too small to be a valid image: ${bytes.length} bytes");
          return false;
        }

        // Check for common image file signatures
        if (_hasValidImageSignature(bytes)) {
          return true;
        } else {
          Log.v("File does not have a valid image signature");
          return false;
        }
      } catch (e) {
        Log.v("Error reading file bytes for validation: $e");
        return false;
      }
    } catch (e) {
      Log.v("Error validating image file: $e");
      return false;
    }
  }

  /// Checks if the file has a valid image signature (magic bytes)
  bool _hasValidImageSignature(List<int> bytes) {
    if (bytes.length < 4) return false;

    // JPEG signature: FF D8 FF
    if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return true;
    }

    // PNG signature: 89 50 4E 47 0D 0A 1A 0A
    if (bytes.length >= 8 &&
        bytes[0] == 0x89 &&
        bytes[1] == 0x50 &&
        bytes[2] == 0x4E &&
        bytes[3] == 0x47 &&
        bytes[4] == 0x0D &&
        bytes[5] == 0x0A &&
        bytes[6] == 0x1A &&
        bytes[7] == 0x0A) {
      return true;
    }

    // GIF signature: 47 49 46 38 (GIF8)
    if (bytes[0] == 0x47 &&
        bytes[1] == 0x49 &&
        bytes[2] == 0x46 &&
        bytes[3] == 0x38) {
      return true;
    }

    // BMP signature: 42 4D (BM)
    if (bytes[0] == 0x42 && bytes[1] == 0x4D) {
      return true;
    }

    // WebP signature: 52 49 46 46 ... 57 45 42 50 (RIFF...WEBP)
    if (bytes.length >= 12 &&
        bytes[0] == 0x52 &&
        bytes[1] == 0x49 &&
        bytes[2] == 0x46 &&
        bytes[3] == 0x46 &&
        bytes[8] == 0x57 &&
        bytes[9] == 0x45 &&
        bytes[10] == 0x42 &&
        bytes[11] == 0x50) {
      return true;
    }

    return false;
  }

  void _initFilePiker(CreatePostProvider provider) async {
    if (provider.files!.length > 4) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('only_four_files_are_allowed').tr(),
      ));
      return;
    }

    // Add a small delay to ensure UI is ready
    await Future.delayed(Duration(milliseconds: 100));
    FilePickerResult? result;

    if (Platform.isIOS) {
      result = await FilePicker.platform.pickFiles(
          allowMultiple: true, type: FileType.media, allowedExtensions: []);
    } else {
      result = await FilePicker.platform.pickFiles(
          allowMultiple: true,
          type: FileType.custom,
          onFileLoading: (path) {},
          allowedExtensions: ['jpg', 'jpeg', 'png', 'mp4']);
    }

    if (result != null) {
      for (int i = 0; i < result.paths.length; i++) {
        if (provider.files!.length >= 4) {
          break;
        }

        if (((File(result.paths[i]!).lengthSync() / 1000000) +
                provider.totalMBSize) >
            50.0) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${tr('content_file_size_larger_than')} 50 MB'),
          ));
        } else {
          // Validate image file before adding
          String filePath = result.paths[i]!;
          String fileName = filePath.split('/').last;

          if (await _isValidImageFile(filePath)) {
            provider.addSize(File(filePath).lengthSync() ~/ 1000000);
            provider.addToList(filePath);
            Log.v("Successfully added valid image file: $fileName");
          } else {
            // Provide more specific error messages based on the validation failure
            File file = File(filePath);
            String errorMessage;

            if (!await file.exists()) {
              errorMessage = 'File not found: $fileName';
            } else if (await file.length() == 0) {
              errorMessage = 'File is empty or corrupted: $fileName';
            } else if (await file.length() > 50 * 1024 * 1024) {
              errorMessage = 'File too large (max 50MB): $fileName';
            } else {
              errorMessage = 'Invalid or unsupported image format: $fileName';
            }

            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ));
            Log.v("Rejected invalid image file: $fileName - $errorMessage");
          }
        }

        croppedList = provider.files?.toList();
      }

      if (provider.files!.length >= 4) {
        log('break here 2');

        provider.updateList(
          provider.files?.sublist(provider.files!.length - 4),
        );
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('only_four_files_are_allowed').tr(),
        ));
      }
    }
    //}
  }
}

class ShowReadyToPost extends StatefulWidget {
  final CreatePostProvider? provider;
  final bool isReelPost;
  const ShowReadyToPost({Key? key, this.provider, this.isReelPost = false})
      : super(key: key);

  @override
  _ShowReadyToPostState createState() => _ShowReadyToPostState();
}

class _ShowReadyToPostState extends State<ShowReadyToPost> {
  List<PlatformUiSettings>? buildUiSettings(BuildContext context) {
    return [
      AndroidUiSettings(
          toolbarTitle: '',
          toolbarColor: Colors.black,
          toolbarWidgetColor: Colors.white,
          hideBottomControls: !Platform.isAndroid,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false),
      IOSUiSettings(
        title: '',
      ),
    ];
  }

  @override
  void initState() {
    super.initState();
    setValue();
  }

  void setValue() {
    List<String?>? readyToPost;
    readyToPost = widget.provider!.files;
    croppedList = readyToPost?.toList();
    if (croppedList!.length > 4) {
      log('break here 3');
      croppedList = croppedList!.sublist(0, 4);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('only_four_files_are_allowed').tr(),
      ));
    }
  }

  Future<String> _cropImage(_pickedFile) async {
    if (_pickedFile == null) {
      return _pickedFile;
    }

    try {
      // Validate that the file exists and is accessible
      final File sourceFile = File(_pickedFile);
      if (!await sourceFile.exists()) {
        Log.v("Source file does not exist: $_pickedFile");
        _showCropErrorMessage(
            'Source file not found. Please try selecting the image again.');
        return _pickedFile;
      }

      // Check file size to ensure it's not corrupted
      final int fileSize = await sourceFile.length();
      if (fileSize == 0) {
        Log.v("Source file is empty: $_pickedFile");
        _showCropErrorMessage(
            'Selected file appears to be corrupted. Please try another image.');
        return _pickedFile;
      }

      // Check if file is too large (over 50MB)
      if (fileSize > 50 * 1024 * 1024) {
        Log.v("Source file is too large: ${fileSize / (1024 * 1024)}MB");
        _showCropErrorMessage(
            'Image file is too large. Please select an image smaller than 50MB.');
        return _pickedFile;
      }

      // Prepare a clean source file for cropping
      String sourcePath =
          await _prepareSourceFileForCropping(_pickedFile, sourceFile);

      // Validate the prepared source file
      final File preparedFile = File(sourcePath);
      if (!await preparedFile.exists() || await preparedFile.length() == 0) {
        Log.v("Prepared source file is invalid: $sourcePath");
        _showCropErrorMessage(
            'Failed to prepare image for cropping. Please try again.');
        return _pickedFile;
      }

      CroppedFile? croppedFile;
      await Future.delayed(Duration(milliseconds: 300));

      // Try simple approach first (like working implementations in other files)
      try {
        Log.v(
            "Attempting simple crop approach with original file: $_pickedFile");

        croppedFile = await ImageCropper().cropImage(
          sourcePath: _pickedFile, // Use original file path first
          compressFormat: ImageCompressFormat.jpg,
          compressQuality:
              100, // Use higher quality like other working implementations
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: '',
              toolbarColor: Colors.black,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.original,
              lockAspectRatio: false,
              showCropGrid: true,
              hideBottomControls: true, // Match other working implementations
            ),
            IOSUiSettings(
              title: '',
            ),
          ],
        );

        Log.v("Simple crop approach completed successfully");
      } catch (e) {
        Log.v("Simple crop failed: $e. Trying with prepared file...");

        // Fallback to prepared file approach
        try {
          await Future.delayed(Duration(milliseconds: 200));

          croppedFile = await ImageCropper().cropImage(
            sourcePath: sourcePath,
            compressFormat: ImageCompressFormat.jpg,
            compressQuality: 85,
            maxWidth: 1920,
            maxHeight: 1920,
            uiSettings: [
              AndroidUiSettings(
                toolbarTitle: 'Crop Image',
                toolbarColor: Colors.black,
                toolbarWidgetColor: Colors.white,
                initAspectRatio: CropAspectRatioPreset.original,
                lockAspectRatio: false,
                showCropGrid: true,
                hideBottomControls: false,
                cropFrameColor: Colors.white,
                cropGridColor: Colors.white.withValues(alpha: 0.5),
                cropFrameStrokeWidth: 2,
                cropGridStrokeWidth: 1,
                aspectRatioPresets: [
                  CropAspectRatioPreset.original,
                  CropAspectRatioPreset.square,
                  CropAspectRatioPreset.ratio4x3,
                  CropAspectRatioPreset.ratio16x9
                ],
              ),
              IOSUiSettings(
                title: 'Crop Image',
                aspectRatioPresets: [
                  CropAspectRatioPreset.original,
                  CropAspectRatioPreset.square,
                  CropAspectRatioPreset.ratio4x3,
                  CropAspectRatioPreset.ratio16x9
                ],
              ),
            ],
          );

          Log.v("Prepared file crop approach completed successfully");
        } catch (preparedError) {
          Log.v(
              "Both crop approaches failed. Original error: $e, Prepared error: $preparedError");

          // Final fallback: Try using utility saveTemporarily method
          try {
            Log.v("Attempting final fallback with utility saveTemporarily...");
            String tempPath = await Utility.saveTemporarily(sourceFile);
            await Future.delayed(Duration(milliseconds: 500));

            croppedFile = await ImageCropper().cropImage(
              sourcePath: tempPath,
              compressFormat: ImageCompressFormat.jpg,
              compressQuality: 90,
              uiSettings: [
                AndroidUiSettings(
                  toolbarTitle: '',
                  toolbarColor: Colors.black,
                  toolbarWidgetColor: Colors.white,
                  initAspectRatio: CropAspectRatioPreset.original,
                  lockAspectRatio: false,
                  showCropGrid: false,
                  hideBottomControls: true,
                ),
                IOSUiSettings(
                  title: '',
                ),
              ],
            );

            // Clean up the utility temp file
            try {
              await File(tempPath).delete();
            } catch (cleanupError) {
              Log.v("Failed to cleanup utility temp file: $cleanupError");
            }

            Log.v("Utility fallback approach completed successfully");
          } catch (utilityError) {
            Log.v("All crop approaches failed. Utility error: $utilityError");
            _showCropErrorMessage(
                'Image cropping failed. This might be due to Android storage restrictions or image format. Please try a different image or restart the app.');
          }
        }
      }

      // Clean up temporary source file if we created one
      if (sourcePath != _pickedFile) {
        try {
          await File(sourcePath).delete();
        } catch (e) {
          Log.v("Failed to delete temporary source file: $e");
        }
      }

      if (croppedFile != null) {
        // Validate the cropped file
        final File croppedFileObj = File(croppedFile.path);
        if (await croppedFileObj.exists() &&
            await croppedFileObj.length() > 0) {
          Log.v("Successfully cropped image: ${croppedFile.path}");
          return croppedFile.path;
        } else {
          Log.v("Cropped file is invalid or empty");
          _showCropErrorMessage(
              'Cropped image could not be saved. Please try again.');
        }
      } else {
        Log.v("User cancelled cropping or cropping failed");
        // Don't show error message if user cancelled
      }
    } catch (e) {
      Log.v("Error during image cropping: $e", name: 'ImageCropper');
      _showCropErrorMessage(
          'An unexpected error occurred while cropping the image. Please try again.');
    }

    return _pickedFile;
  }

  /// Prepares the source file for cropping by ensuring it's in a format and location
  /// that UCrop can access reliably
  Future<String> _prepareSourceFileForCropping(
      String originalPath, File sourceFile) async {
    try {
      // Check if the file needs to be copied to a more accessible location
      bool needsCopy = originalPath.contains('.temp') ||
          originalPath.contains('REC') ||
          originalPath.contains('cache/crop_source_') ||
          !originalPath.toLowerCase().endsWith('.jpg') &&
              !originalPath.toLowerCase().endsWith('.jpeg') &&
              !originalPath.toLowerCase().endsWith('.png');

      if (!needsCopy) {
        // File is already in a good format and location
        return originalPath;
      }

      // Create a clean copy in the app's temporary directory
      final Directory tempDir = await getTemporaryDirectory();
      final String fileName =
          'crop_input_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final String newPath = '${tempDir.path}/$fileName';

      // Copy the file
      await sourceFile.copy(newPath);

      // Verify the copy was successful
      final File copiedFile = File(newPath);
      if (await copiedFile.exists() && await copiedFile.length() > 0) {
        Log.v("Successfully prepared source file: $newPath");
        return newPath;
      } else {
        Log.v("Failed to create valid copy of source file");
        return originalPath;
      }
    } catch (e) {
      Log.v("Error preparing source file for cropping: $e");
      return originalPath;
    }
  }

  /// Shows a user-friendly error message for cropping failures
  void _showCropErrorMessage(String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 4),
          action: SnackBarAction(
            label: 'OK',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 10),
      height: MediaQuery.of(context).size.height * 0.5,
      width: double.infinity,
      child: ListView.builder(
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          itemCount: croppedList!.length,
          itemBuilder: (context, index) {
            File? pickedFile =
                (croppedList != null && croppedList![index] != null)
                    ? File(croppedList![index]!)
                    : null;

            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(2),
                child: Stack(children: [
                  Container(
                    color: ColorConstants.GREY,
                    width: MediaQuery.of(context).size.width * 0.8,
                    child: pickedFile != null
                        ? pickedFile.path.contains('.pdf')
                            ? InkWell(child: PDFScreen(path: pickedFile.path))
                            : pickedFile.path.contains('.mp4') ||
                                    pickedFile.path.contains('.mov') ||
                                    pickedFile.path.contains('.hevc') ||
                                    pickedFile.path.contains('.temp') ||
                                    pickedFile.path.contains('.h.265')
                                ? ShowImage(path: pickedFile.path)
                                : Image.file(
                                    pickedFile,
                                    height: 240,
                                    fit: BoxFit.contain,
                                    cacheWidth: 800,
                                    cacheHeight: 600,
                                  )
                        : SizedBox(),
                  ),
                  if (pickedFile?.path.contains('.mp4') == true ||
                      pickedFile?.path.contains('.mov') == true ||
                      pickedFile?.path.contains('.hevc') == true ||
                      pickedFile?.path.contains('.h.265') == true)
                    Positioned.fill(
                        child: Align(
                            alignment: Alignment.center,
                            child: SvgPicture.asset(
                              'assets/images/play_video_icon.svg',
                              height: 50,
                              width: 50,
                              allowDrawingOutsideViewBox: true,
                            ))),
                  Positioned(
                    right: 5,
                    top: 5,
                    child: Container(
                      padding: EdgeInsets.all(2),
                      decoration: BoxDecoration(
                          color: ColorConstants.BLACK, shape: BoxShape.circle),
                      child: IconButton(
                        onPressed: () {
                          AlertsWidget.showCustomDialog(
                              context: context,
                              title: tr('deletePost'),
                              text: tr('confirm_deletion_textone'),
                              icon: 'assets/images/circle_alert_fill.svg',
                              onOkClick: () async {
                                widget.provider?.subSize(
                                    File('${widget.provider?.files?[index]}')
                                            .lengthSync() ~/
                                        1000000);

                                widget.provider!.removeFromList(index);

                                setState(() {
                                  croppedList = widget.provider?.files;
                                  if (widget.isReelPost &&
                                      croppedList?.length == 0) {
                                    Navigator.pop(context);
                                  }
                                });
                              });
                        },
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(),
                        icon: Icon(Icons.delete_forever,
                            size: 18, color: Colors.white),
                      ),
                    ),
                  ),
                  if (!(pickedFile?.path.contains('.mp4') == true ||
                      pickedFile?.path.contains('.mov') == true ||
                      pickedFile?.path.contains('.hevc') == true ||
                      pickedFile?.path.contains('.h.265') == true))
                    Positioned(
                      left: 5,
                      top: 5,
                      child: Container(
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: ColorConstants.BLACK,
                            shape: BoxShape.circle),
                        child: IconButton(
                          onPressed: () {
                            AlertsWidget.showCustomDialog(
                                context: context,
                                title: "",
                                text: tr('want_to_crop'),
                                icon: 'assets/images/circle_alert_fill.svg',
                                oKText: tr('ok'),
                                onOkClick: () async {
                                  String croppedPath = await _cropImage(
                                      widget.provider!.files?[index]);
                                  if (widget.provider!.files?[index] !=
                                      croppedPath) {
                                    // Update both the provider and the local list
                                    widget.provider!
                                        .updateAtIndex(croppedPath, index);
                                    setState(() {
                                      croppedList![index] = croppedPath;
                                    });
                                  } else {
                                    // If cropping failed, show option to skip cropping
                                    _showSkipCroppingDialog(index);
                                  }
                                });
                          },
                          padding: EdgeInsets.zero,
                          constraints: BoxConstraints(),
                          icon: Icon(Icons.crop, size: 15, color: Colors.white),
                        ),
                      ),
                    ),
                ]),
              ),
            );
          }),
    );
  }

  /// Shows a dialog asking user if they want to skip cropping when it fails
  void _showSkipCroppingDialog(int index) {
    if (context.mounted) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Cropping Failed'),
            content: Text(
                'Image cropping failed due to technical issues. Would you like to use the original image without cropping?'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Keep the original image without cropping
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Using original image without cropping'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                child: Text('Use Original'),
              ),
            ],
          );
        },
      );
    }
  }
}

class ShowImage extends StatelessWidget {
  final String? path;
  ShowImage({Key? key, this.path}) : super(key: key);
  Future<File> writeToFile(Uint8List data) async {
    final directory = await getTemporaryDirectory();
    final file = File('${directory.path}/example.jpg');
    await file.writeAsBytes(data);
    return file;
  }

  Future<Uint8List?> getFile() async {
    final uint8list = await Thumbnail.VideoThumbnail.thumbnailData(
      video: path!,
      imageFormat: Thumbnail.ImageFormat.JPEG,
      quality: 10,
    );
    File file = await writeToFile(uint8list!);
    thumnailUrl = file.path;

    return uint8list;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
      future: getFile(), // async work
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasData) {
          return Image.memory(
            snapshot.data,
            fit: BoxFit.cover,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
          );
        }

        return Shimmer.fromColors(
          baseColor: Color(0xffe6e4e6),
          highlightColor: Color(0xffeaf0f3),
          child: Container(
              height: 400,
              margin: EdgeInsets.only(left: 2),
              width: 150,
              decoration: BoxDecoration(
                color: Colors.white,
              )),
        );
      },
    );
  }
}

extension on Row {
  Widget reverse(bool isReverse) {
    return Row(
        mainAxisAlignment: mainAxisAlignment,
        children: isReverse ? children.reversed.toList() : children);
  }
}
