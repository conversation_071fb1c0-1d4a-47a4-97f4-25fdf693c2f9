import 'dart:convert';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
//import 'package:custom_widget_marquee/custom_widget_marquee.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:marqueer/marqueer.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/wow_dashboard_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/rounded_appbar.dart';
import 'package:masterg/pages/explore_job/explore_job_list_page.dart';
import 'package:masterg/pages/singularis/app_drawer_page.dart';
import 'package:masterg/pages/singularis/job/my_job_all_view_list_page.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
import 'package:masterg/utils/Log.dart';

import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:photo_view/photo_view.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../bots/set_goal_bot/bot_screen.dart';
import '../../bots/set_goal_bot/dialogFlow_Bot.dart';
import '../../data/models/response/auth_response/oraganization_program_resp.dart';
import '../../utils/custom_outline_button.dart';
import '../../utils/resource/size_constants.dart';
import '../ghome/my_assessments.dart';
import '../onboarding_pages/onboarding_select_intreset.dart';
import '../singularis/dashboard/skill/set_goal_intreset_area_page.dart';
import '../singularis/dashboard/skill/skill_child_card_page.dart';
import '../singularis/job/job_dashboard_page.dart';

class SingualrisWowDashboard extends StatefulWidget {
  const SingualrisWowDashboard({Key? key}) : super(key: key);

  @override
  _SingualrisWowDashboardState createState() => _SingualrisWowDashboardState();
}

class _SingualrisWowDashboardState extends State<SingualrisWowDashboard>
    with TickerProviderStateMixin {
  var _scaffoldKey = new GlobalKey<ScaffoldState>();
  MenuListProvider? menuProvider;

  WowDashboardResponse? wowDashboardDetails;
  bool? isLoading = true;
  Box? box;
  //CompetitionResponse? myJobResponse;
  bool? myJobLoading = true;

  bool? competitionDetailLoading = true;
  bool? domainListLoading = true;
  bool? jobApplyLoading = true;
  List<OragnizationProgram>? response;
  List<OragnizationProgram>? displayInterestList = [];

  int? programId;
  int selectedIndex = 0;
  String seletedIds = '';
  String domainId = '';
  List<int> selectedIdList = <int>[];
  int? applied;

  bool myJobRecall = false;
  double maxX = 0;
  double maxY = 0;
  double minX = 2090;
  double minY = 2090;
  final List<Color> gradientColors = [
    Color(0xff3EBDA0),
    Color.fromARGB(255, 169, 214, 204),
  ];
  Map<int, List<dynamic>>? data;
  String? selectedCategoryName;
  List<Skill>? selectedSkills;
  int skillTabSelection = 0;
  late MarqueerController marqeeController;
  @override
  void initState() {
    super.initState();
    marqeeController = MarqueerController();
    getWowDashboardDetails();
    _getInterestPrograms();
  }

  @override
  void dispose() {
    marqeeController.stop();
    super.dispose();
  }

  void getWowDashboardDetails() {
    BlocProvider.of<HomeBloc>(context).add(WowDashboardEvent());
  }

  void _getInterestPrograms() {
    BlocProvider.of<HomeBloc>(context).add(OrganizationProgramListEvent(0));
  }

  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: Consumer<MenuListProvider>(
            builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
                listener: (context, state) async {
                  if (state is WowDashboardState) {
                    _handleWowDashboardDetails(state);
                  }
                  if (state is OrganizationProgramListState) {
                    _handleGetInterestPrograms(state);
                  }

                  setState(() {
                    menuProvider = mp;
                  });
                },
                child: Scaffold(
                  backgroundColor: ColorConstants.DASHBOARD_BG_COLOR,
                  key: _scaffoldKey,
                  endDrawer: new AppDrawer(),
                  body: SingleChildScrollView(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                        RoundedAppBar(
                            appBarHeight: height(context) *
                                (Utility().isRTL(context) ? 0.18 : 0.16),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      /*SizedBox(
                                              height: 5.0,
                                            ),*/
                                      Row(
                                        children: [
                                          InkWell(
                                            onTap: () async {
                                              if (Preference.getString(
                                                          Preference.ROLE) ==
                                                      'Learner' ||
                                                  Preference.getString(
                                                          Preference.ROLE) ==
                                                      'Lead' ||
                                                  Preference.getString(
                                                          Preference.ROLE) ==
                                                      'Alumni') {
                                                Navigator.push(
                                                        context,
                                                        NextPageRoute(
                                                            NewPortfolioPage()))
                                                    .then((value) {
                                                  if (value != null)
                                                    menuProvider
                                                        ?.updateCurrentIndex(
                                                            value);
                                                });
                                              } else {
                                                Navigator.push(
                                                    context,
                                                    NextPageRoute(
                                                        NewPortfolioPage(
                                                      expJobResume: false,
                                                    ))).then((value) {
                                                  if (value != null)
                                                    menuProvider
                                                        ?.updateCurrentIndex(
                                                            value);
                                                });
                                                /*if (APK_DETAILS[
                                                        "package_name"] ==
                                                    "com.singulariswow.mec") {
                                                  Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                          builder: (context) =>
                                                              TermsAndCondition(
                                                                url: 'https://mecfuture.mec.edu.om/hris/my-profile?user_id=' +
                                                                    Preference.getInt(
                                                                            Preference.USER_ID)
                                                                        .toString(),
                                                                title: tr(
                                                                    'my_profile'),
                                                              ),
                                                          maintainState:
                                                              false));
                                                } else {
                                                  Navigator.push(
                                                          context,
                                                          NextPageRoute(
                                                              NewPortfolioPage()))
                                                      .then((value) {
                                                    if (value != null)
                                                      menuProvider
                                                          ?.updateCurrentIndex(
                                                              value);
                                                  });
                                                }*/
                                              }
                                            },
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(200),
                                              child: SizedBox(
                                                width: 50,
                                                height: 50,
                                                child: CachedNetworkImage(
                                                  imageUrl:
                                                      '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                                  fit: BoxFit.cover,
                                                  placeholder: (context, url) =>
                                                      SvgPicture.asset(
                                                    'assets/images/default_user.svg',
                                                    width: 50,
                                                    height: 50,
                                                  ),
                                                  errorWidget:
                                                      (context, url, error) =>
                                                          SvgPicture.asset(
                                                    'assets/images/default_user.svg',
                                                    width: 50,
                                                    height: 50,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      Spacer(),
                                      Padding(
                                        padding: Utility().isRTL(context)
                                            ? EdgeInsets.only(
                                                top: 18.0, left: 6)
                                            : EdgeInsets.only(top: 8, right: 6),
                                        child: SizedBox(
                                          // flex: 2,
                                          child: Align(
                                            alignment: Utility().isRTL(context)
                                                ? Alignment.topLeft
                                                : Alignment.topRight,
                                            child: InkWell(
                                              onTap: () {
                                                _scaffoldKey.currentState
                                                    ?.openEndDrawer();
                                              },
                                              child: SvgPicture.asset(
                                                  'assets/images/hamburger_menu.svg'),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 12),
                                ],
                              ),
                            )),
                        SizedBox(
                          height: height(context) * 0.02,
                        ),
                        Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Text('welcome_msg',
                                    style: Styles.regular(size: 14))
                                .tr()
                            //  _searchFilter(),
                            ),
                        Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: SvgPicture.asset(
                              'assets/images/singualris_wow_head.svg'),
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        // myJobResponse?.data != null ? _myJobSectionCard() : SizedBox(),
                        //TODO: Show Set Goal
                        APK_DETAILS["set_goal"] == "1"
                            ? _setGoalsAssessment()
                            : SizedBox(),

                        //TODO: Other Widget
                        wowDashboardDetails?.data == null
                            ? DashboardBlankPage()
                            : isLoading != false
                                ? Center(
                                    //child: CircularProgressIndicator.adaptive()
                                    child: Text('no_data').tr(),
                                  )
                                : Column(
                                    children: [
                                      //TODO: Skill Widget
                                      Preference.getString(Preference.ROLE) ==
                                                  'Learner' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Alumni' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Lead'
                                          ? Padding(
                                              padding: const EdgeInsets.all(10),
                                              child: renderSkillData(),
                                            )
                                          : SizedBox(),
                                      SizedBox(
                                        height: 8,
                                      ),

                                      Preference.getString(Preference.ROLE) ==
                                                  'Learner' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Alumni' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Lead'
                                          ? myJobCard()
                                          : SizedBox(),
                                      Preference.getString(Preference.ROLE) ==
                                                  'Learner' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Alumni' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Lead'
                                          ? SizedBox(height: 15)
                                          : SizedBox(),

                                      ///World of Word
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      TermsAndCondition(
                                                        url: '${APK_DETAILS['wow-dashboard']}' +
                                                            Preference.getInt(
                                                                    Preference
                                                                        .USER_ID)
                                                                .toString() +
                                                            '&is_webview=1',
                                                        title:
                                                            tr('world_of_work'),
                                                      ),
                                                  maintainState: false));
                                        },
                                        child: wowCard(),
                                      ),
                                      SizedBox(height: 15),

                                      ///Job Opportunity
                                      Preference.getString(Preference.ROLE) ==
                                                  'Learner' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Alumni' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Lead'
                                          ? GestureDetector(
                                              onTap: () {
                                                Navigator.push(
                                                    context,
                                                    NextPageRoute(
                                                        JobDashboardPage(
                                                            myJobEnable: false),
                                                        isMaintainState: true));
                                              },
                                              child: jobOpportunities(),
                                            )
                                          : SizedBox(),
                                      Preference.getString(Preference.ROLE) ==
                                                  'Learner' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Alumni' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Lead'
                                          ? SizedBox(height: 15)
                                          : SizedBox(),

                                      ///Smart Job Matching
                                      Preference.getString(Preference.ROLE) ==
                                                  'Learner' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Alumni' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Lead'
                                          ? GestureDetector(
                                              onTap: () {
                                                Navigator.push(
                                                    context,
                                                    NextPageRoute(
                                                        ExploreJobListPage(
                                                          //indexNo: null,
                                                          indexNo: 0,
                                                        ),
                                                        isMaintainState: true));
                                              },
                                              child: smartJobMatching(),
                                            )
                                          : SizedBox(),

                                      //TODO: Knowing Yourself: Insights
                                      Preference.getString(Preference.ROLE) ==
                                                  'Learner' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Alumni' ||
                                              Preference.getString(
                                                      Preference.ROLE) ==
                                                  'Lead'
                                          ? knowingYourSelfInsights()
                                          : SizedBox(),

                                      SizedBox(
                                        height: 18,
                                      ),
                                      _highLightsCard(
                                          ColorConstants.ORANGE,
                                          tr('build_portfolio'),
                                          tr('build_portfolio_text'),
                                          'build_portfolio'),
                                      SizedBox(height: 30),
                                    ],
                                  )
                      ])),
                ))));
  }

  void _handleWowDashboardDetails(WowDashboardState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................WowDashboardDetails.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................WowDashboardDetails");
            wowDashboardDetails = state.response;

            ///This is for graph
            // final Map<String, dynamic>? jobVacanciesGraph = wowDashboardDetails?.data?.jobVacanciesGraph;
            // if (jobVacanciesGraph != null) {
            //   data = jobVacanciesGraph.map((key, value) {
            //     return MapEntry(int.parse(key), List<dynamic>.from(value));
            //   });
            // }

            ///This is for graph
            // final Map<String, dynamic>? jobVacanciesGraph1 = wowDashboardDetails?.data?.jobVacanciesGraph;
            // if (jobVacanciesGraph1 != null) {
            //   final List<dynamic> data = jobVacanciesGraph1.values.toList();
            //   setState(() {
            //     _spots = data.map((entry) {
            //       return FlSpot(double.parse(jobVacanciesGraph1.keys.elementAt(data.indexOf(entry))), double.parse(entry[0].toString().substring(0, 1)));
            //     }).toList();
            //   });
            // }
            // Log.v('_spots---${_spots}');

            Preference.setInt(Preference.RESUME_PARSER_DATA_COUNT,
                wowDashboardDetails!.data!.matchingProfile!.matchingJobs!);
            final userSkillAssessmentMap =
                wowDashboardDetails?.data?.userSkillAssessment;
            if (userSkillAssessmentMap != null &&
                userSkillAssessmentMap.isNotEmpty) {
              final firstCategory = userSkillAssessmentMap.values.first;
              setState(() {
                selectedCategoryName = firstCategory.categoryName;
                selectedSkills = firstCategory.skills;
              });
            }
            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error............WowDashboardDetails");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void _handleGetInterestPrograms(OrganizationProgramListState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................OrganizationProgramListState.");
            //isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................OrganizationProgramListState");
            response = state.response?.data;
            displayInterestList = response;

            if (displayInterestList?.length != 0) {
              var selectedCategoryIds = '';
              String selectedCategoryNames = '';
              displayInterestList?.forEach((element) {
                if ((int.tryParse('${element.isMapped}') ?? 0) > 0) {
                  selectedCategoryIds += element.id.toString() + ',';
                  selectedCategoryNames +=
                      element.interestarea.toString() + ',';
                }
              });

              if (selectedCategoryNames.isNotEmpty) {
                /*Preference.setString(
                    Preference.SETUP_GOAL,
                    selectedCategoryNames.substring(
                        0, selectedCategoryNames.lastIndexOf(",")));*/ //Hide for other bot you use intrest area selection then uncoment  code
                Preference.setString(
                    Preference.SETUP_GOAL_INTEREST_ID,
                    selectedCategoryIds.substring(
                        0, selectedCategoryIds.lastIndexOf(",")));
              }
            }

            Log.v('displayInterestList===$displayInterestList');

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error............OrganizationProgramListState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  // void _handlecompetitionListResponse(JobCompListState state) {
  //   var jobCompState = state;
  //   setState(() {
  //     switch (jobCompState.apiState) {
  //       case ApiStatus.LOADING:
  //         Log.v("Loading....................");
  //         myJobLoading = true;
  //         break;
  //       case ApiStatus.SUCCESS:
  //         Log.v("CompetitionState....................");
  //         if (myJobRecall == false) {
  //           myJobResponse = state.myJobListResponse;
  //         } else {
  //           myJobResponse = state.myJobListResponse;
  //         }

  //         myJobLoading = false;
  //         break;
  //       case ApiStatus.ERROR:
  //         Log.v(
  //             "Error CompetitionListIDState .....................${jobCompState.error}");
  //         myJobLoading = false;
  //         FirebaseAnalytics.instance
  //             .logEvent(name: 'job_dashboard', parameters: {
  //           "ERROR": '${jobCompState.error}',
  //         });

  //         break;
  //       case ApiStatus.INITIAL:
  //         break;
  //     }
  //   });
  // }

  ///All Widget---------

  Widget _setGoalsAssessment() {
    return Container(
      margin: EdgeInsets.only(top: 10, left: 10.0, right: 10.0, bottom: 20.0),
      height: 100,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(6)),
        color: ColorConstants.ACCENT_COLOR,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                SizedBox(
                  height: 20,
                  width: 20,
                  //child: SvgPicture.asset('assets/images/s_careers.svg')
                  child: Icon(Icons.bar_chart),
                ),
                SizedBox(width: 10),
                Text(
                  'career_fitment_evalution',
                  style: Styles.bold(color: Color(0xff0E1638)),
                ).tr(),
              ],
            ),
          ),
          Row(
            children: [
              Preference.getString(Preference.SETUP_GOAL) == '' ||
                      Preference.getString(Preference.SETUP_GOAL) == null
                  ? Padding(
                      padding: const EdgeInsets.only(left: 10.0),
                      child: ElevatedButton(
                        style: ButtonStyle(
                          backgroundColor:
                              APK_DETAILS['package_name'] == 'com.singulariswow'
                                  ? MaterialStateProperty.all(
                                      ColorConstants.WOW_PRIMARY_COLOR)
                                  : MaterialStateProperty.all(
                                      ColorConstants.PRIMARY_COLOR),
                          shape: MaterialStateProperty.all(
                              RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30))),
                        ),
                        child: SizedBox(
                          width: 110,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text('set_your_goal').tr(),
                              SizedBox(width: 2),
                              Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: 18,
                              ),
                            ],
                          ),
                        ),
                        onPressed: () {
                          FirebaseAnalytics.instance
                              .setCurrentScreen(screenName: 'Dashboard_Screen');
                          FirebaseAnalytics.instance.logEvent(
                              name:
                                  'user_id-${Preference.getInt(Preference.USER_ID)}',
                              parameters: {
                                "set_goal_event": 'Set_Your_Goal',
                              });
                          /*Navigator.push(context, NextPageRoute(DialogFlowBot())).then((value){
                   setState(() {});
                   //_getHomeData();
                  });*/
                          if (APK_DETAILS['package_name'] ==
                              'com.singulariswow_non') {
                            Navigator.push(
                              context,
                              NextPageRoute(
                                BotScreen(fromDashboard: true),
                              ),
                            ).then((value) {
                              setState(() {});
                            });
                          } else {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        SetGoalIntresetAreaPage(
                                          singleSelection: false,
                                          returnValue: true,
                                          fetchGoalList: 1,
                                        ))).then((value) {
                              setState(() {
                                Preference.setString(
                                    Preference.SETUP_GOAL, '${value[0]}');
                                Preference.setString(
                                    Preference.SETUP_GOAL_INTEREST_ID,
                                    '${value[1]}');
                              });
                            });
                          }

                          /*Navigator.push(
                        context,
                        NextPageRoute(BotScreen(
                          fromDashboard: true,
                        ))).then((value) {
                      setState(() {});
                      //_getHomeData();
                    });*/
                        },
                      ),
                    )
                  : Container(
                      margin: EdgeInsets.symmetric(horizontal: 4),
                      child: GestureDetector(
                        onTap: () {
                          /* Navigator.push(context, NextPageRoute(DialogFlowBot())).then((value){
                      setState(() {});
                      });*/

                          FirebaseAnalytics.instance
                              .setCurrentScreen(screenName: 'Dashboard_Screen');
                          FirebaseAnalytics.instance.logEvent(
                              name:
                                  'user_id-${Preference.getInt(Preference.USER_ID)}',
                              parameters: {
                                "set_goal_event": 'Set_Your_Goal_Update',
                              });

                          if (APK_DETAILS['package_name'] ==
                              'com.singulariswow_non') {
                            Navigator.push(
                              context,
                              NextPageRoute(
                                BotScreen(fromDashboard: true),
                              ),
                            ).then((value) {
                              setState(() {});
                            });
                          } else {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        SetGoalIntresetAreaPage(
                                          singleSelection: false,
                                          returnValue: true,
                                          fetchGoalList: 1,
                                        ))).then((value) {
                              setState(() {
                                Preference.setString(
                                    Preference.SETUP_GOAL, '${value[0]}');
                                Preference.setString(
                                    Preference.SETUP_GOAL_INTEREST_ID,
                                    '${value[1]}');
                              });
                            });
                          }
                        },
                        child: Row(
                          children: [
                            SizedBox(
                              width: int.parse(
                                          '${Preference.getString(Preference.SETUP_GOAL)?.length}') >
                                      27
                                  ? null
                                  : 150,
                              child: Text(
                                Preference.getString(Preference.SETUP_GOAL) !=
                                            'null' &&
                                        Preference.getString(
                                                Preference.SETUP_GOAL) !=
                                            null
                                    ? '${Preference.getString(Preference.SETUP_GOAL) ?? 'set_your_goal'}'
                                    : 'set_your_goal',
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                style: Styles.regularUnderline(
                                    lineHeight: 1,
                                    size: 12,
                                    color: ColorConstants.BLACK),
                              ).tr(),
                            ),
                            // SizedBox(width: 5),
                            Icon(
                              Icons.edit,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
              //if (Preference.getString(Preference.SETUP_GOAL)!.isNotEmpty && Preference.getString(Preference.SETUP_GOAL) != null) ...[
              if (Preference.getString(Preference.SETUP_GOAL)?.isNotEmpty ??
                  false) ...[
                _assessInterestFitment(),
              ],
            ],
          ),
        ],
      ),
    );
  }

  _assessInterestFitment() {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(right: 8),
        height: 35,
        child: CustomOutlineButton(
          strokeWidth: 1,
          radius: 50,
          gradient: LinearGradient(
            colors: [
              ColorConstants().gradientLeft(),
              ColorConstants().gradientRight()
            ],
            begin: Alignment.topLeft,
            end: Alignment.topRight,
          ),
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0, right: 10.0),
            child: SizedBox(
              width: width(context) * 0.42,
              child: GradientText(
                '${tr('assess_interest_fitment')}',
                style: Styles.regular(size: 12),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                colors: [
                  ColorConstants().gradientLeft(),
                  ColorConstants().gradientRight(),
                ],
              ),
            ),
          ),
          onPressed: () {
            FirebaseAnalytics.instance
                .setCurrentScreen(screenName: 'Dashboard_Screen');
            FirebaseAnalytics.instance.logEvent(
                name: 'user_id-${Preference.getInt(Preference.USER_ID)}',
                parameters: {
                  "set_goal_event": 'Evaluate_Suitability_Interest',
                });

            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => MyAssessmentPage(
                          interestAreaID:
                              '${Preference.getString(Preference.SETUP_GOAL_INTEREST_ID)}',
                        )));
          },
        ),
      ),
    );
  }

  myJobCard() {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 12),
        height: MediaQuery.of(context).size.height * 0.15,
        decoration: BoxDecoration(
          color: ColorConstants.WHITE,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('my_job',
                          style: Styles.bold(
                              size: 18, color: ColorConstants.HEADING_TITLE))
                      .tr(),
                  Spacer(),
                  InkWell(
                    onTap: () {
                      Navigator.push(
                              context, NextPageRoute(MyJobAllViewListPage()))
                          .then((value) {
                        //getMyJobList(false);
                        //getDomainList();
                      });
                    },
                    child: Container(
                      height: 30,
                      width: 30,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(colors: [
                          ColorConstants().gradientLeft(),
                          ColorConstants().gradientRight(),
                        ]),
                        // color: APK_DETAILS['package_name'] == 'com.singulariswow'?  ColorConstants.WOW_PRIMARY_COLOR:ColorConstants.PRIMARY_BLUE,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(Icons.arrow_forward,
                          size: 15, color: ColorConstants.WHITE),
                    ),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: Text(
                  'my_job_card_desc',
                  style: Styles.regular(size: 14),
                ).tr(),
              )
            ],
          ),
        ));
  }

  loadShimmer() {
    return SingleChildScrollView(
      child: Column(children: [
        Container(
          margin: EdgeInsets.symmetric(horizontal: 12),
          height: MediaQuery.of(context).size.height * 0.15,
          width: double.infinity,
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            enabled: true,
            child: Container(
              margin: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                  color: ColorConstants.WHITE,
                  borderRadius: BorderRadius.circular(20)),
              width: 100,
              height: 13,
            ),
          ),
        ),
        SizedBox(height: 15),
        Container(
          // height: 200,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: 3,
            itemBuilder: (context, index) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 12),
                height: MediaQuery.of(context).size.height * 0.40,
                width: double.infinity,
                child: Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  enabled: true,
                  child: Container(
                    margin: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: ColorConstants.WHITE,
                        borderRadius: BorderRadius.circular(20)),
                    width: 100,
                    height: 13,
                  ),
                ),
              );
            },
          ),
        ),
        SizedBox(height: 15),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 12),
          height: MediaQuery.of(context).size.height * 0.17,
          width: double.infinity,
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            enabled: true,
            child: Container(
              margin: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                  color: ColorConstants.WHITE,
                  borderRadius: BorderRadius.circular(20)),
              width: 100,
              height: 13,
            ),
          ),
        ),
      ]),
    );
  }

  wowCard() {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 12),
        height: MediaQuery.of(context).size.height * 0.49,
        width: double.infinity,
        decoration: BoxDecoration(
          color: ColorConstants.ACCENT_COLOR,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('world_of_work',
                          style: Styles.bold(
                              size: 18, color: ColorConstants.HEADING_TITLE))
                      .tr(),
                  Spacer(),
                  Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(colors: [
                        ColorConstants().gradientLeft(),
                        ColorConstants().gradientRight(),
                      ]),

                      // color: APK_DETAILS['package_name'] == 'com.singulariswow'?  ColorConstants.WOW_PRIMARY_COLOR:ColorConstants.PRIMARY_BLUE,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.arrow_forward,
                      size: 15,
                      color: ColorConstants.WHITE,
                    ),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: Text(
                  'wow_card_desc',
                  style: Styles.regular(size: 14),
                ).tr(),
              ),
              SizedBox(height: 15),
              RichText(
                text: TextSpan(
                  style: TextStyle(
                    fontSize: 20.0,
                    color: Colors.black,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                        text: tr('job_vacancies'),
                        style: Styles.regular(color: ColorConstants.GREY_2)),
                    TextSpan(
                      text:
                          ' : ${wowDashboardDetails?.data?.wowDetails?.jobVacancies ?? 0}',
                      style: Styles.bold(color: ColorConstants.HEADING_TITLE),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20),

              splineGraph()
              // graphCard2()
            ],
          ),
        ));
  }

  jobOpportunities() {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 12),
        height: MediaQuery.of(context).size.height * 0.33,
        width: double.infinity,
        decoration: BoxDecoration(
          color: ColorConstants.ACCENT_COLOR,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('job_opportunities',
                          style: Styles.bold(
                              size: 18, color: ColorConstants.HEADING_TITLE))
                      .tr(),
                  Spacer(),
                  Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(colors: [
                        ColorConstants().gradientLeft(),
                        ColorConstants().gradientRight(),
                      ]),
                      // color: APK_DETAILS['package_name'] == 'com.singulariswow'?  ColorConstants.WOW_PRIMARY_COLOR:ColorConstants.PRIMARY_BLUE,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.arrow_forward,
                      size: 15,
                      color: ColorConstants.WHITE,
                    ),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: Text(
                  'job_opportunities_desc',
                  style: Styles.regular(size: 14),
                ).tr(),
              ),
              SizedBox(height: 30),
              SizedBox(
                height: 120,
                child: Marqueer.builder(
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.all(8),
                      child: Column(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.all(Radius.circular(4)),
                            child: SizedBox(
                              width: 100,
                              height: 80,
                              child: Image.network(
                                "${wowDashboardDetails?.data?.jobOpportunities?[index].image ?? ''}",
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                  itemCount:
                      wowDashboardDetails?.data?.jobOpportunities?.length ?? 0,
                  controller: marqeeController,

                  //directionOption: DirectionOption.twoDirection,
                ),
              ),
            ],
          ),
        ));
  }

  smartJobMatching() {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 12),
        height: MediaQuery.of(context).size.height * 0.3,
        decoration: BoxDecoration(
          color: ColorConstants.ACCENT_COLOR,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('smart_job_matching',
                          style: Styles.bold(
                              size: 18, color: ColorConstants.HEADING_TITLE))
                      .tr(),
                  Spacer(),
                  Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(colors: [
                        ColorConstants().gradientLeft(),
                        ColorConstants().gradientRight(),
                      ]),
                      // color: APK_DETAILS['package_name'] == 'com.singulariswow'?  ColorConstants.WOW_PRIMARY_COLOR:ColorConstants.PRIMARY_BLUE,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.arrow_forward,
                      size: 15,
                      color: ColorConstants.WHITE,
                    ),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: Text(
                  'smart_job_matching_desc',
                  style: Styles.regular(size: 14),
                ).tr(),
              ),
              SizedBox(height: 5),
              Container(
                  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //'${tr('complete_your_profile')}-${wowDashboardDetails?.data?.matchingProfile?.profileCompletion?.toStringAsFixed(2)}%',
                      SizedBox(
                        height: 20,
                      ),
                      Row(
                        children: [
                          Text(
                            '${wowDashboardDetails?.data?.matchingProfile?.matchingJobs}+ ${tr('Jobs')}',
                            style: Styles.bold(size: 16),
                          ).tr(),
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              'job_matching_to',
                              style: Styles.regular(size: 16),
                            ).tr(),
                          ),
                        ],
                      ),

                      Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 20.0),
                            child: Text(
                              '${tr('complete_your_profile')} :',
                              style: Styles.regular(),
                            ).tr(),
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.only(left: 3.0, top: 10.0),
                            child: Text(
                              '${wowDashboardDetails?.data?.matchingProfile?.profileCompletion}%',
                              style: Styles.bold(size: 20),
                            ).tr(),
                          ),
                        ],
                      ),
                      SizedBox(height: 5),
                      ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(30)),
                        child: LinearProgressIndicator(
                          minHeight: 6,
                          value: (wowDashboardDetails?.data?.matchingProfile
                                      ?.profileCompletion ??
                                  0) /
                              100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            ColorConstants.GREEN,
                          ),
                        ),
                      ),
                    ],
                  ))
            ],
          ),
        ));
  }

  //TODO: Gain Skill Showing
  renderSkillData() {
    return Container(
      decoration: BoxDecoration(
        color: ColorConstants.WHITE,
        borderRadius: BorderRadius.all(Radius.circular(8.0)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                  padding: Utility().isRTL(context)
                      ? EdgeInsets.only(right: 15.0)
                      : EdgeInsets.only(left: 15.0),
                  child: Icon(Icons.trending_up_outlined, size: 18)),
              Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 10,
                  ),
                  child: Text(
                    'gain_skill',
                    style: Styles.bold(
                        size: 14, color: ColorConstants.HEADING_PRIMARY_COLOR),
                  ).tr()),
            ],
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 3),
            child: Container(
              height: 45,
              child: ListView.builder(
                itemCount:
                    wowDashboardDetails?.data?.userSkillAssessment?.length ?? 0,
                scrollDirection: Axis.horizontal,
                itemBuilder: (BuildContext context, int index) {
                  Map<String, UserSkillAssessmentWOW>? userSkillAssessmentMap =
                      wowDashboardDetails?.data?.userSkillAssessment;

                  if (userSkillAssessmentMap != null) {
                    final userSkillAssessmentList =
                        userSkillAssessmentMap.values.toList();
                    UserSkillAssessmentWOW userSkillAssessment =
                        userSkillAssessmentList[index];
                    String categoryName =
                        userSkillAssessment.categoryName ?? 'Unknown Category';
                    List<Skill> skills = userSkillAssessment.skills;

                    return InkWell(
                      onTap: () {
                        // When a category is clicked, update the selected category name and skills
                        setState(() {
                          selectedCategoryName = categoryName;
                          selectedSkills = skills;
                          skillTabSelection = index;
                        });
                      },
                      child: Container(
                        width:
                            min(MediaQuery.of(context).size.width, 280) * 0.4,
                        decoration: BoxDecoration(
                          color: skillTabSelection == index
                              ? ColorConstants.PRIMARY_COLOR
                              : Colors.blue.withOpacity(0.08),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        margin: EdgeInsets.all(4),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Text(
                              categoryName,
                              style: TextStyle(
                                color: skillTabSelection == index
                                    ? Colors.white
                                    : Colors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                              softWrap: true,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                      ),
                    );
                  }

                  // Return an empty container if the map is null
                  return Container();
                },
              ),
            ),
          ),

          Divider(),

          // Skills List (Vertical ListView.builder)
          Container(
            padding: EdgeInsets.only(bottom: 5),
            child: selectedSkills != null
                ? ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: selectedSkills!.length,
                    itemBuilder: (BuildContext context, int skillIndex) {
                      Skill skill = selectedSkills![skillIndex];

                      return SkillChildCard(
                        skillId: skill.skillId,
                        skill: skill.name,
                        level: skill.userWeightageLabel,
                        weightagePerNo: skill.userWeightagePerNo,
                      );
                    },
                  )
                : Center(
                    child: Text(
                      selectedCategoryName != null
                          ? tr('no_skills_available') + ' $selectedCategoryName'
                          : tr('select_category_view_skill'),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  //TODO: Knowing Yourself: Insights
  knowingYourSelfInsights() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => TermsAndCondition(
                      url: '${APK_DETAILS['personality']}' +
                          Preference.getInt(Preference.USER_ID).toString() +
                          '&is_webview=1',
                      title: tr('knowing_myself'),
                    ),
                maintainState: false));
      },
      child: Container(
          margin: EdgeInsets.only(left: 15.0, right: 15.0, top: 15.0),
          height: MediaQuery.of(context).size.height * 0.2,
          decoration: BoxDecoration(
            color: ColorConstants.ACCENT_COLOR,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text('knowing_yourself_insights',
                            style: Styles.bold(
                                size: 18, color: ColorConstants.HEADING_TITLE))
                        .tr(),
                    Spacer(),
                    Container(
                      height: 30,
                      width: 30,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(colors: [
                          ColorConstants().gradientLeft(),
                          ColorConstants().gradientRight(),
                        ]),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        Icons.arrow_forward,
                        size: 15,
                        color: ColorConstants.WHITE,
                      ),
                    )
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: Text(
                    'knowing_myself_des',
                    style: Styles.regular(size: 14),
                  ).tr(),
                ),
                SizedBox(height: 5),
              ],
            ),
          )),
    );
  }

  Widget _highLightsCard(
      Color colorBg, String strTitle, String strDes, String clickType) {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 6),
      margin: const EdgeInsets.only(
          left: SizeConstants.JOB_LEFT_SCREEN_MGN,
          right: SizeConstants.JOB_RIGHT_SCREEN_MGN),
      width: double.infinity,
      child: InkWell(
        onTap: () {
          if (clickType == 'build_portfolio') {
            FirebaseAnalytics.instance
                .logEvent(name: 'careers_portfolio', parameters: {
              "build_portfolio": 'build portfolio',
            });
            /* Navigator.push(context, NextPageRoute(NewPortfolioPage()))
                .then((value) {
              if (value != null) menuProvider?.updateCurrentIndex(value);
            });*/

            if (Preference.getString(Preference.ROLE) == 'Learner' ||
                Preference.getString(Preference.ROLE) == 'Lead' ||
                Preference.getString(Preference.ROLE) == 'Alumni') {
              Navigator.push(context, NextPageRoute(NewPortfolioPage()))
                  .then((value) {
                if (value != null) menuProvider?.updateCurrentIndex(value);
              });
            } else {
              Navigator.push(
                  context,
                  NextPageRoute(NewPortfolioPage(
                    expJobResume: false,
                  ))).then((value) {
                if (value != null) menuProvider?.updateCurrentIndex(value);
              });
            }
          } else if (clickType == 'complete_profile') {
            FirebaseAnalytics.instance
                .logEvent(name: 'careers_profile', parameters: {
              "complete_profile": 'complete profile',
            });
            Navigator.push(context, NextPageRoute(NewPortfolioPage()))
                .then((value) {
              if (value != null) menuProvider?.updateCurrentIndex(value);
            });
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              clickType != 'complete_profile'
                  ? Expanded(
                      child: Image.asset(
                        'assets/images/build_read.png',
                        height: 40,
                        width: 40,
                      ),
                    )
                  : SizedBox(),
              Expanded(
                flex: 9,
                child: Container(
                  margin: EdgeInsets.only(left: 10.0, right: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('$strTitle',
                          style: Styles.bold(
                              lineHeight: 1.4,
                              size: 16,
                              color: ColorConstants.WHITE)),
                      Padding(
                        padding: const EdgeInsets.only(top: 10.0),
                        child: Text('$strDes',
                            style: Styles.regularWhite(lineHeight: 1.4)),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Container(
                  padding: EdgeInsets.only(
                    left: 10.0,
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: colorBg,
        boxShadow: [],
      ),
    );
  }

  Widget splineGraph() {
    double maxValueY = 0;
    double minValueY = 0;
    double maxValueX = 0;
    double minValueX = 0;

    List<FlSpot> spots = [];
    List<String> monthNames = [];

    if (wowDashboardDetails?.data?.jobVacanciesGraph != null) {
      Map<String, dynamic>? jobVacanciesGraph =
          wowDashboardDetails?.data?.jobVacanciesGraph;

      spots = List.generate(
        jobVacanciesGraph?.length ?? 0,
        (index) {
          List<dynamic> item = jobVacanciesGraph?[index.toString()];

          maxValueY = max(maxValueY, item[0] * 1.0);
          minValueY = min(minValueY, item[0] * 1.0);

          maxValueX = max(maxValueX, index * 1.0);
          minValueX = min(minValueX, index * 1.0);
          monthNames.add(item[2]);
          return FlSpot(index * 1.0, item[0] * 1.0);
        },
      );
    }

    return Container(
      height: MediaQuery.of(context).size.height * 0.25,
      width: double.infinity,
      child: LineChart(
        LineChartData(
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              // spots: [
              //   FlSpot(0, 4737),
              //   FlSpot(1, 4),
              //   FlSpot(2, 3.5),
              //   FlSpot(3, 5),
              //   FlSpot(4, 4),
              //   FlSpot(5, 6),
              // ],

              isCurved: true,
              color: ColorConstants.GREEN.withOpacity(0.6),
              barWidth: 1,
              isStrokeCapRound: true,
              //belowBarData: BarAreaData(show: false),

              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.green.withOpacity(0.6),
                    Colors.white.withOpacity(0.6),
                  ],
                ),
              ),
            ),
          ],
          minX: minValueX,
          maxX: maxValueX,
          minY: minValueY,
          maxY: maxValueY,
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
              axisNameWidget: Text(
                  '${wowDashboardDetails?.data?.jobVacanciesGraph?.values.map((e) => e[2]).toList()}',
                  style: Styles.regular(size: 12)),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                reservedSize: 44,
                showTitles: false,
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                reservedSize: 44,
                showTitles: false,
              ),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(
                reservedSize: 44,
                showTitles: false,
              ),
            ),
          ),
          borderData: FlBorderData(
            show: false,
            border: Border.all(color: ColorConstants.WHITE, width: 0),
          ),
        ),
      ),
    );
  }

  Widget graphCard2() {
    return Container(
      height: 200,
      child: Container(
        height: 50,
        //width: 90,
        child: SfCartesianChart(
          margin: EdgeInsets.zero,
          enableSideBySideSeriesPlacement: true,
          plotAreaBorderWidth: 0,
          primaryXAxis: CategoryAxis(isVisible: true),
          primaryYAxis: NumericAxis(
            isVisible: false,
          ),
          // series: <ChartSeries>[
          //   ColumnSeries<MapEntry<int, List<dynamic>>, String>(
          //     dataSource: data!.entries.toList(),
          //     xValueMapper: (entry, _) => entry.value[2],
          //     yValueMapper: (entry, _) => entry.value[0],
          //     dataLabelSettings: DataLabelSettings(isVisible: true),
          //   )
          // ], // change 13 Aug 2025
          series: <CartesianSeries>[
            ColumnSeries<MapEntry<int, List<dynamic>>, String>(
              dataSource: data!.entries.toList(),
              xValueMapper: (entry, _) => entry.value[2],
              yValueMapper: (entry, _) => entry.value[0],
              dataLabelSettings: DataLabelSettings(isVisible: true),
            )
          ],
        ),
      ),
    );
  }

  // Widget splineGraph() {
  //   return Container(
  //     height: 200,
  //     child: SingleChildScrollView(
  //       scrollDirection: Axis.horizontal,
  //       child: Container(
  //         height: 200,
  //         margin: EdgeInsets.only(top: 40.0, left: 10.0, right: 10.0),
  //         width: MediaQuery.of(context).size.width,
  //         child: LineChart(
  //           LineChartData(
  //             lineBarsData: [
  //               LineChartBarData(
  //                 /*spots: [
  //                   FlSpot(0.0, 4.0),
  //                   FlSpot(1.0, 3.0),
  //                   FlSpot(2.0, 4.0),
  //                 ],*/
  //                 spots: _spots,
  //                 isCurved: true,
  //                 color: ColorConstants.GREEN,
  //                 barWidth: 4,
  //                 isStrokeCapRound: true,
  //                 aboveBarData: BarAreaData(show: false),
  //                 belowBarData: BarAreaData(
  //                   show: true,
  //                   gradient: LinearGradient(
  //                     begin: Alignment.topCenter,
  //                     end: Alignment.bottomCenter,
  //                     colors: [
  //                       Colors.green,
  //                       Colors.white,
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ],
  //             minX: 0,
  //             maxX: 9,
  //             minY: 0,
  //             maxY: 6,
  //             titlesData: FlTitlesData(
  //               bottomTitles: AxisTitles(
  //                 sideTitles: SideTitles(
  //                   reservedSize: 20,
  //                   showTitles: false,
  //                 ),
  //               ),
  //               leftTitles: AxisTitles(
  //                 sideTitles: SideTitles(
  //                   reservedSize: 20,
  //                   showTitles: false,
  //                 ),
  //               ),
  //               rightTitles: AxisTitles(
  //                 sideTitles: SideTitles(
  //                   reservedSize: 20,
  //                   showTitles: false,
  //                 ),
  //               ),
  //               topTitles: AxisTitles(
  //                 sideTitles: SideTitles(
  //                   reservedSize: 20,
  //                   showTitles: false,
  //                 ),
  //               ),
  //             ),
  //             borderData: FlBorderData(
  //               show: true,
  //               border: Border.all(color: const Color(0xff37434d), width: 0),
  //             ),
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  // }
}

class DashboardBlankPage extends StatelessWidget {
  const DashboardBlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListView.builder(
            shrinkWrap: true,
            itemCount: 5,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  height: 90,
                  width: double.infinity,
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: ColorConstants.WHITE,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: const Offset(5, 5),
                      ),
                    ],
                  ),
                  child: Row(children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 80,
                          margin: EdgeInsets.only(left: 2),
                          width: 80,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    SizedBox(width: 10),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 12,
                              margin: EdgeInsets.only(left: 2),
                              width: 150,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        SizedBox(
                          height: 7,
                        ),
                        Row(
                          children: [
                            Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                  height: 12,
                                  margin: EdgeInsets.only(left: 2),
                                  width: 100,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                  )),
                            ),
                            Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                  height: 12,
                                  margin: EdgeInsets.only(left: 2),
                                  width: 100,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                  )),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 7,
                        ),
                        Row(
                          children: [
                            Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                  height: 12,
                                  margin: EdgeInsets.only(left: 2),
                                  width: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                  )),
                            ),
                            SizedBox(
                              width: 7,
                            ),
                            SizedBox(
                              width: 7,
                            ),
                            Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                  height: 12,
                                  margin: EdgeInsets.only(left: 2),
                                  width: 70,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                  )),
                            ),
                            SizedBox(
                              width: 3,
                            ),
                            SizedBox(
                              width: 3,
                            ),
                            Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                  height: 12,
                                  margin: EdgeInsets.only(left: 2),
                                  width: 70,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                  )),
                            ),
                          ],
                        )
                      ],
                    ),
                  ]),
                ),
              );
            })

        // Container(
        //   color: ColorConstants.WHITE,
        //   margin: EdgeInsets.all(8),
        //   // height: 200,
        //   child: ListView.builder(
        //     shrinkWrap: true,
        //     itemCount: 3,
        //     itemBuilder: (context, index) {
        //       return Container(
        //         margin: EdgeInsets.symmetric(horizontal: 6),
        //         height: MediaQuery.of(context).size.height * 0.40,
        //         width: double.infinity,
        //         child: Shimmer.fromColors(
        //           baseColor: Color(0xffe6e4e6),
        //           highlightColor: Color(0xffeaf0f3),
        //           enabled: true,
        //           child: Container(
        //             margin: const EdgeInsets.all(10),
        //             decoration: BoxDecoration(
        //                 color: ColorConstants.WHITE,
        //                 borderRadius: BorderRadius.circular(20)),
        //             width: 100,
        //             height: 13,
        //             child: Row(
        //               children: [
        //                 Container(
        //                   height: 20,
        //                   width: 60,
        //                   child: SizedBox(),
        //                 )
        //               ],
        //             ),
        //           ),
        //         ),
        //       );
        //     },
        //   ),
        // ),
      ],
    );
  }
}
