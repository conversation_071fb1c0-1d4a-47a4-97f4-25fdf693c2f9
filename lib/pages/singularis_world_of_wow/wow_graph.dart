// import 'dart:convert';
// import 'dart:math';

// import 'package:cached_network_image/cached_network_image.dart';
// //import 'package:custom_widget_marquee/custom_widget_marquee.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:hive_flutter/hive_flutter.dart';
// import 'package:masterg/blocs/bloc_manager.dart';
// import 'package:masterg/blocs/home_bloc.dart';
// import 'package:masterg/data/api/api_service.dart';
// import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
// import 'package:masterg/data/models/response/auth_response/dashboard_content_resp.dart';
// import 'package:masterg/data/models/response/home_response/competition_response.dart';
// import 'package:masterg/data/models/response/home_response/domain_list_response.dart';
// import 'package:masterg/data/models/response/home_response/training_module_response.dart';
// import 'package:masterg/data/models/response/home_response/wow_dashboard_response.dart';
// import 'package:masterg/local/pref/Preference.dart';
// import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
// import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
// import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
// import 'package:masterg/pages/custom_pages/custom_widgets/rounded_appbar.dart';
// import 'package:masterg/pages/explore_job/explore_job_list_page.dart';
// import 'package:masterg/pages/reels/widgets/left_panel.dart';
// import 'package:masterg/pages/singularis/app_drawer_page.dart';
// import 'package:masterg/pages/singularis/job/job_details_page.dart';
// import 'package:masterg/pages/singularis/job/my_job_all_view_list_page.dart';
// import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
// import 'package:masterg/utils/Log.dart';

// import 'package:masterg/utils/Styles.dart';
// import 'package:masterg/utils/config.dart';
// import 'package:masterg/utils/constant.dart';
// import 'package:masterg/utils/resource/colors.dart';
// import 'package:masterg/utils/utility.dart';
// import 'package:provider/provider.dart';
// import 'package:shimmer/shimmer.dart';

// import '../../utils/custom_widget_marquee_replacement.dart';
// import '../../utils/resource/size_constants.dart';
// import '../singularis/job/job_dashboard_page.dart';

// class SingualrisWowDashboard extends StatefulWidget {
//   const SingualrisWowDashboard({Key? key}) : super(key: key);

//   @override
//   _SingualrisWowDashboardState createState() => _SingualrisWowDashboardState();
// }

// class _SingualrisWowDashboardState extends State<SingualrisWowDashboard>
//     with TickerProviderStateMixin {
//   var _scaffoldKey = new GlobalKey<ScaffoldState>();
//   MenuListProvider? menuProvider;

//   WowDashboardResponse? wowDashboardDetails;
//   bool? isLoading = false;
//   Box? box;

//   CompetitionResponse? myJobResponse,
//       allJobListResponse,
//       recommendedJobOpportunities;
//   bool? isJobLoading;
//   bool? myJobLoading = true;
//   bool? competitionDetailLoading = true;
//   bool? domainListLoading = true;
//   bool? jobApplyLoading = true;
//   TrainingModuleResponse? competitionDetail;
//   DomainListResponse? domainList;

//   int? programId;
//   int selectedIndex = 0;
//   String seletedIds = '';
//   String domainId = '';
//   List<int> selectedIdList = <int>[];
//   int? applied;

//   bool myJobRecall = false;
//   double maxX = 0;
//   double maxY = 0;
//   double minX = 2090;
//   double minY = 2090;
//   final List<Color> gradientColors = [
//     Color(0xff3EBDA0),
//     Color.fromARGB(255, 169, 214, 204),
//   ];

//   @override
//   void initState() {
//     super.initState();

//     getWowDashboardDetails();
//     getMyJobList(false);
//     getDomainList();
//   }

//   @override
//   void dispose() {
//     super.dispose();
//   }

//   Widget build(BuildContext context) {
//     return BlocManager(
//         initState: (context) {},
//         child: Consumer<MenuListProvider>(
//             builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
//                 listener: (context, state) async {
//                   if (state is WowDashboardState) {
//                     _handleWowDashboardDetails(state);
//                   }
//                   if (state is JobCompListState) {
//                     _handlecompetitionListResponse(state);
//                   }
//                   if (state is CompetitionDetailState) {
//                     handlecompetitionDetailResponse(state);
//                   }
//                   if (state is DomainListState) {
//                     handleDomainListResponse(state);
//                   }
//                   if (state is CompetitionContentListState)
//                     handleJobApplyState(state);

//                   setState(() {
//                     menuProvider = mp;
//                   });
//                 },
//                 child: Scaffold(
//                     backgroundColor: ColorConstants.DASHBOARD_BG_COLOR,
//                     key: _scaffoldKey,
//                     endDrawer: new AppDrawer(),
//                     body: ScreenWithLoader(
//                       isLoading: isLoading,
//                       body: SafeArea(
//                           child: SingleChildScrollView(
//                               child: Column(
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                             RoundedAppBar(
//                                 appBarHeight: height(context) *
//                                     (Utility().isRTL(context) ? 0.18 : 0.16),
//                                 child: Padding(
//                                   padding: const EdgeInsets.symmetric(
//                                       vertical: 8, horizontal: 12),
//                                   child: Column(
//                                     mainAxisAlignment: MainAxisAlignment.start,
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       SizedBox(
//                                         height: 10,
//                                       ),
//                                       Row(
//                                         crossAxisAlignment:
//                                             CrossAxisAlignment.start,
//                                         children: [
//                                           /*SizedBox(
//                                               height: 5.0,
//                                             ),*/
//                                           Row(
//                                             children: [
//                                               InkWell(
//                                                 onTap: () async {
//                                                   if (Preference.getString(
//                                                           Preference.ROLE) ==
//                                                       'Learner') {
//                                                     Navigator.push(
//                                                             context,
//                                                             NextPageRoute(
//                                                                 NewPortfolioPage()))
//                                                         .then((value) {
//                                                       if (value != null)
//                                                         menuProvider
//                                                             ?.updateCurrentIndex(
//                                                                 value);
//                                                     });
//                                                   } else {
//                                                     if (APK_DETAILS[
//                                                             "package_name"] ==
//                                                         "com.singulariswow.mec") {
//                                                       Navigator.push(
//                                                           context,
//                                                           MaterialPageRoute(
//                                                               builder: (context) =>
//                                                                   TermsAndCondition(
//                                                                     url: 'https://mecfuture.mec.edu.om/hris/my-profile?user_id=' +
//                                                                         Preference.getInt(Preference.USER_ID)
//                                                                             .toString(),
//                                                                     title: tr(
//                                                                         'my_profile'),
//                                                                   ),
//                                                               maintainState:
//                                                                   false));
//                                                     } else {
//                                                       Navigator.push(
//                                                               context,
//                                                               NextPageRoute(
//                                                                   NewPortfolioPage()))
//                                                           .then((value) {
//                                                         if (value != null)
//                                                           menuProvider
//                                                               ?.updateCurrentIndex(
//                                                                   value);
//                                                       });
//                                                     }
//                                                   }
//                                                 },
//                                                 child: ClipRRect(
//                                                   borderRadius:
//                                                       BorderRadius.circular(
//                                                           200),
//                                                   child: SizedBox(
//                                                     width: 50,
//                                                     height: 50,
//                                                     child: CachedNetworkImage(
//                                                       imageUrl:
//                                                           '${Preference.getString(Preference.PROFILE_IMAGE)}',
//                                                       fit: BoxFit.cover,
//                                                       placeholder:
//                                                           (context, url) =>
//                                                               SvgPicture.asset(
//                                                         'assets/images/default_user.svg',
//                                                         width: 50,
//                                                         height: 50,
//                                                       ),
//                                                       errorWidget: (context,
//                                                               url, error) =>
//                                                           SvgPicture.asset(
//                                                         'assets/images/default_user.svg',
//                                                         width: 50,
//                                                         height: 50,
//                                                       ),
//                                                     ),
//                                                   ),
//                                                 ),
//                                               ),
//                                             ],
//                                           ),
//                                           Spacer(),
//                                           Padding(
//                                             padding: Utility().isRTL(context)
//                                                 ? EdgeInsets.only(
//                                                     top: 18.0, left: 6)
//                                                 : EdgeInsets.only(
//                                                     top: 8, right: 6),
//                                             child: SizedBox(
//                                               // flex: 2,
//                                               child: Align(
//                                                 alignment:
//                                                     Utility().isRTL(context)
//                                                         ? Alignment.topLeft
//                                                         : Alignment.topRight,
//                                                 child: InkWell(
//                                                   onTap: () {
//                                                     _scaffoldKey.currentState
//                                                         ?.openEndDrawer();
//                                                   },
//                                                   child: SvgPicture.asset(
//                                                       'assets/images/hamburger_menu.svg'),
//                                                 ),
//                                               ),
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                                       SizedBox(height: 12),
//                                     ],
//                                   ),
//                                 )),
//                             SizedBox(
//                               height: height(context) * 0.02,
//                             ),
//                             Padding(
//                                 padding: EdgeInsets.symmetric(horizontal: 16),
//                                 child: Text('welcome_msg',
//                                         style: Styles.regular(size: 14))
//                                     .tr()
//                                 //  _searchFilter(),
//                                 ),
//                             Padding(
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: 16, vertical: 8),
//                               child: SvgPicture.asset(
//                                   'assets/images/singualris_wow_head.svg'),
//                             ),
//                             SizedBox(
//                               height: 8,
//                             ),
//                             // myJobResponse?.data != null ? _myJobSectionCard() : SizedBox(),
//                             wowDashboardDetails?.data == null
//                                 ? Container(
//                                     child: Center(
//                                       child: Text('Your Details Not Found'),
//                                     ),
//                                   )
//                                 : Column(
//                                     children: [
//                                       Preference.getString(Preference.ROLE) ==
//                                               'Learner'
//                                           ? myJobCard()
//                                           : SizedBox(),
//                                       Preference.getString(Preference.ROLE) ==
//                                               'Learner'
//                                           ? SizedBox(height: 15)
//                                           : SizedBox(),

//                                       ///World of Word
//                                       wowCard(),
//                                       SizedBox(height: 15),

//                                       ///Job Opportunity
//                                       Preference.getString(Preference.ROLE) ==
//                                               'Learner'
//                                           ? GestureDetector(
//                                               onTap: () {
//                                                 Navigator.push(
//                                                     context,
//                                                     NextPageRoute(
//                                                         JobDashboardPage(
//                                                             myJobEnable: false),
//                                                         isMaintainState: true));
//                                               },
//                                               child: jobOpportunities(),
//                                             )
//                                           : SizedBox(),
//                                       Preference.getString(Preference.ROLE) ==
//                                               'Learner'
//                                           ? SizedBox(height: 15)
//                                           : SizedBox(),

//                                       ///Smart Job Matching
//                                       Preference.getString(Preference.ROLE) ==
//                                               'Learner'
//                                           ? GestureDetector(
//                                               onTap: () {
//                                                 Navigator.push(
//                                                     context,
//                                                     NextPageRoute(
//                                                         ExploreJobListPage(
//                                                           indexNo: null,
//                                                         ),
//                                                         isMaintainState: true));
//                                               },
//                                               child: smartJobMatching(),
//                                             )
//                                           : SizedBox(),
//                                       SizedBox(
//                                         height: 18,
//                                       ),
//                                       _highLightsCard(
//                                           ColorConstants.ORANGE,
//                                           tr('build_portfolio'),
//                                           tr('build_portfolio_text'),
//                                           'build_portfolio'),
//                                       SizedBox(height: 30),
//                                     ],
//                                   )
//                           ]))),
//                     )))));
//   }

//   void getMyJobList(bool jobType) {
//     BlocProvider.of<HomeBloc>(context).add(JobCompListEvent(
//         isPopular: false,
//         isFilter: false,
//         isJob: 1,
//         myJob: 1,
//         jobTypeMyJob: jobType));
//   }

//   void getWowDashboardDetails() {
//     BlocProvider.of<HomeBloc>(context).add(WowDashboardEvent());
//   }

//   void getCompetitionList(bool isFilter, String? ids) {
//     BlocProvider.of<HomeBloc>(context).add(
//         CompetitionListEvent(isPopular: false, isFilter: isFilter, ids: ids));
//   }

//   void jobApply(int jobId, int? isApplied) {
//     BlocProvider.of<HomeBloc>(context).add(CompetitionContentListEvent(
//         competitionId: jobId, isApplied: isApplied));
//   }

//   void getDomainList() {
//     BlocProvider.of<HomeBloc>(context).add(DomainListEvent());
//   }

//   void getFilterList(String ids) {
//     BlocProvider.of<HomeBloc>(context).add(DomainFilterListEvent(ids: ids));
//   }

//   void _handleWowDashboardDetails(WowDashboardState state) {
//     try {
//       var loginState = state;
//       setState(() {
//         switch (loginState.apiState) {
//           case ApiStatus.LOADING:
//             Log.v("Loading...................WowDashboardDetails.");
//             isLoading = true;
//             break;
//           case ApiStatus.SUCCESS:
//             Log.v("Success....................WowDashboardDetails");
//             wowDashboardDetails = state.response;

//             isLoading = false;
//             break;
//           case ApiStatus.ERROR:
//             Log.v("Error..........................WowDashboardDetails");
//             isLoading = false;
//             break;
//           case ApiStatus.INITIAL:
//             break;
//         }
//       });
//     } catch (e, stacktrace) {
//       Log.v("$stacktrace: $e");

//       setState(() {
//         isLoading = false;
//       });
//     }
//   }

//   void _handlecompetitionListResponse(JobCompListState state) {
//     var jobCompState = state;
//     setState(() {
//       switch (jobCompState.apiState) {
//         case ApiStatus.LOADING:
//           Log.v("Loading....................");
//           myJobLoading = true;
//           break;
//         case ApiStatus.SUCCESS:
//           Log.v("CompetitionState....................");
//           if (myJobRecall == false) {
//             myJobResponse = state.myJobListResponse;
//             allJobListResponse = state.jobListResponse;
//             recommendedJobOpportunities = state.recommendedJobOpportunities;
//           } else {
//             myJobResponse = state.myJobListResponse;
//           }

//           myJobLoading = false;
//           break;
//         case ApiStatus.ERROR:
//           Log.v(
//               "Error CompetitionListIDState .....................${jobCompState.error}");
//           myJobLoading = false;
//           FirebaseAnalytics.instance
//               .logEvent(name: 'job_dashboard', parameters: {
//             "ERROR": '${jobCompState.error}',
//           });

//           break;
//         case ApiStatus.INITIAL:
//           break;
//       }
//     });
//   }

//   void handlecompetitionDetailResponse(CompetitionDetailState state) {
//     var competitionState = state;
//     setState(() {
//       switch (competitionState.apiState) {
//         case ApiStatus.LOADING:
//           Log.v("Loading....................");
//           competitionDetailLoading = true;
//           break;
//         case ApiStatus.SUCCESS:
//           Log.v("Competition Detail State....................");
//           competitionDetail = state.response;
//           competitionDetailLoading = false;
//           break;
//         case ApiStatus.ERROR:
//           Log.v(
//               "Error Competition Detail IDState ..........................${competitionState.error}");
//           competitionDetailLoading = false;
//           FirebaseAnalytics.instance
//               .logEvent(name: 'job_dashboard', parameters: {
//             "ERROR": '${competitionState.error}',
//           });
//           break;
//         case ApiStatus.INITIAL:
//           break;
//       }
//     });
//   }

//   void handleDomainListResponse(DomainListState state) {
//     var popularCompetitionState = state;
//     setState(() {
//       switch (popularCompetitionState.apiState) {
//         case ApiStatus.LOADING:
//           Log.v("DomainListLoading....................");
//           domainListLoading = true;
//           break;
//         case ApiStatus.SUCCESS:
//           Log.v("DomainListState....................");
//           domainList = state.response;
//           domainListLoading = false;

//           if (domainList?.data?.list?.length != 0) {
//             domainId = domainList!.data!.list![0].id.toString();
//           }

//           break;
//         case ApiStatus.ERROR:
//           Log.v(
//               "DomainListError....................${popularCompetitionState.error}");
//           domainListLoading = false;
//           FirebaseAnalytics.instance
//               .logEvent(name: 'job_dashboard', parameters: {
//             "ERROR": '${popularCompetitionState.error}',
//           });
//           break;
//         case ApiStatus.INITIAL:
//           break;
//       }
//     });
//   }

//   void handleJobApplyState(CompetitionContentListState state) {
//     var competitionState = state;
//     setState(() {
//       switch (competitionState.apiState) {
//         case ApiStatus.LOADING:
//           Log.v("Loading....................");
//           jobApplyLoading = true;
//           break;
//         case ApiStatus.SUCCESS:
//           Log.v("Competition Content List State....................");
//           myJobRecall = true;
//           getMyJobList(true);

//           jobApplyLoading = false;
//           break;
//         case ApiStatus.ERROR:
//           Log.v(
//               "Error Competition Content ..........................${competitionState.response?.error}");
//           jobApplyLoading = false;
//           FirebaseAnalytics.instance
//               .logEvent(name: 'job_dashboard', parameters: {
//             "ERROR": '${competitionState.response?.error}',
//           });

//           break;
//         case ApiStatus.INITIAL:
//           break;
//       }
//     });
//   }

//   myJobCard() {
//     return Container(
//         margin: EdgeInsets.symmetric(horizontal: 12),
//         height: MediaQuery.of(context).size.height * 0.15,
//         decoration: BoxDecoration(
//           color: ColorConstants.WHITE,
//           borderRadius: BorderRadius.circular(20),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 16),
//           child: Column(
//             children: [
//               Row(
//                 children: [
//                   Text('my_job',
//                           style: Styles.bold(
//                               size: 18, color: ColorConstants.HEADING_TITLE))
//                       .tr(),
//                   Spacer(),
//                   InkWell(
//                     onTap: () {
//                       Navigator.push(
//                           context,
//                           NextPageRoute(MyJobAllViewListPage(
//                             myJobResponse: myJobResponse,
//                           ))).then((value) {
//                         getMyJobList(false);
//                         getDomainList();
//                       });
//                     },
//                     child: Container(
//                       height: 30,
//                       width: 30,
//                       decoration: BoxDecoration(
//                         color: ColorConstants.PRIMARY_BLUE,
//                         borderRadius: BorderRadius.circular(20),
//                       ),
//                       child: Icon(Icons.arrow_forward,
//                           size: 15, color: ColorConstants.WHITE),
//                     ),
//                   )
//                 ],
//               ),
//               Padding(
//                 padding: const EdgeInsets.only(top: 10.0),
//                 child: Text(
//                   'my_job_card_desc',
//                   style: Styles.regular(size: 14),
//                 ).tr(),
//               )
//             ],
//           ),
//         ));
//   }

//   wowCard() {
//     return Container(
//         margin: EdgeInsets.symmetric(horizontal: 12),
//         height: MediaQuery.of(context).size.height * 0.49,
//         width: double.infinity,
//         decoration: BoxDecoration(
//           color: ColorConstants.ACCENT_COLOR,
//           borderRadius: BorderRadius.circular(20),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 children: [
//                   Text('world_of_work',
//                           style: Styles.bold(
//                               size: 18, color: ColorConstants.HEADING_TITLE))
//                       .tr(),
//                   Spacer(),
//                   InkWell(
//                     onTap: () {
//                       Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                               builder: (context) => TermsAndCondition(
//                                     url: '${APK_DETAILS['wow-dashboard']}' +
//                                         Preference.getInt(Preference.USER_ID)
//                                             .toString() +
//                                         '&is_webview=1',
//                                     title: tr('World of Work'),
//                                   ),
//                               maintainState: false));
//                     },
//                     child: Container(
//                       height: 30,
//                       width: 30,
//                       decoration: BoxDecoration(
//                         color: ColorConstants.PRIMARY_BLUE,
//                         borderRadius: BorderRadius.circular(20),
//                       ),
//                       child: Icon(
//                         Icons.arrow_forward,
//                         size: 15,
//                         color: ColorConstants.WHITE,
//                       ),
//                     ),
//                   )
//                 ],
//               ),
//               Padding(
//                 padding: const EdgeInsets.only(top: 10.0),
//                 child: Text(
//                   'wow_card_desc',
//                   style: Styles.regular(size: 14),
//                 ).tr(),
//               ),
//               SizedBox(height: 15),
//               RichText(
//                 text: TextSpan(
//                   style: TextStyle(
//                     fontSize: 20.0,
//                     color: Colors.black,
//                   ),
//                   children: <TextSpan>[
//                     TextSpan(
//                         text: tr('job_vacancies'),
//                         style: Styles.regular(color: ColorConstants.GREY_2)),
//                     TextSpan(
//                       text:
//                           ' : ${wowDashboardDetails?.data?.wowDetails?.jobVacancies ?? 0}',
//                       style: Styles.bold(color: ColorConstants.HEADING_TITLE),
//                     ),
//                   ],
//                 ),
//               ),
//               SizedBox(height: 20),
//               splineGraph()
//             ],
//           ),
//         ));
//   }

//   jobOpportunities() {
//     return Container(
//         margin: EdgeInsets.symmetric(horizontal: 12),
//         height: MediaQuery.of(context).size.height * 0.3,
//         width: double.infinity,
//         decoration: BoxDecoration(
//           color: ColorConstants.ACCENT_COLOR,
//           borderRadius: BorderRadius.circular(20),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 children: [
//                   Text('job_opportunities',
//                           style: Styles.bold(
//                               size: 18, color: ColorConstants.HEADING_TITLE))
//                       .tr(),
//                   Spacer(),
//                   Container(
//                     height: 30,
//                     width: 30,
//                     decoration: BoxDecoration(
//                       color: ColorConstants.PRIMARY_BLUE,
//                       borderRadius: BorderRadius.circular(20),
//                     ),
//                     child: Icon(
//                       Icons.arrow_forward,
//                       size: 15,
//                       color: ColorConstants.WHITE,
//                     ),
//                   )
//                 ],
//               ),
//               Padding(
//                 padding: const EdgeInsets.only(top: 10.0),
//                 child: Text(
//                   'job_opportunities_desc',
//                   style: Styles.regular(size: 14),
//                 ).tr(),
//               ),
//               SizedBox(height: 30),
//               SizedBox(
//                   height: 100,
//                   child: CustomWidgetMarqueeReplacement(
//                     animationDuration: const Duration(seconds: 20),
//                     backDuration: const Duration(seconds: 20),
//                     pauseDuration: const Duration(seconds: 2),
//                     //directionOption: DirectionOption.twoDirection,
//                     child: ListView.builder(
//                       scrollDirection: Axis.horizontal,
//                       itemCount: wowDashboardDetails?.data?.jobOpportunities?.length,
//                       shrinkWrap: true,
//                       itemBuilder: (context, index) {
//                         return Container(
//                             padding: EdgeInsets.all(8),
//                             child: Column(
//                               children: [
//                                 ClipRRect(
//                                   borderRadius:
//                                       BorderRadius.all(Radius.circular(4)),
//                                   child: SizedBox(
//                                     width: 120,
//                                     height: 80,
//                                     child: Image.network(
//                                       "${wowDashboardDetails?.data?.jobOpportunities?[index].image ?? ''}",
//                                       fit: BoxFit.cover,
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ));
//                       },
//                     ),
//                   )),
//             ],
//           ),
//         ));
//   }

//   smartJobMatching() {
//     return Container(
//         margin: EdgeInsets.symmetric(horizontal: 12),
//         height: MediaQuery.of(context).size.height * 0.3,
//         decoration: BoxDecoration(
//           color: ColorConstants.ACCENT_COLOR,
//           borderRadius: BorderRadius.circular(20),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 children: [
//                   Text('smart_job_matching',
//                           style: Styles.bold(
//                               size: 18, color: ColorConstants.HEADING_TITLE))
//                       .tr(),
//                   Spacer(),
//                   Container(
//                     height: 30,
//                     width: 30,
//                     decoration: BoxDecoration(
//                       color: ColorConstants.PRIMARY_BLUE,
//                       borderRadius: BorderRadius.circular(20),
//                     ),
//                     child: Icon(
//                       Icons.arrow_forward,
//                       size: 15,
//                       color: ColorConstants.WHITE,
//                     ),
//                   )
//                 ],
//               ),
//               Padding(
//                 padding: const EdgeInsets.only(top: 10.0),
//                 child: Text(
//                   'smart_job_matching_desc',
//                   style: Styles.regular(size: 14),
//                 ).tr(),
//               ),
//               SizedBox(height: 5),
//               Container(
//                   padding: EdgeInsets.all(8),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       //'${tr('complete_your_profile')}-${wowDashboardDetails?.data?.matchingProfile?.profileCompletion?.toStringAsFixed(2)}%',
//                       SizedBox(
//                         height: 20,
//                       ),
//                       Row(
//                         children: [
//                           Text(
//                             '${wowDashboardDetails?.data?.matchingProfile?.matchingJobs}+ ${tr('Jobs')}',
//                             style: Styles.bold(size: 20),
//                           ).tr(),
//                           Padding(
//                             padding: const EdgeInsets.only(left: 8.0),
//                             child: Text(
//                               '${tr('job_matching_to')}',
//                               style: Styles.regular(size: 20),
//                             ).tr(),
//                           ),
//                         ],
//                       ),

//                       Row(
//                         children: [
//                           Padding(
//                             padding: const EdgeInsets.only(top: 10.0),
//                             child: Text(
//                               '${tr('complete_your_profile')} :',
//                               style: Styles.regular(),
//                             ).tr(),
//                           ),
//                           Padding(
//                             padding:
//                                 const EdgeInsets.only(left: 3.0, top: 10.0),
//                             child: Text(
//                               '${wowDashboardDetails?.data?.matchingProfile?.profileCompletion}%',
//                               style: Styles.bold(size: 20),
//                             ).tr(),
//                           ),
//                         ],
//                       ),
//                       SizedBox(height: 5),
//                       ClipRRect(
//                         borderRadius: BorderRadius.all(Radius.circular(30)),
//                         child: LinearProgressIndicator(
//                           minHeight: 6,
//                           value: (wowDashboardDetails?.data?.matchingProfile
//                                       ?.profileCompletion ??
//                                   0) /
//                               100,
//                           backgroundColor: Colors.grey[300],
//                           valueColor: AlwaysStoppedAnimation<Color>(
//                             ColorConstants.GREEN,
//                           ),
//                         ),
//                       ),
//                     ],
//                   ))
//             ],
//           ),
//         ));
//   }

//   Widget _highLightsCard(
//       Color colorBg, String strTitle, String strDes, String clickType) {
//     return Container(
//       height: 120,
//       padding: const EdgeInsets.symmetric(horizontal: 6),
//       margin: const EdgeInsets.only(
//           left: SizeConstants.JOB_LEFT_SCREEN_MGN,
//           right: SizeConstants.JOB_RIGHT_SCREEN_MGN),
//       width: double.infinity,
//       child: InkWell(
//         onTap: () {
//           if (clickType == 'build_portfolio') {
//             FirebaseAnalytics.instance
//                 .logEvent(name: 'careers_portfolio', parameters: {
//               "build_portfolio": 'build portfolio',
//             });
//             Navigator.push(context, NextPageRoute(NewPortfolioPage()))
//                 .then((value) {
//               if (value != null) menuProvider?.updateCurrentIndex(value);
//             });
//           } else if (clickType == 'complete_profile') {
//             FirebaseAnalytics.instance
//                 .logEvent(name: 'careers_profile', parameters: {
//               "complete_profile": 'complete profile',
//             });
//             Navigator.push(context, NextPageRoute(NewPortfolioPage()))
//                 .then((value) {
//               if (value != null) menuProvider?.updateCurrentIndex(value);
//             });
//           }
//         },
//         child: Padding(
//           padding: const EdgeInsets.all(8),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.start,
//             children: [
//               clickType != 'complete_profile'
//                   ? Expanded(
//                       child: Image.asset(
//                         'assets/images/build_read.png',
//                         height: 40,
//                         width: 40,
//                       ),
//                     )
//                   : SizedBox(),
//               Expanded(
//                 flex: 9,
//                 child: Container(
//                   margin: EdgeInsets.only(left: 10.0, right: 10),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Text('$strTitle',
//                           style: Styles.bold(
//                               lineHeight: 1.4,
//                               size: 16,
//                               color: ColorConstants.WHITE)),
//                       Padding(
//                         padding: const EdgeInsets.only(top: 10.0),
//                         child: Text('$strDes',
//                             style: Styles.regularWhite(lineHeight: 1.4)),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//               Expanded(
//                 flex: 1,
//                 child: Container(
//                   padding: EdgeInsets.only(
//                     left: 10.0,
//                   ),
//                   child: Icon(
//                     Icons.arrow_forward_ios,
//                     color: Colors.white,
//                     size: 28,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(10),
//         color: colorBg,
//         boxShadow: [],
//       ),
//     );
//   }

//   Widget splineGraph() {
//     double maxValueY = 0;
//     double minValueY = 0;
//     double maxValueX = 0;
//     double minValueX = 0;

//     List<FlSpot> spots = [];
//     List<String> monthNames = [];
//     if (wowDashboardDetails?.data?.jobVacanciesGraph != null) {
//       Map<String, dynamic>? jobVacanciesGraph =
//           wowDashboardDetails?.data?.jobVacanciesGraph;
//       spots = List.generate(
//         jobVacanciesGraph?.length ?? 0,
//         (index) {
//           List<dynamic> item = jobVacanciesGraph?[index.toString()];

//           maxValueY = max(maxValueY, item[0] * 1.0);
//           minValueY = min(minValueY, item[0] * 1.0);

//           maxValueX = max(maxValueX, index * 1.0);
//           minValueX = min(minValueX, index * 1.0);
//           Log.v("index $index and ${item[0]}");
//           monthNames.add(item[2]);
//           return FlSpot(index * 1.0, item[0] * 1.0);
//         },
//       );
//     }

//     return Container(
//       height: MediaQuery.of(context).size.height * 0.25,
//       width: double.infinity,
//       child: LineChart(
//         LineChartData(
//           lineBarsData: [
//             LineChartBarData(
//               spots: spots,
//               // spots: [
//               //   FlSpot(0, 4737),
//               //   FlSpot(1, 4),
//               //   FlSpot(2, 3.5),
//               //   FlSpot(3, 5),
//               //   FlSpot(4, 4),
//               //   FlSpot(5, 6),
//               // ],

//               isCurved: true,
//               color: ColorConstants.GREEN.withOpacity(0.6),
//               barWidth: 1,
//               isStrokeCapRound: true,
//               //belowBarData: BarAreaData(show: false),

//               belowBarData: BarAreaData(
//                 show: true,
//                 gradient: LinearGradient(
//                   begin: Alignment.topCenter,
//                   end: Alignment.bottomCenter,
//                   colors: [
//                     Colors.green.withOpacity(0.6),
//                     Colors.white.withOpacity(0.6),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//           minX: minValueX,
//           maxX: maxValueX,
//           minY: minValueY,
//           maxY: maxValueY,
//           titlesData: FlTitlesData(
//             bottomTitles: AxisTitles(
//               sideTitles: SideTitles(showTitles: false),
//               axisNameWidget:
//                   Text('$monthNames', style: Styles.regular(size: 12)),
//             ),
//             leftTitles: AxisTitles(
//               sideTitles: SideTitles(
//                 reservedSize: 44,
//                 showTitles: false,
//               ),
//             ),
//             rightTitles: AxisTitles(
//               sideTitles: SideTitles(
//                 reservedSize: 44,
//                 showTitles: false,
//               ),
//             ),
//             topTitles: AxisTitles(
//               sideTitles: SideTitles(
//                 reservedSize: 44,
//                 showTitles: false,
//               ),
//             ),
//           ),
//           borderData: FlBorderData(
//             show: false,
//             border: Border.all(color: ColorConstants.WHITE, width: 0),
//           ),
//         ),
//       ),
//     );
//   }
// }
