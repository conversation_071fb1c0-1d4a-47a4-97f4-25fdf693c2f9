// import 'package:easy_localization/easy_localization.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:masterg/blocs/bloc_manager.dart';
// import 'package:masterg/blocs/home_bloc.dart';
// import 'package:masterg/data/api/api_service.dart';
// import 'package:masterg/data/models/response/auth_response/oraganization_program_resp.dart';
// import 'package:masterg/local/pref/Preference.dart';
// import 'package:masterg/pages/custom_pages/custom_widgets/CommonWebView.dart';
// import 'package:masterg/utils/Log.dart';
// import 'package:masterg/utils/Styles.dart';
// import 'package:masterg/utils/constant.dart';
// import 'package:masterg/utils/resource/colors.dart';

// class OnboardingSelecteOnboardingSelecteInterestPage extends StatefulWidget {
//   @override
//   _OnboardingSelecteOnboardingSelecteInterestPageState createState() =>
//       _OnboardingSelecteOnboardingSelecteInterestPageState();
// }

// class _OnboardingSelecteOnboardingSelecteInterestPageState
//     extends State<OnboardingSelecteOnboardingSelecteInterestPage> {
//   TextEditingController collegeNameController = TextEditingController();
//   TextEditingController courseNameController = TextEditingController();
//   String? selectedCourse;

//   bool studyingInCollege = false;
//   List<OragnizationProgram>? response;

//   List<OragnizationProgram>? displayInterestList = [];

//   @override
//   void initState() {
//     // displayInterestList.addAll(interest);

//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     double screenHeight = height(context);
//     return Scaffold(
//       body: BlocManager(
//         initState: (BuildContext context) {
//           BlocProvider.of<HomeBloc>(context)
//               .add(OrganizationProgramListEvent());
//         },
//         child: BlocListener<HomeBloc, HomeState>(
//           listener: (BuildContext context, state) {
//             if (state is OrganizationProgramListState) {
//               setState(() {
//                 switch (state.apiState) {
//                   case ApiStatus.LOADING:
//                     Log.v("Loading....................");
//                     break;
//                   case ApiStatus.SUCCESS:
//                     Log.v("Success.................... ");
//                     response = state.response?.data;
//                     displayInterestList = response;

//                     break;

//                   case ApiStatus.ERROR:
//                     Log.v("Error..........................");
//                     Log.v("Error..........................${state.error}");

//                     break;
//                   case ApiStatus.INITIAL:
//                     break;
//                 }
//               });
//             }
//           },
//           child: Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Stack(
//               children: [
//                 ListView(
//                   // crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'selecte_your_interest',
//                       style: Styles.bold(size: 20),
//                     ).tr(),
//                     SizedBox(height: 6),
//                     Text(
//                       'what_you_want_to_become',
//                       style: Styles.regular(size: 14),
//                     ).tr(),
//                     SizedBox(height: 20),
//                     TextField(
//                       controller: collegeNameController,
//                       onChanged: (String value) {
//                         displayInterestList = response
//                             ?.where((element) => element.interestarea!
//                                 .toLowerCase()
//                                 .contains(value.toLowerCase()))
//                             .toList();

//                         if (value == "") {
//                           displayInterestList?.addAll(response!);
//                         }

//                         setState(() {});
//                       },
//                       decoration: InputDecoration(
//                         suffixIcon: Icon(
//                           Icons.search,
//                           size: 28,
//                           color: ColorConstants.GREY_2,
//                         ),
//                         hintText: tr('search'),
//                         border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(10),
//                         ),
//                         focusedBorder: OutlineInputBorder(
//                           borderSide: BorderSide(color: ColorConstants.GREY_3),
//                           borderRadius: BorderRadius.circular(10),
//                         ),
//                       ),
//                     ),
//                     SizedBox(height: 25),
//                     if (response != null) ...[
//                       displayInterestList?.length == 0
//                           ? Container(
//                             height: height(context) * 0.6,
//                             child: Center(
//                               child: Text(
//                               'no_data',
//                               style: Styles.bold(),
//                               ).tr(),
//                             ),
//                           )
//                           : GridView.builder(
//                               shrinkWrap: true,
//                               physics: ScrollPhysics(),
//                               gridDelegate:
//                                   SliverGridDelegateWithFixedCrossAxisCount(
//                                 mainAxisSpacing: 0,
//                                 crossAxisSpacing: 0,
//                                 childAspectRatio: 1,
//                                 crossAxisCount: 2,
//                               ),
//                               itemCount: displayInterestList?.length,
//                               itemBuilder: (BuildContext context, int index) {
//                                 return InkWell(
//                                   onTap: () {
//                                     try {
//                                       FirebaseAnalytics.instance.logEvent(
//                                           name:
//                                               'user_interest_${Preference.getInt(Preference.USER_ID).toString()}',
//                                           parameters: {
//                                             "interest_area":
//                                                 "${displayInterestList?[index].interestarea}",
//                                           });
//                                     } catch (e, stacktrace) {
//                                       debugPrint('$stacktrace');
//                                       return;
//                                     }

//
//                                     Navigator.push(
//                                         context,
//                                         MaterialPageRoute(
//                                             builder: (context) => CommonWebView(
//                                                   url:
//                                                       '',
//                                                 ))).then(
//                                         (value) => Navigator.pop(context));
//                                   },
//                                   child: Column(
//                                     mainAxisAlignment: MainAxisAlignment.start,
//                                     children: [
//                                       Container(
//                                         margin: const EdgeInsets.symmetric(
//                                             vertical: 4),
//                                         width: width(context) * 0.32,
//                                         height: width(context) * 0.32,
//                                         decoration: BoxDecoration(
//                                             borderRadius:
//                                                 BorderRadius.circular(20),
//                                             border: Border.all(
//                                                 width: 2,
//                                                 color: ColorConstants()
//                                                     .primaryColorAlways())),
//                                         child: ClipRRect(
//                                           borderRadius:
//                                               BorderRadius.circular(16),
//                                           child: Image.asset(
//                                             "assets/images/temp/${(index % 14) + 1}.JPG",
//                                             fit: BoxFit.cover,
//                                           ),
//                                         ),
//                                       ),
//                                       Text(
//                                           '${displayInterestList?[index].interestarea}',
//                                           style: Styles.bold(
//                                             size: 12,
//                                             lineHeight: 1.2,
//                                             color: ColorConstants.GREY_2,
//                                           ),
//                                           maxLines: 2,
//                                           textAlign: TextAlign.center),
//                                     ],
//                                   ),
//                                 );
//                               },
//                             ),
//                     ]
//                   ],
//                 ),
//                 // Positioned(
//                 //   bottom: 10,
//                 //   left: 10,
//                 //   right: 10,
//                 //   child: Container(
//                 //     // margin: EdgeInsets.only(left: 10.0, top: 30.0, right: 10.0),
//                 //     width: double.infinity,
//                 //     height: screenHeight * WidgetSize.AUTH_BUTTON_SIZE,
//                 //     decoration: BoxDecoration(
//                 //         gradient: LinearGradient(colors: [
//                 //           ColorConstants().gradientLeft(),
//                 //           ColorConstants().gradientRight(),
//                 //         ]),
//                 //         color: ColorConstants().buttonColor(),
//                 //         borderRadius: BorderRadius.circular(10)),
//                 //     child: Center(
//                 //         child: Text('Next',
//                 //                 style: Styles.regular(color: ColorConstants.WHITE))
//                 //             .tr()),
//                 //   ),
//                 // ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/joy_category_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/ghome/home_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/widget_size.dart';
import 'package:shimmer/shimmer.dart';

import '../../data/models/response/auth_response/oraganization_program_resp.dart';

class OnboardingSelecteInterestPage extends StatefulWidget {
  final bool? backEnable;
  final bool? moveToHome;
  final bool? singleSelection;
  final bool? returnValue;
  final int? fetchGoalList;

  OnboardingSelecteInterestPage({
    Key? key,
    this.backEnable,
    this.moveToHome = false,
    this.singleSelection = false,
    this.returnValue = false,
    this.fetchGoalList = 0,
  }) : super(key: key);

  @override
  State<OnboardingSelecteInterestPage> createState() =>
      _OnboardingSelecteInterestPageState();
}

class _OnboardingSelecteInterestPageState
    extends State<OnboardingSelecteInterestPage> {
  bool isInterestMapping = false;

  List<String>? interestMapResponse;
  List<OragnizationProgram>? programs_list;
  List<int?> selectProgramId = [];
  List<int?> selectProgramParentId = [];
  List<int> selectedPrograms = [];
  List<OragnizationProgram> joyCategoryList = [];
  bool isUpdating = false;
  List<Menu>? menuList;
  Color foregroundColor = ColorConstants.BLACK;
  int? isParentLanguage =
      Preference.getInt(Preference.IS_PRIMARY_LANGUAGE) ?? 1;

  List<OragnizationProgram>? response;

  List<OragnizationProgram>? displayInterestList = [];

  String? selectInst;
  List<String?> listInterest = <String>[];

  @override
  void initState() {
    super.initState();
    foregroundColor = ColorConstants().primaryForgroundColor();
    _getInterestPrograms();
  }

  void _getInterestPrograms() {
    // BlocProvider.of<HomeBloc>(context).add(InterestEvent());
    BlocProvider.of<HomeBloc>(context).add(OrganizationProgramListEvent(widget.fetchGoalList));
  }

  void _mapInterest(param) {
    BlocProvider.of<HomeBloc>(context)
        .add(MapInterestEvent(param: param, mapType: 'InterestArea'));
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {},
        child: MultiBlocListener(
            listeners: [
              BlocListener<HomeBloc, HomeState>(
                  listener: (BuildContext context, state) {
                // if (state is InterestState)
                //   _handleInterestProgramResponse(state);
                if (state is MapInterestState)
                  _handleMapInterestResponse(state);
              }),
              BlocListener<HomeBloc, HomeState>(
                listener: (BuildContext context, state) {
                  if (state is GetBottomBarState) {
                    _handelBottomNavigationBar(state);
                  }
                  if (state is PiDetailState) {
                    handlePiDetail(state);
                  }
                  if (state is OrganizationProgramListState) {
                    setState(() {
                      switch (state.apiState) {
                        case ApiStatus.LOADING:
                          Log.v("Loading....................");
                          break;
                        case ApiStatus.SUCCESS:
                          Log.v(
                              "Success....................interest area list ");
                          response = state.response?.data;
                          displayInterestList = response;
                         
                          break;
                        case ApiStatus.ERROR:
                          Log.v("Error..........................${state.error}");

                          break;
                        case ApiStatus.INITIAL:
                          break;
                      }
                    });
                  }
                },
              ),
            ],
            child: Scaffold(
              backgroundColor: ColorConstants.WHITE,
              appBar: AppBar(
                backgroundColor: ColorConstants.WHITE,
                leading: widget.moveToHome == false
                    ? BackButton(color: ColorConstants.BLACK)
                    : SizedBox(),
                title: Text(
                  'choose_interest',
                  style: Styles.semibold(color: ColorConstants.BLACK),
                ).tr(),
                elevation: 0,
                automaticallyImplyLeading:
                    widget.backEnable == true ? true : false,
              ),
              body: SafeArea(
                child: ScreenWithLoader(
                  isLoading: isUpdating,
                  body: SingleChildScrollView(
                    child: (displayInterestList != null &&
                            displayInterestList?.length != 0)
                        ? Padding(
                            padding: const EdgeInsets.only(
                                left: 20.0,
                                top: 0.0,
                                right: 20.0,
                                bottom: 20.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('select_categories',
                                          style: Styles.bold())
                                      .tr(),
                                  SizedBox(height: 2),
                                  Text('select_1_catg', style: Styles.regular())
                                      .tr(),
                                  SizedBox(height: 20),
                                  Container(
                                    height: MediaQuery.of(context).size.height *
                                        0.69,
                                    child: techChips(),
                                  ),
                                  Visibility(
                                    visible: true,
                                    child: InkWell(
                                        onTap: () {
                                          var selectedCategoryIds = '';
                                          displayInterestList
                                              ?.forEach((element) {
                                            if ((int.tryParse(
                                                        '${element.isMapped}') ??
                                                    0) >
                                                0) {
                                              selectedCategoryIds +=
                                                  element.id.toString() + ',';
                                            }
                                          });

                                          selectedCategoryIds =
                                              selectedCategoryIds.substring(
                                                  0,
                                                  selectedCategoryIds.length -
                                                      1);
                                          _mapInterest(selectedCategoryIds);

                                          // Preference.setString(
                                          //     'interestCategory',
                                          //     '$selectedCategoryIds');

                                          return;

                                          // var parentId = '';
                                          // var localId = '';

                                          // if (isParentLanguage == 1) {
                                          //   selectProgramId.forEach((element) {
                                          //     localId +=
                                          //         element.toString() + ',';
                                          //   });

                                          //   localId = localId.substring(
                                          //       0, localId.length - 1);
                                          //   Preference.setString(
                                          //       'interestCategory', '$localId');

                                          //   _mapInterest(localId);
                                          // } else {
                                          //   selectProgramParentId
                                          //       .forEach((element) {
                                          //     parentId +=
                                          //         element.toString() + ',';
                                          //   });

                                          //   parentId = parentId.substring(
                                          //       0, parentId.length - 1);

                                          //   Preference.setString(
                                          //       'interestCategory',
                                          //       '$parentId');
                                          //   _mapInterest(parentId);
                                          // }
                                        },
                                        child: Container(
                                          margin: EdgeInsets.only(
                                              left: 5.0,
                                              top: 10.0,
                                              right: 5.0,
                                              bottom: 10.0),
                                          width: double.infinity,
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              WidgetSize.AUTH_BUTTON_SIZE,
                                          decoration: BoxDecoration(
                                              gradient: LinearGradient(colors: [
                                                ColorConstants().gradientLeft(),
                                                ColorConstants()
                                                    .gradientRight(),
                                              ]),
                                              color: ColorConstants()
                                                  .buttonColor(),
                                              borderRadius:
                                                  BorderRadius.circular(5)),
                                          child: Center(
                                              child: Text('confirm_interest',
                                                  style: Styles.regular(
                                                    color: foregroundColor,
                                                  )).tr()),
                                        )),
                                  ),
                                ]),
                          )
                        : Container(
                            height: MediaQuery.of(context).size.height,
                            child: SingleChildScrollView(
                              child: Wrap(
                                  direction: Axis.horizontal,
                                  children: shimmerChips.toList()),
                            ),
                          ),
                  ),
                ),
              ),
            )));
  }

  void _handleMapInterestResponse(MapInterestState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isInterestMapping = true;
          isUpdating = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          interestMapResponse = state.response!.data;
          isUpdating = false;
          isInterestMapping = true;
          //Preference.SAVE_INTEREST
          _getInterestPrograms();
          if (widget.moveToHome == true) {
            getBottomNavigationBar();
          } else {
            AlertsWidget.alertWithOkBtn(
              context: context,
              onOkClick: () {
                if(widget.returnValue == false){
                  Navigator.pop(context, true);
                }else{
                  //Navigator.pop(context, selectInst);
                  Navigator.pop(context, listInterest);
                }
              },
             text: "${state.response?.data?.first}",
            );
          }

          // var box = Hive.box("content");
          // JoyCategoryResponse joyCategoryResponse =
          // JoyCategoryResponse.fromJson(response.body);
          // box.put("joy_category",
          //     joyCategoryList..map((e) => e.toJson()).toList());

          break;
        case ApiStatus.ERROR:
          isUpdating = false;
          isInterestMapping = false;
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'select_interest', parameters: {
            "map_interest_error": loginState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          isUpdating = false;
          break;
      }
    });
  }

  // void _handleInterestProgramResponse(InterestState state) {
  //   var loginState = state;
  //   setState(() {
  //     switch (loginState.apiState) {
  //       case ApiStatus.LOADING:
  //         Log.v("Loading....................");
  //         break;
  //       case ApiStatus.SUCCESS:
  //         if (isUpdating) {
  //           if (widget.backEnable == true)
  //             Navigator.pop(context);
  //           else
  //             Preference.getInt(Preference.ENABLE_PI) == 1
  //                 ? getPiDetail()
  //                 : getBottomNavigationBar();
  //         } else {
  //           Log.v("JoyCategoryState....................");

  //           Log.v(state.response!.data!.list.toString());

  //           programs_list = state.response!.data!.list;

  //           for (int i = 0; i < displayInterestList!.length; i++) {
  //             if ((int.tryParse('${displayInterestList![i].isMapped}') ?? 0) >
  //                 1) {
  //               joyCategoryList.add(displayInterestList![i]);
  //               setState(() {
  //                 // if (isParentLanguage != 1) {
  //                 //   if (selectProgramParentId
  //                 //       .contains(displayInterestList![i].parentId)) {
  //                 //     selectProgramParentId.remove(displayInterestList![i].parentId);
  //                 //   } else {
  //                 //     selectProgramParentId.add(displayInterestList![i].parentId);
  //                 //   }
  //                 // } else {
  //                 //   if (selectProgramId.contains(displayInterestList![i].id)) {
  //                 //     selectProgramId.remove(displayInterestList![i].id);
  //                 //   } else {
  //                 //     selectProgramId.add(displayInterestList![i].id);
  //                 //   }
  //                 // }

  //                 if (selectProgramId.contains(displayInterestList![i].id)) {
  //                   selectProgramId.remove(displayInterestList![i].id);
  //                 } else {
  //                   selectProgramId.add(displayInterestList![i].id);
  //                 }
  //               });
  //             }
  //           }

  //           Log.v("JoyCategoryState Done ....................");
  //         }

  //         break;
  //       case ApiStatus.ERROR:
  //         Log.v("Error..........................");
  //         Log.v("ErrorHome..........................${loginState.error}");
  //         FirebaseAnalytics.instance
  //             .logEvent(name: 'select_interest', parameters: {
  //           "map_interest_program_error": loginState.error,
  //         });
  //         break;
  //       case ApiStatus.INITIAL:
  //         break;
  //     }
  //   });
  // }

  void getBottomNavigationBar() {
    BlocProvider.of<HomeBloc>(context).add((GetBottomNavigationBarEvent()));
  }

  void getPiDetail() {
    BlocProvider.of<HomeBloc>(context)
        .add(PiDetailEvent(userId: Preference.getInt(Preference.USER_ID)));
  }

  void handlePiDetail(PiDetailState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PI Detail Loading....................");
          //isPortfolioLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PI Detail Success....................${portfolioState.response?.data?.toJson()}");

          if (portfolioState.response?.data?.name != '' &&
              portfolioState.response?.data?.name != null)
            Preference.setString(Preference.FIRST_NAME,
                '${portfolioState.response?.data?.name}');
          if (portfolioState.response?.data?.email != '' &&
              portfolioState.response?.data?.email != null)
            Preference.setString(Preference.USER_EMAIL,
                '${portfolioState.response?.data?.email}');

          if (portfolioState.response?.data?.mobile != '' &&
              portfolioState.response?.data?.mobile != null)
            Preference.setString(
                Preference.PHONE, '${portfolioState.response?.data?.mobile}');

          setState(() {});
          getBottomNavigationBar(); //hide 18 jun 2024
          break;

        case ApiStatus.ERROR:
          getBottomNavigationBar(); //hide 18 jun 2024
          Log.v(
              "PI Detail Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'select_interest', parameters: {
            "map_pi_interest_error": portfolioState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }


  void _handelBottomNavigationBar(GetBottomBarState state) {
    var getBottomBarState = state;
    setState(() {
      switch (getBottomBarState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading.................... bottom ");
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success.................... bottom");
          Log.v("bottom...${state.response?.data?.menuSide?.length}");
          Log.v("bottom...${state.response?.data?.menuSide?[0].label}");
          Log.v("bottom role...${Preference.getString(Preference.ROLE)?.toLowerCase()}");

          menuList = state.response!.data!.menu;
            menuList =  menuList!.where((element) {
        bool containRole = element.role.toString().toLowerCase().contains(
            '${Preference.getString(Preference.ROLE)?.toLowerCase()}');
        return containRole;
      }).toList();
          if (menuList?.length == 0) {
            AlertsWidget.alertWithOkBtn(
                context: context,
                text:tr('menu_not_found_msg'),
                onOkClick: () {
                  FocusScope.of(context).unfocus();
                });
          } else {
            // menuList?.sort((a, b) => a.inAppOrder!.compareTo(b.inAppOrder!));
            menuList?.sort((a, b) => (int.tryParse('${a.inAppOrder}') ?? 0).compareTo(int.tryParse('${b.inAppOrder}') ?? 0));

            int index = 0;
            for (var item in menuList!) {
              if (item.url == '/g-home') {
                index = menuList!.indexOf(item);
                break;
              }
            }

            Navigator.pushAndRemoveUntil(
                context,
                NextPageRoute(
                    homePage(
                      bottomMenu: menuList,
                      index: index,
                    ),
                    isMaintainState: true),
                (route) => false);
          }

          break;

        case ApiStatus.ERROR:
          Log.v("Error..........................");
          FirebaseAnalytics.instance
              .logEvent(name: 'select_interest', parameters: {
            "map_interest_bottom_bar_error": getBottomBarState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          // TODO: Handle this case.
          break;
      }
    });
  }

  Iterable<Widget> get shimmerChips sync* {
    for (int i = 0; i < 15; i++) {
      yield Padding(
        padding: const EdgeInsets.only(top: 20),
        child: Shimmer.fromColors(
          baseColor: Color(0xffe6e4e6),
          highlightColor: Color(0xffeaf0f3),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 10.0),
                child: Container(
                  height: width(context) * 0.4,
                  width: width(context) * 0.4,
                  decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 10.0),
                child: Container(
                  height: 10,
                  width: width(context) * 0.25,
                  margin: const EdgeInsets.only(top: 10),
                  decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget techChips() {
    return GridView.builder(
        shrinkWrap: true,
        physics: ScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          mainAxisSpacing: 4,
          crossAxisSpacing: 2,
          childAspectRatio: 1,
          crossAxisCount: 2,
        ),
        itemCount: displayInterestList?.length,
        itemBuilder: (context, i) {
          return InkWell(
            onTap: () {
              Log.v('map is ${displayInterestList?[i].isMapped}');
              if(widget.singleSelection  == false) {
                setState(() {
                  if ((int.tryParse('${displayInterestList![i].isMapped}') ??
                      0) > 0) {
                    displayInterestList![i].isMapped = 0;
                  } else {
                    displayInterestList![i].isMapped = 1;
                  }
                  setState(() {
                    if (selectProgramId.contains(displayInterestList![i].id)) {
                      selectProgramId.remove(displayInterestList![i].id);
                    } else {
                      selectProgramId.add(displayInterestList![i].id);
                    }
                  });
                  // if (isParentLanguage != 1) {
                  //   if (selectProgramParentId
                  //       .contains(displayInterestList![i].parentId)) {
                  //     selectProgramParentId
                  //         .remove(displayInterestList![i].parentId);
                  //   } else {
                  //     selectProgramParentId.add(displayInterestList![i].parentId);
                  //   }
                  // } else {
                  //   if (selectProgramId.contains(displayInterestList![i].id)) {
                  //     selectProgramId.remove(displayInterestList![i].id);
                  //   } else {
                  //     selectProgramId.add(displayInterestList![i].id);
                  //   }
                  // }

                  listInterest.clear();
                  listInterest.add(displayInterestList![i].interestarea);
                  listInterest.add(displayInterestList![i].id.toString());
                  //selectInst = displayInterestList![i].interestarea;
                });

              }else{
                setState(() {
                  if ((int.tryParse('${displayInterestList![i].isMapped}') ?? 0) == 1) {
                    displayInterestList![i].isMapped = 0;
                  } else {
                    displayInterestList![i].isMapped = 1;
                  }
                  setState(() {
                    if (selectProgramId.contains(displayInterestList![i].id)) {
                      selectProgramId.remove(displayInterestList![i].id);
                    } else {
                      selectProgramId.add(displayInterestList![i].id);
                    }
                  });
                });
              }
            },

            child: Column(
              children: [
                Stack(
                  children: [
                    Container(
                      margin: EdgeInsets.all(0),
                      //width: width(context),
                      //height: width(context),
                      width: 120,
                      height: 120,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          '${displayInterestList?[i].iconUrl}',
                          fit: BoxFit.cover,
                          errorBuilder: (context, url, error) {
                            return Image.asset(
                              'assets/images/blank.png',
                              height: 50,
                              width: 50,
                            );
                          },
                          loadingBuilder: (BuildContext context, Widget child,
                              ImageChunkEvent? loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                  height: 45,
                                  margin: EdgeInsets.only(left: 2),
                                  width: 45,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  )),
                            );
                          },
                        ),
                      ),
                    ),
                    // if (selectProgramId.contains(displayInterestList![i].id) !=
                    //     true)


                    if(widget.singleSelection == false)...[
                      if ((int.tryParse('${displayInterestList![i].isMapped}') ?? 0) > 0)
                        Positioned(
                            right: -3,
                            top: -3,
                            child: SvgPicture.asset('assets/images/interest_selected.svg')),
                    ]else...[
                      if ((int.tryParse('${displayInterestList![i].isMapped}') ?? 0) == 1)
                      Positioned(
                          right: -3,
                          top: -3,
                          child: SvgPicture.asset('assets/images/interest_selected.svg')),
                    ]
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 0, right: 0, top: 5),
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Text('${displayInterestList![i].interestarea ?? ""}',
                        style: Styles.bold(
                          size: 11,
                          //lineHeight: 1.2,
                          color: ColorConstants.GREY_3,
                        ),
                        maxLines: 2,
                        textAlign: TextAlign.center),
                  ),
                ),
              ],
            ),
            // child: Padding(
            //   padding: const EdgeInsets.only(left: 0, right: 10),
            //   child: Chip(
            //     backgroundColor: Colors.transparent,
            //     shape: StadiumBorder(
            //         side: BorderSide(
            //             color: isParentLanguage != 1
            //                 ? (selectProgramParentId
            //                         .contains(displayInterestList![i].parentId)
            //                     ? ColorConstants.ORANGE
            //                     : ColorConstants.GREY_4)
            //                 : (selectProgramId.contains(displayInterestList![i].id)
            //                     ? ColorConstants.ORANGE
            //                     : ColorConstants.GREY_4),
            //             width: 1)),
            //     avatar: isParentLanguage != 1
            //         ? (selectProgramParentId.contains(displayInterestList![i].parentId)
            //             ? Icon(
            //                 Icons.check_circle,
            //                 color: ColorConstants.GREEN,
            //                 size: 20,
            //               )
            //             : Icon(
            //                 Icons.check_circle,
            //                 color: ColorConstants.GREY_3,
            //                 size: 20,
            //               ))
            //         : (selectProgramId.contains(displayInterestList![i].id)
            //             ? Icon(
            //                 Icons.check_circle,
            //                 color: ColorConstants.GREEN,
            //                 size: 20,
            //               )
            //             : Icon(
            //                 Icons.check_circle,
            //                 color: ColorConstants.GREY_3,
            //                 size: 20,
            //               )),
            //     label: Text(
            //       '${displayInterestList![i].title}',
            //       style: Styles.regular(size: 14.22),
            //     ),
            //   ),

            // ),
          );
        });
  }
}
