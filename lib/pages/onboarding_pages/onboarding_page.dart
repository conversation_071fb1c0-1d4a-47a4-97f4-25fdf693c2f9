import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/onboarding_pages/onboarding_select_intreset.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/widget_size.dart';

class OnboardingPage extends StatefulWidget {
  @override
  _OnboardingPageState createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  TextEditingController collegeNameController = TextEditingController();
  TextEditingController courseNameController = TextEditingController();
  String? selectedCourse;
  bool studyingInCollege = true;

  @override
  Widget build(BuildContext context) {
    double screenHeight = height(context);
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Spacer(),
                    InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Icon(Icons.close),
                    )
                  ],
                ),
                Text(
                  'studying_in_mec',
                  style: Styles.semibold(),
                ).tr(),
                Row(
                  children: [
                    Radio(
                      activeColor: ColorConstants().primaryColorAlways(),
                      value: true,
                      groupValue: studyingInCollege,
                      onChanged: (value) {
                        setState(() {
                          studyingInCollege = true;
                        });
                      },
                    ),
                    Text('yes', style: Styles.regular()).tr(),
                    Radio(
                      activeColor: ColorConstants().primaryColorAlways(),
                      value: false,
                      groupValue: studyingInCollege,
                      onChanged: (value) {
                        setState(() {
                          studyingInCollege = false;
                        });
                      },
                    ),
                    Text('no', style: Styles.regular()).tr(),
                  ],
                ),
                SizedBox(
                  height: screenHeight * 0.05,
                ),
                studyingInCollege
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'select_course_you_enrolled',
                            style: Styles.regular(size: 18),
                          ).tr(),
                          SizedBox(
                            height: screenHeight * 0.02,
                          ),
                          Container(
                            width: width(context),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(color: ColorConstants.GREY_3)),
                            child: Container(
                              margin: const EdgeInsets.all(10),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  value: selectedCourse,
                                  elevation: 2,
                                  borderRadius: BorderRadius.circular(10),
                                  hint: Text(
                                    'select_your_course',
                                    style: Styles.regular(),
                                  ).tr(),
                                  onChanged: (value) {
                                    setState(() {
                                      selectedCourse = value;
                                    });
                                  },
                                  items: <String>[
                                    'BA(Hons) Business Administration in Human Resource Management',
                                    'BA(Hons) Business Administration in Accounting and Finance',
                                    'BSc (Hons) in Logistics Management',
                                    'Master of Business Administration(MBA)',
                                    'BA(Hons) Business Administration in General Management',
                                    'MSc in Records and Information Management',
                                    'B.Sc. Archives and Records Management',
                                  ].map<DropdownMenuItem<String>>(
                                      (String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: SizedBox(
                                          width: width(context) * 0.75,
                                          child: Text(value)),
                                    );
                                  }).toList(),
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'tell_your_college_course',
                            style: Styles.regular(),
                          ).tr(),
                          SizedBox(height: 8),
                          TextField(
                            controller: collegeNameController,
                            decoration: InputDecoration(
                              hintText: tr('your_college_name'),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide:
                                    BorderSide(color: ColorConstants.GREY_3),
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                          SizedBox(height: 8),
                          TextField(
                            controller: courseNameController,
                            decoration: InputDecoration(
                              hintText: tr('your_course_name'),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide:
                                    BorderSide(color: ColorConstants.GREY_3),
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ],
                      ),
                // Spacer(),
                SizedBox(
                  height: screenHeight * 0.06,
                ),
                InkWell(
                  onTap: () {
                    Preference.setBool('hidePreboarding', true);
                    try {
                      FirebaseAnalytics.instance.logEvent(
                          name:
                              'onboarding_user_info_${Preference.getInt(Preference.USER_ID).toString()}',
                          parameters: {
                            "user_id":
                                "${Preference.getInt(Preference.USER_ID)}",
                            "current_organization_user": "$studyingInCollege",
                            "college_name":
                                "${collegeNameController.value.text}",
                            "course_name":
                                "${studyingInCollege ? selectedCourse : courseNameController.value.text}",
                          });
                    } catch (e, stacktrace) {
                      debugPrint('$stacktrace');
                      return;
                    }
                    Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    OnboardingSelecteInterestPage()))
                        .then((value) => Navigator.pop(context));
                  },
                  child: Container(
                    // margin: EdgeInsets.only(left: 10.0, top: 30.0, right: 10.0),
                    width: double.infinity,
                    height: screenHeight * WidgetSize.AUTH_BUTTON_SIZE,
                    decoration: BoxDecoration(
                        gradient: LinearGradient(colors: [
                          ColorConstants().gradientLeft(),
                          ColorConstants().gradientRight(),
                        ]),
                        color: ColorConstants().buttonColor(),
                        borderRadius: BorderRadius.circular(10)),
                    child: Center(
                        child: Text('next',
                                style:
                                    Styles.regular(color: ColorConstants.WHITE))
                            .tr()),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                OnboardingSelecteInterestPage()));
                  },
                  child: Center(
                    child: Text('explore_interest_area',
                            style: Styles.textExtraBoldUnderline())
                        .tr(),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
