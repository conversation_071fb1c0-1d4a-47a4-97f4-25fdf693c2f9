import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

class AlertsWidget {
  static Future alertWithOkCancelBtn(
      {required BuildContext context,
      String? title = "",
      String? text = "",
      String? okText,
      String? cancelText,
      Function? onOkClick,
      Function? onCancelClick}) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async => false,
          child: AlertDialog(
            title: Text('$title'),
            content: Text('$text'),
            actions: <Widget>[
              TextButton(
                child: new Text(cancelText ?? 'no').tr(),
                onPressed: () {
                  if (onCancelClick != null) onCancelClick();
                  Navigator.of(context).pop();
                },
              ),
              Container(
                decoration: BoxDecoration(
                    color: ColorConstants().primaryColorAlways(),
                    borderRadius: BorderRadius.circular(8)),
                child: TextButton(
                  child: new Text(
                    okText ?? 'yes',
                    style: Styles.regular(color: ColorConstants.WHITE),
                  ).tr(),
                  onPressed: () {
                    Navigator.pop(context);
                    if (onOkClick != null) onOkClick();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future alertWithYesNoBtn(
      {required BuildContext context,
      String title = "",
      String text = "",
      Function? onOkClick,
      Function? onCancelClick}) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        // return object of type Dialog
        return WillPopScope(
          onWillPop: () async => false,
          child: AlertDialog(
            title: Text(
              title,
              style: Styles.textBold(size: 18),
              textAlign: TextAlign.center,
            ),
            content: Text(
              text,
              style: Styles.textRegular(size: 14),
              textAlign: TextAlign.center,
            ),
            actions: <Widget>[
              Padding(
                padding: const EdgeInsets.only(left: 30, right: 10),
                child: TextButton(
                  child: new Text(
                    'no',
                    style: Styles.textBold(size: 18),
                    textAlign: TextAlign.center,
                  ).tr(),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (onCancelClick != null) onCancelClick();
                  },
                ),
              ),
              SizedBox(
                width: 30,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 10, right: 30),
                child: TextButton(
                  child: new Text(
                    'yes',
                    style: Styles.textBold(size: 18),
                    textAlign: TextAlign.center,
                  ).tr(),
                  onPressed: () {
                    Navigator.pop(context);
                    if (onOkClick != null) onOkClick();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future alertWithOkBtn(
      {required BuildContext context,
      String? text = "",
      Function? onOkClick,
      bool isBarrierDismissible = false}) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        // return object of type Dialog
        return WillPopScope(
          onWillPop: () async => false,
          child: AlertDialog(
            title: Text(
              'alert',
              style: Styles.textBold(size: 20),
            ).tr(),
            content: Text(text ?? '', style: Styles.textBold(size: 16)),
            actions: <Widget>[
              Container(
                height: 40,
                width: 55,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                  color: ColorConstants.PRIMARY_BLUE,
                ),
                child: TextButton(
                  child: new Text('ok', style: Styles.textBold(size: 14, color: ColorConstants.WHITE)).tr(),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (onOkClick != null) onOkClick();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future showCustomDialog(
      {required BuildContext context,
      bool? showCancel = true,
      String? title = "",
      String? text = "",
      String? oKText,
      String? icon,
      Function? onOkClick,
      Function? onCancelClick,
      int? alertType = 1,
      bool? backonOk = true,
      bool isRTL = false,
      Function(BuildContext)? returnContext,
      bool? enable}) {
    String? icon;
    switch (alertType) {
      case 1:
        icon = 'assets/images/circle_alert_fill.svg';
        break;
      case 2:
        icon = 'assets/images/success_icon.svg';
        break;

      case 3:
        icon = 'assets/images/warning_icon.svg';
        break;

      default:
        icon = 'assets/images/circle_alert_fill.svg';
    }

    return showDialog(
        context: context,
        builder: (BuildContext context) {
          if (returnContext != null) {
            returnContext(context);
          }
          return Directionality(
            textDirection: Utility.setDirection(isRTL),
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0)), //this right here
              child: Container(
                // height: 230,
                constraints: BoxConstraints(
                  minHeight: 0,
                  maxHeight: MediaQuery.of(context).size.height *
                      0.32, // Set maxHeight to infinity
                ),
                child: Padding(
                  padding: const EdgeInsets.all(0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        icon!,
                        allowDrawingOutsideViewBox: true,
                        width: height(context) * 0.04,
                        height: height(context) * 0.04,
                      ),
                      SizedBox(
                        height: 10.0,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: Text(
                            '$title',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ).tr(),
                        ),
                      ),
                      Text(
                        text!,
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 14),
                      ).tr(),
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 15.0,
                          top: 25.0,
                          right: 15.0,
                        ),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          height: 50,
                          padding: EdgeInsets.only(bottom: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              if (showCancel == true)
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      if (onCancelClick != null)
                                        onCancelClick();
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Align(
                                          alignment: Alignment.center,
                                          child: Text(
                                            'cancel',
                                          ).tr()),
                                    ),
                                  ),
                                ),
                              SizedBox(width: 10,),
                              Expanded(
                                child: InkWell(
                                  onTap: () {
                                    if (backonOk == true)
                                      Navigator.pop(context);
                                    if (onOkClick != null) onOkClick();
                                  },
                                  child: Container(
                                    height: 50.0,
                                    decoration: BoxDecoration(
                                      color: ColorConstants.RED,
                                      borderRadius: BorderRadius.circular(10),
                                      gradient: LinearGradient(colors: [
                                        ColorConstants().gradientLeft(),
                                        ColorConstants().gradientRight(),
                                      ]),
                                    ),
                                    child: Align(
                                        alignment: Alignment.center,
                                        child: Text(
                                          oKText ?? tr('ok'),
                                          style: Styles.regular(
                                              color: ColorConstants()
                                                  .primaryForgroundColor()),
                                        )),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }


  static Future helpMecFutureCustomDialog(
      {required BuildContext context,
        bool? showCancel = true,
        String? title = "",
        String? text = "",
        String? oKText,
        Function? onOkClick,
        Function? onCancelClick,
        int? alertType = 1,
        bool? backonOk = true,
        bool isRTL = false,
        Function(BuildContext)? returnContext,
        bool? enable}) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          if (returnContext != null) {
            returnContext(context);
          }
          return Directionality(
            textDirection: Utility.setDirection(isRTL),
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0)), //this right here
              child: Container(
                // height: 230,
                constraints: BoxConstraints(
                  minHeight: 0,
                  maxHeight: MediaQuery.of(context).size.height *
                      0.32, // Set maxHeight to infinity
                ),
                child: Padding(
                  padding: const EdgeInsets.all(0.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      /*SvgPicture.asset(
                        icon!,
                        allowDrawingOutsideViewBox: true,
                        width: height(context) * 0.04,
                        height: height(context) * 0.04,
                      ),*/
                      Icon(Icons.help),
                      SizedBox(
                        height: 10.0,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: Text(
                            '$title',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          text!,
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 15.0,
                          top: 25.0,
                          right: 15.0,
                        ),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          height: 50,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              if (showCancel == true)
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      if (onCancelClick != null)
                                        onCancelClick();
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Align(
                                          alignment: Alignment.center,
                                          child: Text(
                                            'skip',
                                          ).tr()),
                                    ),
                                  ),
                                ),
                              SizedBox(width: 10,),
                              Expanded(
                                child: InkWell(
                                  onTap: () {
                                    if (backonOk == true)
                                      Navigator.pop(context);
                                    if (onOkClick != null) onOkClick();
                                  },
                                  child: Container(
                                    height: 50.0,
                                    decoration: BoxDecoration(
                                      color: ColorConstants.RED,
                                      borderRadius: BorderRadius.circular(10),
                                      gradient: LinearGradient(colors: [
                                        ColorConstants().gradientLeft(),
                                        ColorConstants().gradientRight(),
                                      ]),
                                    ),
                                    child: Align(
                                        alignment: Alignment.center,
                                        child: Text(
                                          oKText ?? tr('Go'),
                                          style: Styles.regular(
                                              color: ColorConstants()
                                                  .primaryForgroundColor()),
                                        )),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }


}