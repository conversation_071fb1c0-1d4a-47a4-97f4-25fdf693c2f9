import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/company_list_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/rounded_appbar.dart';
import 'package:masterg/pages/singularis/app_drawer_page.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
import 'package:masterg/pages/world_of_wow/explore_world_of_work.dart';
import 'package:masterg/pages/world_of_wow/future_trends_world_of_work.dart';
import 'package:masterg/pages/world_of_wow/internship.dart';
import 'package:masterg/pages/world_of_wow/popular_jobs_internship.dart';
import 'package:masterg/pages/world_of_wow/top_companies_jobs.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class WorldOfWow extends StatefulWidget {
  const WorldOfWow({Key? key}) : super(key: key);

  @override
  State<WorldOfWow> createState() => _WorldOfWowState();
}

class _WorldOfWowState extends State<WorldOfWow> {
  var _scaffoldKey = new GlobalKey<ScaffoldState>();
  List<Company>? companies;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      endDrawer: new AppDrawer(),
      body: BlocManager(
          initState: (BuildContext context) {
            getTopCompanies();
          },
          child: BlocListener<HomeBloc, HomeState>(
              listener: (context, state) async {
                if (state is TopCompaniesState) handleTopCompanies(state);
              },
              child: Container(
                color: ColorConstants.GREY,
                child: Consumer<MenuListProvider>(
                  builder: (context, menuProvider, child) =>
                      SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RoundedAppBar(
                            appBarHeight: height(context) *
                                (Utility().isRTL(context) ? 0.12 : 0.10),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: 5.0,
                                      ),
                                      Row(
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              Navigator.push(
                                                      context,
                                                      NextPageRoute(
                                                          NewPortfolioPage()))
                                                  .then((value) {
                                                if (value != null)
                                                  menuProvider
                                                      .updateCurrentIndex(
                                                          value);
                                              });
                                            },
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(200),
                                              child: SizedBox(
                                                width: 50,
                                                child: CachedNetworkImage(
                                                  imageUrl:
                                                      '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                                  placeholder: (context, url) =>
                                                      SvgPicture.asset(
                                                    'assets/images/default_user.svg',
                                                    width: 50,
                                                  ),
                                                  errorWidget:
                                                      (context, url, error) =>
                                                          SvgPicture.asset(
                                                    'assets/images/default_user.svg',
                                                    width: 50,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                width: width(context) * 0.7,
                                                child: Text('welcome_text',
                                                        style: Styles.regular(
                                                            color:
                                                                ColorConstants
                                                                    .WHITE,
                                                            size: 14))
                                                    .tr(),
                                              ),
                                              SizedBox(
                                                width: width(context) * 0.7,
                                                child: Text(
                                                  Utility().decrypted128(
                                                      '${Preference.getString(Preference.FIRST_NAME)}'),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: Styles.bold(
                                                      color:
                                                          ColorConstants.WHITE,
                                                      size: 22),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Align(
                                          alignment: Alignment.topRight,
                                          child: InkWell(
                                            onTap: () {
                                              _scaffoldKey.currentState
                                                  ?.openEndDrawer();
                                            },
                                            child: SvgPicture.asset(
                                                'assets/images/hamburger_menu.svg'),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )),
                        ExploreWorldOfWork(),
                        FutureTrendsWorldOfWork(),
                        PopularJobInternship(
                          title: tr('popular_jobs_internship'),
                        ),
                        
                        TopCompanies(companies: companies),
                        Container(
                          margin: EdgeInsets.symmetric(vertical: 12),
                          color: ColorConstants.WHITE,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 12),
                                child: Text(
                                  'top_hiring_companies',
                                  style: Styles.semibold(size: 14),
                                ).tr(),
                              ),
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 12),
                                child: GridView.builder(
                                  physics: NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 4),
                                  itemBuilder: (_, index) => CachedNetworkImage(
                                    imageUrl: '${companies?[index].thumbnail}',
                                    progressIndicatorBuilder:
                                        (context, url, downloadProgress) =>
                                            Shimmer.fromColors(
                                      baseColor: Color(0xffe6e4e6),
                                      highlightColor: Color(0xffeaf0f3),
                                      child: Container(
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 10, vertical: 10),
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(6)),
                                      ),
                                    ),
                                   
                                  ),
                                  itemCount: companies?.length ?? 0,
                                ),
                              )
                            ],
                          ),
                        ),
                        PopularInternship(
                          title: tr('internship_opportunities'),
                        
                        )
                      ],
                    ),
                  ),
                ),
              ))),
    );
  }

  void getTopCompanies() {
    try {
      BlocProvider.of<HomeBloc>(context).add(TopCompaniesEvent());
    } catch (e) {
      print('Exception $e');
    }
  }

  void handleTopCompanies(TopCompaniesState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................TopCompanies");
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................TopCompanies");

          companies = state.response?.data;

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
           FirebaseAnalytics.instance.logEvent(name: 'world_of_wow', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
