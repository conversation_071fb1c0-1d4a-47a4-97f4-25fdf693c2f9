import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/company_job_list_response.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/singularis/job/job_details_page.dart';
import 'package:masterg/pages/world_of_wow/view_all_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

class PopularInternship extends StatefulWidget {
  final String? title;

  final bool? showViewAll;
  final List<Job>? jobs;
  const PopularInternship({
    Key? key,
    this.title,
    this.showViewAll = true,
    this.jobs,
  }) : super(key: key);

  @override
  State<PopularInternship> createState() => _PopularJobInternshipState();
}

class _PopularJobInternshipState extends State<PopularInternship> {
  List<Job>? jobs;
  @override
  void initState() {
    initData();
    super.initState();
  }

  void initData() {
    setState(() {
      jobs = widget.jobs;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (BuildContext context) {
        getPopularJobInternship();
      },
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) async {
          if (state is PopularIntershipState) {
            handlePopularJobInternship(state);
          }
        },
        child: Container(
          color: ColorConstants.WHITE,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.title != null)
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                  child: Text(
                    '${widget.title}',
                    style: Styles.semibold(size: 14),
                  ),
                ),
              if (jobs != null)
                ListView.builder(
                    shrinkWrap: true,
                    itemCount: widget.showViewAll == true
                        ? min(4, jobs!.length)
                        : jobs?.length,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () {
                          Navigator.push(
                              context,
                              NextPageRoute(JobDetailsPage(
                                title: jobs?[index].name,
                                description: jobs?[index].description,
                                location: jobs?[index].location,
                                skillNames: jobs?[index].skillNames,
                                companyName: jobs?[index].organizedBy,
                                domain: jobs?[index].domainName,
                                companyThumbnail: jobs?[index].image,
                                experience: jobs?[index].experience,
                                
                                id: jobs?[index].id,
                                jobStatus: jobs?[index].jobStatus,
                                jobStatusNumeric: int.parse(
                                    '${jobs?[index].jobStatusNumeric ?? 0}'),
                              ))).then((value) {});
                        },
                        child: Container(
                          height: MediaQuery.of(context).size.height * 0.12,
                          width: MediaQuery.of(context).size.width * 0.9,
                          margin: const EdgeInsets.only(right: 12),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: index == 0
                                ? null
                                : Border.all(
                                    color: ColorConstants.GREY_4, width: 0.3),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    width: width(context) * 0.25,
                                    height: width(context) * 0.25,
                                    padding: EdgeInsets.only(
                                      right: 10.0,
                                      left: 10,
                                    ),
                                    child: jobs?[index].image != null
                                        ? Image.network(
                                            '${jobs?[index].image}',

                                            width: width(context) * 0.25,
                                            height: width(context) * 0.25,
                                            // fit: BoxFit.cover,
                                          )
                                        : Image.asset('assets/images/pb_2.png'),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 12),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: width(context) * 0.6,
                                          child: Text(
                                            '${jobs?[index].name}',
                                            style: Styles.bold(size: 14),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 2,
                                          ),
                                        ),
                                        SizedBox(
                                          height: 4,
                                        ),
                                        SizedBox(
                                            width: width(context) * 0.6,
                                            child: Text(
                                              '${jobs?[index].organizedBy}',
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: Styles.regular(
                                                  color: ColorConstants.GREY_3,
                                                  size: 12),
                                            )),
                                        SizedBox(
                                          height: height(context) * 0.015,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                // Image.asset(
                                                //   'assets/images/jobicon.png',
                                                //   height: 18,
                                                //   width: 18,
                                                // ),
                                                 Icon(Icons.work_outline, size: 16,
                                                      color:
                                                                    ColorConstants
                                                                        .GREY_6
                                                      ),
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                    left:
                                                        Utility().isRTL(context)
                                                            ? 0
                                                            : 5.0,
                                                    right:
                                                        Utility().isRTL(context)
                                                            ? 5.0
                                                            : 0.0,
                                                  ),
                                                  child: Text('${tr('exp')}: ',
                                                      style: Styles.regular(
                                                          size: 12,
                                                          color: ColorConstants
                                                              .GREY_6)),
                                                ),
                                                Text(
                                                    '${jobs?[index].experience} ${tr('yrs')}',
                                                    style: Styles.regular(
                                                        size: 12,
                                                        color: ColorConstants
                                                            .GREY_6)),
                                              ],
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 1.0),
                                              child: Icon(
                                                Icons.location_on_outlined,
                                                size: 16,
                                                color: ColorConstants.GREY_3,
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 8.0),
                                              child: Text(
                                                '${jobs?[index].location}',
                                                style: Styles.regular(
                                                    color:
                                                        ColorConstants.GREY_3,
                                                    size: 11),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
              if (jobs?.length != 0 && widget.showViewAll == true)
                Container(
                  color: ColorConstants.WHITE,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  child: ShaderMask(
                      blendMode: BlendMode.srcIn,
                      shaderCallback: (Rect bounds) {
                        return LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: <Color>[
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight()
                            ]).createShader(bounds);
                      },
                      child: InkWell(
                        onTap: () => Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => ViewAllPage(
                                        title: tr('popular_jobs_internship'),
                                        child: PopularInternship(
                                          showViewAll: false,
                                          jobs: jobs,
                                        ))))
                            .then((value) => getPopularJobInternship()),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'show_all',
                              style: Styles.regular(size: 12),
                            ).tr(),
                            SizedBox(width: 10),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 14,
                            )
                          ],
                        ),
                      )),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void getPopularJobInternship() {
    try {
      BlocProvider.of<HomeBloc>(context).add(PopularIntershipEvent());
    } catch (e) {
      print('Exception $e');
    }
  }

  void handlePopularJobInternship(PopularIntershipState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................PopularInternship");
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................PopularInternship");

          jobs = state.response?.data;

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
            FirebaseAnalytics.instance.logEvent(name: 'internship', parameters: {
            "ERROR": '${loginState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
