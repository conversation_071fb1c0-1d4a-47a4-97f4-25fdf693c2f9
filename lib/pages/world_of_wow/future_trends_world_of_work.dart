import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/domain_list_response.dart';
import 'package:masterg/pages/singularis/graph.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:shimmer/shimmer.dart';

class FutureTrendsWorldOfWork extends StatefulWidget {
  const FutureTrendsWorldOfWork({Key? key}) : super(key: key);

  @override
  State<FutureTrendsWorldOfWork> createState() =>
      _FutureTrendsWorldOfWorkState();
}

class _FutureTrendsWorldOfWorkState extends State<FutureTrendsWorldOfWork> {
  DomainListResponse? domainList;

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {
          BlocProvider.of<HomeBloc>(context).add(DomainListEvent());
        },
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is DomainListState) handleDomainListResponse(state);
            },
            child: Container(
              decoration: BoxDecoration(color: ColorConstants.GREY),
              height: Utility().isRTL(context)
                  ? MediaQuery.of(context).size.height * 0.2
                  : MediaQuery.of(context).size.height * 0.185,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: Utility().isRTL(context)
                            ? EdgeInsets.only(right: 15.0)
                            : EdgeInsets.only(left: 15.0),
                        child: SvgPicture.asset(
                          'assets/images/grf_job.svg',
                          height: 18,
                          width: 18,
                          allowDrawingOutsideViewBox: true,
                        ),
                      ),
                      Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 10,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'future_job_trend',
                                style: Styles.semibold(size: 14),
                              ).tr(),
                              Text(
                                'important_jobs_trends',
                                style: Styles.regular(size: 12),
                              ).tr(),
                            ],
                          )),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    /*child: Container(
                        height: Utility().isRTL(context)
                            ? MediaQuery.of(context).size.height * 0.11
                            : MediaQuery.of(context).size.height * 0.1,
                        child: domainList != null
                            ? ListView.builder(
                                itemCount: domainList?.data?.list!.length ?? 0,
                                scrollDirection: Axis.horizontal,
                                itemBuilder: (BuildContext context, int index) {
                                  return InkWell(
                                    onTap: () {
                                      FirebaseAnalytics.instance.logEvent(
                                          name: 'future_trend',
                                          parameters: {
                                            "trend": domainList!.data!.list![index].name,
                                            //"organization_id": domainList!.data!.list[index].organizationId
                                          });
                                      futureTrendsButtonSheet(
                                          domainList!.data!.list[index].name,
                                          domainList!.data!.list[index].jobCount
                                              .toString(),
                                          domainList!
                                              .data!.list[index].growthType,
                                          domainList!.data!.list[index].growth,
                                          domainList!.data!.list[index].id);
                                    },
                                    child: Container(
                                      width: MediaQuery.of(context).size.width *
                                          0.44,
                                      decoration: BoxDecoration(
                                          color: ColorConstants.WHITE,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                              color:
                                                  ColorConstants.List_Color)),
                                      margin: EdgeInsets.all(8),
                                      // color: Colors.red,
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                            left: 8.0,
                                            right: 8.0,
                                            top: 8.0,
                                            bottom: 8.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.4,
                                              child: Center(
                                                child: Text(
                                                  '${domainList!.data!.list[index].name}',
                                                  style: Styles.bold(
                                                      color: Color(0xff0E1638),
                                                      size: 13),
                                                  softWrap: true,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  maxLines: 1,
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              height: 5,
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  '${domainList!.data!.list[index].jobCount} ${tr('job_roles')} ',
                                                  style: Styles.regular(
                                                      color:
                                                          ColorConstants.GREY_3,
                                                      size: 11),
                                                ),
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                      left: !Utility()
                                                              .isRTL(context)
                                                          ? 0
                                                          : 8.0),
                                                  child: Text(
                                                    domainList!
                                                                .data!
                                                                .list[index]
                                                                .growthType ==
                                                            'up'
                                                        ? ' + ${domainList!.data!.list[index].growth}%'
                                                        : ' - ${domainList!.data!.list[index].growth}%',
                                                    style: Styles.regular(
                                                        color: domainList!
                                                                    .data!
                                                                    .list[index]
                                                                    .growthType ==
                                                                'up'
                                                            ? ColorConstants
                                                                .GREEN
                                                            : ColorConstants
                                                                .RED,
                                                        size: 11),
                                                  ),
                                                ),
                                                Transform.translate(
                                                    offset: Offset(
                                                        Utility().isRTL(context)
                                                            ? 10.0
                                                            : 0,
                                                        0),
                                                    child: domainList!
                                                                .data!
                                                                .list[index]
                                                                .growthType ==
                                                            'up'
                                                        ? Icon(
                                                            Icons
                                                                .arrow_drop_up_outlined,
                                                            color: Colors.green,
                                                            size: 20,
                                                          )
                                                        : Icon(
                                                            Icons
                                                                .arrow_drop_down,
                                                            color: Colors.red,
                                                            size: 20,
                                                          )),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                })
                            : ListView.separated(
                                scrollDirection: Axis.horizontal,
                                itemBuilder: (context, index) => SizedBox(
                                      width: MediaQuery.of(context).size.width *
                                          0.44,
                                      height: 100,
                                      child: Shimmer.fromColors(
                                          baseColor: Colors.grey[300]!,
                                          highlightColor: Colors.grey[100]!,
                                          enabled: true,
                                          child: Card()),
                                    ),
                                separatorBuilder: (context, i) =>
                                    SizedBox(width: 20),
                                itemCount: 3)),*/
                  ),
                  SizedBox(
                    height: 20,
                  ),
                ],
              ),
            )));
  }

  void handleDomainListResponse(DomainListState state) {
    var popularCompetitionState = state;
    setState(() {
      switch (popularCompetitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading DomainList....................");

          break;
        case ApiStatus.SUCCESS:
          Log.v("popularCompetitionState....................");
          domainList = state.response;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Popular CompetitionListIDState ..........................${popularCompetitionState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'future_trends', parameters: {
            "ERROR": '${popularCompetitionState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  futureTrendsButtonSheet(String title, String jobsCount, String growthType,
      String growth, int domainId) {
    return showModalBottomSheet(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30), topRight: Radius.circular(30))),
        backgroundColor: Colors.white,
        context: context,
        // useRootNavigator: true,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return Stack(
            children: [
              Positioned(
                right: 10,
                child: IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(Icons.close)),
              ),
              Container(
                height: MediaQuery.of(context).size.height * 0.7,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width * 0.6,
                      decoration: BoxDecoration(
                          color: ColorConstants.WHITE,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: ColorConstants.List_Color)),
                      margin: EdgeInsets.all(8),
                      // color: Colors.red,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                        child: Column(
                          children: [
                            Center(
                              child: Text(
                                '$title',
                                style: Styles.bold(
                                    color: Color(0xff0E1638), size: 13),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(
                              height: 5,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '$jobsCount ${tr('job_roles')} ',
                                  style: Styles.regular(
                                      color: ColorConstants.GREY_3, size: 11),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                      left:
                                          !Utility().isRTL(context) ? 0 : 8.0),
                                  child: Text(
                                    growthType == 'up'
                                        ? ' + $growth%'
                                        : ' - $growth%',
                                    style: Styles.regular(
                                        color: growthType == 'up'
                                            ? ColorConstants.GREEN
                                            : ColorConstants.RED,
                                        size: 11),
                                  ),
                                ),
                                Transform.translate(
                                    offset: Offset(
                                        Utility().isRTL(context) ? 10.0 : 0, 0),
                                    child: growthType == 'up'
                                        ? Icon(
                                            Icons.arrow_drop_up_outlined,
                                            color: Colors.green,
                                            size: 20,
                                          )
                                        : Icon(
                                            Icons.arrow_drop_down,
                                            color: Colors.red,
                                            size: 20,
                                          )),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                        child: LineChartWidget(
                      domainid: domainId,
                    )),
                  ],
                ),
              ),
            ],
          );
        });
  }
}
