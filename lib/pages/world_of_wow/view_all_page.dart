import 'package:flutter/material.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class ViewAllPage extends StatelessWidget {
  final Widget child;
  final String title;

  const ViewAllPage({Key? key, required this.child, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorConstants.BG_GREY,
        elevation: 0.0,
        flexibleSpace: Container(
          color: ColorConstants.WHITE,
        ),
        centerTitle: true,
        leading: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(
              Icons.arrow_back_ios,
              color: Colors.black,
            )),
        title: Text(
          '$title',
          style: Styles.bold(),
        ),
      ),
      body: child,
    );
  }
}
