import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/onboard_sessions.dart';
import 'package:masterg/data/models/response/home_response/popular_courses_response.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/card_loader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/gschool_widget/date_picker.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/call_once.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui' as ui;

class MyClasses extends StatefulWidget {
  const MyClasses({Key? key}) : super(key: key);

  @override
  _MyClassesState createState() => _MyClassesState();
}

class _MyClassesState extends State<MyClasses> {
  bool isProgramListLoading = true;
  // List<MProgram> courseList1;
  List<Liveclass>? liveclassList;
  List<PopularCourses>? popularcourses;
  List<Recommended>? recommendedcourses;
  List<OtherLearners>? otherLearners;
  List<ShortTerm>? shortTerm;
  List<HighlyRated>? highlyRated;
  List<MostViewed>? mostViewed;
  late DateTime selectedDate;
  bool haveData = false;

  bool _isJoyCategoryLoading = true;

  Box? box;
  String selectedOption = "All";
  bool selectedCalanderView = false;
  String? currentZoomUrl;
  String? currentOpenUrl;

  @override
  void initState() {
    _getLiveClass();
    super.initState();
  }

  /*_showPopUpMenu(Offset offset) async {
    final screenSize = MediaQuery.of(context).size;
    double left = offset.dx;
    double top = offset.dy;
    double right = screenSize.width - offset.dx;
    double bottom = screenSize.height - offset.dy;

    showMenu<String>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      position: RelativeRect.fromLTRB(left, top, right, bottom),
      items: [
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/live_icon.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('ongoing').tr()
            ]),
            value: '1'),
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/upcoming_live.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('${tr('upcoming_class')}')
            ]),
            value: '2'),
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/completed_icon.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('class_completed').tr()
            ]),
            value: '3'),
      ],
      elevation: 8.0,
    );
  }*/
  //Add New Code 20-Aug-2025
  _showPopUpMenu(Offset offset) async {
    final screenSize = MediaQuery.of(context).size;
    showMenu<String>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      position: RelativeRect.fromLTRB(
        offset.dx,
        offset.dy,
        screenSize.width - offset.dx,
        screenSize.height - offset.dy,
      ),
      items: _buildMenuItems(),
      elevation: 8.0,
    );
  }

  List<PopupMenuItem<String>> _buildMenuItems() {
    final menuData = [
      ('assets/images/live_icon.svg', 'ongoing', '1'),
      ('assets/images/upcoming_live.svg', 'upcoming_class', '2'),
      ('assets/images/completed_icon.svg', 'class_completed', '3'),
    ];

    return menuData.map((data) => PopupMenuItem<String>(
      enabled: false,
      value: data.$3,
      child: Row(children: [
        SvgPicture.asset(
          data.$1,
          width: 20,
          height: 20,
          allowDrawingOutsideViewBox: true,
        ),
        SizedBox(width: 20),
        Text(data.$2).tr(),
      ]),
    )).toList();
  }


  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('my_classes', style: Styles.bold(size: 18)).tr(),
        actions: [
          GestureDetector(
            onTapDown: (TapDownDetails detail) {
              _showPopUpMenu(detail.globalPosition);
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 15),
              child: SvgPicture.asset(
                'assets/images/info_icon.svg',
                height: 22,
                width: 22,
                allowDrawingOutsideViewBox: true,
              ),
            ),
          )
        ],
        centerTitle: false,
        backgroundColor: Colors.white,
        elevation: 0.0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Colors.black,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: _mainBody(),
    );
  }

  bool checkViewDate(startDate) {
    String startDateString =
        Utility.convertDateFromMillis(startDate, 'dd-MM-yyyy');
    final DateFormat formatter = DateFormat('dd-MM-yyyy');
    final String formatted = formatter.format(selectedDate);
    if (startDateString == formatted)
      return true;
    else
      return false;
  }

  _mainBody() {
    return MultiProvider(
        providers: [
          ChangeNotifierProvider<LiveclassModel>(
            create: (context) => LiveclassModel([]),
          ),
        ],
        child: Consumer<LiveclassModel>(
          builder: (context, liveClassModel, child) => BlocManager(
            initState: (BuildContext context) {},
            child: BlocListener<HomeBloc, HomeState>(
                listener: (context, state) {
                  if (state is getLiveClassState) {
                    _handleLiveClassResponse(state, liveClassModel);
                  }
                  if (state is ZoomOpenUrlState) handleOpenUrlState(state);
                },

                // child: Text('${liveClassModel.liveclass?.length}'),
                child: !_isJoyCategoryLoading
                    ? liveclassList?.length != 0
                        ? _getClasses(liveClassModel, currentIndiaTime!)
                        : SizedBox(
                            height: height(context) * 0.8,
                            child: Center(
                                child: Text(
                              'no_active_class',
                              style: Styles.bold(size: 16),
                            ).tr()))
                    : CardLoader()),
          ),
        ));
  }

  Widget _getClasses(LiveclassModel listClassModel, DateTime now) {
    List<String> months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];

    List<Liveclass>? liveClassTempList = liveclassList;
    if (selectedCalanderView) {
      liveClassTempList?.sort((a, b) => a.fromDate!.compareTo(b.fromDate!));
      DateTime date;
      //>= old condition year, month or day
      liveClassTempList = liveClassTempList?.where((element) {
        date = DateTime.fromMillisecondsSinceEpoch(element.fromDate! * 1000);
        if (date.year == selectedDate.year) {
          if (date.month == selectedDate.month) {
            if (date.day == selectedDate.day) return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
        return false;
      }).toList();
    }

    List<Liveclass>? list = [];

    String searchKey = selectedOption == 'Ongoing' ? 'Live' : selectedOption;
    if (selectedOption != 'All')
      try {
        for (var element in liveClassTempList!) {
          if (element.liveclassStatus == searchKey) {
            list.add(element);
          }
        }
      } catch (e) {}
    else
      list = liveClassTempList;

    if (list != null)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        listClassModel.refreshList(list);
      });

    bool showExpiredClass = true;
    try {
      for (var currClass in listClassModel.list!) {
        if (!Utility.isExpired(currClass.endDate!, now)) {
          showExpiredClass = false;
          break;
        }
      }
    } catch (e) {}
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(color: ColorConstants.GREY),
      child: Column(
        children: [
          Row(
            children: [
              if(selectedCalanderView == false)
              Text('${tr('sort_by')}: ', style: Styles.regular(size: 14)),
              if(selectedCalanderView == false)
              DropdownButton<String>(
                alignment: AlignmentDirectional.centerStart,
                isExpanded: false,
                underline: SizedBox(),
                hint: Text('${selectedOption.toLowerCase()}',
                        style: Styles.bold(size: 14))
                    .tr(),
                items: <String>['All', 'Upcoming', 'Completed', 'Ongoing']
                    .map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value.toLowerCase()).tr(),
                  );
                }).toList(),
                onChanged: (_) {
                  // selectedCalanderView = false;
                  selectedOption = _!;

                  List<Liveclass>? list = [];
                  String searchKey =
                      selectedOption == 'Ongoing' ? 'Live' : selectedOption;

                  if (selectedOption != 'All')
                    try {
                      for (var element in liveclassList!) {
                        if (element.liveclassStatus == searchKey) {
                          list.add(element);
                        }
                      }
                    } catch (e) {}
                  else
                    list = liveclassList;
                  listClassModel.refreshList(list);
                  setState(() {});
                },
              ),
              Expanded(child: SizedBox(height: selectedCalanderView == true ? 50 : 0,)),
              InkWell(
                onTap: () {
                  setState(() {
                    selectedCalanderView = false;
                  });
                },
                child: !selectedCalanderView
                    ? SvgPicture.asset(
                        'assets/images/selected_listview.svg',
                        height: 16,
                        width: 16,
                        allowDrawingOutsideViewBox: true,
                      )
                    : SvgPicture.asset(
                        'assets/images/unselected_listview.svg',
                        height: 16,
                        width: 16,
                        allowDrawingOutsideViewBox: true,
                      ),
              ),
              SizedBox(width: 10),
              InkWell(
                onTap: () {
                  setState(() {
                    selectedCalanderView = true;
                  });
                },
                child: selectedCalanderView
                    ? SvgPicture.asset(
                        'assets/images/selected_calender.svg',
                        height: 20,
                        width: 20,
                        allowDrawingOutsideViewBox: true,
                      )
                    : SvgPicture.asset(
                        'assets/images/unselected_calender.svg',
                        height: 20,
                        width: 20,
                        allowDrawingOutsideViewBox: true,
                      ),
              )
            ],
          ),
          if (selectedCalanderView)
            Calendar(
              sendValue: (DateTime date) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  setState(() {
                    selectedDate = date;
                  });
                });
              },
            ),
          listClassModel.list?.length != 0
              ? Expanded(
                  child: ListView.builder(
                    itemBuilder: (BuildContext context, int index) {
                      int classStatus = Utility.classStatus(
                          listClassModel.list![index].fromDate!,
                          listClassModel.list![index].endDate!,
                          currentIndiaTime!);

                      bool show;
                      if (selectedCalanderView) {
                        show = checkViewDate(
                            listClassModel.list![index].fromDate!);
                      } else {
                        show = true;
                      }
                      return Visibility(
                        visible: showExpiredClass
                            ? showExpiredClass
                            : show
                                ? !selectedCalanderView
                                    ? !Utility.isExpired(
                                        listClassModel.list![index].endDate!,
                                        now)
                                    : true
                                : false,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                                padding: EdgeInsets.all(10),
                                margin: EdgeInsets.symmetric(vertical: 10),
                                decoration: BoxDecoration(
                                    color: ColorConstants.WHITE,
                                    border: Border.all(
                                        color: Colors.grey[350]!, width: 1),
                                    borderRadius: BorderRadius.circular(10)),
                                child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      listClassModel.list![index]
                                                      .liveclassStatus!
                                                      .toLowerCase() ==
                                                  'live' ||
                                              listClassModel.list![index]
                                                      .liveclassStatus!
                                                      .toLowerCase() ==
                                                  'completed'
                                          ? Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                if (classStatus == 0) ...[
                                                  SvgPicture.asset(
                                                    'assets/images/live_icon.svg',
                                                    width: 20,
                                                    height: 20,
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  )
                                                ] else if (classStatus ==
                                                    1) ...[
                                                  SvgPicture.asset(
                                                    'assets/images/empty_circle.svg',
                                                    width: 20,
                                                    height: 20,
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  )
                                                ] else
                                                  Icon(
                                                    Icons.check_circle,
                                                    color: ColorConstants.GREEN,
                                                    size: 20.0,
                                                  ),
                                                SizedBox(width: 5),
                                                classStatus != 2
                                                    ? Text(
                                                            listClassModel
                                                                        .list![
                                                                            index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    'offlineclass'
                                                                ? 'ongoing'
                                                                : 'Live_now',
                                                            style: Styles.regular(
                                                                size: 12,
                                                                color:
                                                                    ColorConstants
                                                                        .RED))
                                                        .tr()
                                                    : Row(
                                                        children: [
                                                          CallOnceWidget(
                                                            onCallOnce: () {
                                                            },
                                                            child: StrToTime(
                                                              appendString:
                                                                  Utility().isRTL(
                                                                          context)
                                                                      ? ''
                                                                      : ' - ',
                                                              dateFormat:
                                                                  'hh:mm a',
                                                              time:
                                                                  '${listClassModel.list![index].startTime}',
                                                              textStyle: Styles
                                                                  .regular(
                                                                      size: 14),
                                                            ),
                                                          ),
                                                          CallOnceWidget(
                                                            onCallOnce: () {
                                                            },
                                                            child: StrToTime(
                                                              appendString:
                                                                  Utility().isRTL(
                                                                          context)
                                                                      ? ' - '
                                                                      : '',
                                                              dateFormat:
                                                                  'hh:mm a',
                                                              time:
                                                                  '${listClassModel.list![index].endTime}',
                                                              textStyle: Styles
                                                                  .regular(
                                                                      size: 14),
                                                            ),
                                                          ),
                                                          Text(
                                                            ' ${Utility().isRTL(context) ? '' : '|'} ${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M','en_IN').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))) - 1]} ${Utility().isRTL(context) ? '|' : ''}',
                                                            style:
                                                                Styles.regular(
                                                                    size: 14),
                                                            textDirection: ui
                                                                .TextDirection
                                                                .ltr,
                                                          ),
                                                        ],
                                                      ),
                                                // Text(
                                                //     '${Utility.getTime(timestamp: 1671089400, dateFormat: 'yyyy-MM-dd')} - ${listClassModel.list![index].endTimeTs} |${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))) - 1]}',
                                                //     style: Styles.regular(
                                                //         size: 14),
                                                //     textDirection: ui
                                                //         .TextDirection.ltr,
                                                //   ),

                                                // Text(
                                                //     '${listClassModel.list![index].startTime} - ${listClassModel.list![index].endTime} |${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))) - 1]}',
                                                //     style: Styles.regular(
                                                //         size: 14),
                                                //     textDirection: ui
                                                //         .TextDirection.ltr,
                                                //   ),
                                                Expanded(child: SizedBox()),
                                                Container(
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      color: ColorConstants
                                                          .BG_GREY),
                                                  padding: EdgeInsets.symmetric(
                                                      vertical: 8,
                                                      horizontal: 18),
                                                  child: Text(
                                                          listClassModel
                                                                      .list![
                                                                          index]
                                                                      .contentType!
                                                                      .toLowerCase() ==
                                                                  'otherclass'
                                                              ? 'weblink'
                                                              : listClassModel
                                                                          .list![
                                                                              index]
                                                                          .contentType!
                                                                          .toLowerCase() ==
                                                                      'teamsclass'
                                                                  ? 'teams'
                                                                  : listClassModel.list![index].contentType!.toLowerCase() ==
                                                                              'liveclass' ||
                                                                          listClassModel.list![index].contentType!
                                                                                  .toLowerCase() ==
                                                                              'zoomclass'
                                                                                  'teamsclass'
                                                                      ? listClassModel.list![index].liveclassStatus!.toLowerCase() ==
                                                                              'completed'
                                                                          ? 'live'
                                                                          : 'Live_now'
                                                                      : 'classroom',
                                                          style: Styles.regular(
                                                              size: 10,
                                                              color:
                                                                  ColorConstants
                                                                      .BLACK))
                                                      .tr(),
                                                ),
                                              ],
                                            )
                                          : listClassModel.list![index]
                                                      .liveclassStatus!
                                                      .toLowerCase() ==
                                                  'upcoming'
                                              ? Row(children: [
                                                  SvgPicture.asset(
                                                    'assets/images/upcoming_live.svg',
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  ),
                                                  SizedBox(width: 5),
                                                  CallOnceWidget(
                                                    onCallOnce: () {},
                                                    child: StrToTime(
                                                      appendString: Utility()
                                                              .isRTL(context)
                                                          ? ''
                                                          : ' - ',
                                                      dateFormat: 'hh:mm a',
                                                      time:
                                                          '${listClassModel.list![index].startTime}',
                                                      textStyle: Styles.regular(
                                                          size: 14),
                                                    ),
                                                  ),
                                                  CallOnceWidget(
                                                    onCallOnce: () {},
                                                    child: StrToTime(
                                                      appendString: Utility()
                                                              .isRTL(context)
                                                          ? ' - '
                                                          : '',
                                                      dateFormat: 'hh:mm a',
                                                      time:
                                                          '${listClassModel.list![index].endTime}',
                                                      textStyle: Styles.regular(
                                                          size: 14),
                                                    ),
                                                  ),
                                                  Text(
                                                    ' ${Utility().isRTL(context) ? '' : '|'} ${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))) - 1]} ${Utility().isRTL(context) ? '|' : ''}',
                                                    style: Styles.regular(
                                                        size: 14),
                                                    textDirection:
                                                        ui.TextDirection.ltr,
                                                  ),

                                                  // Text(
                                                  //   '${listClassModel.list![index].startTime} - ${listClassModel.list![index].endTime} |${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M').format(DateTime.fromMillisecondsSinceEpoch(listClassModel.list![index].fromDate! * 1000))) - 1]}',
                                                  //   style: Styles.regular(
                                                  //       size: 14),
                                                  //   textDirection:
                                                  //       ui.TextDirection.ltr,
                                                  // ),
                                                  Expanded(child: SizedBox()),
                                                  Container(
                                                    decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                        color: ColorConstants
                                                            .BG_GREY),
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 8,
                                                            horizontal: 18),
                                                    child: Text(
                                                            listClassModel
                                                                        .list![
                                                                            index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    'otherclass'
                                                                ? 'weblink'
                                                                : listClassModel
                                                                            .list![
                                                                                index]
                                                                            .contentType!
                                                                            .toLowerCase() ==
                                                                        'teamsclass'
                                                                    ? 'teams'
                                                                    : listClassModel.list![index].contentType ==
                                                                                'liveclass' ||
                                                                            listClassModel.list![index].contentType ==
                                                                                'zoomclass'
                                                                        ? 'live'
                                                                        : 'classroom',
                                                            style: Styles.regular(
                                                                size: 10,
                                                                color:
                                                                    ColorConstants
                                                                        .BLACK))
                                                        .tr(),
                                                  ),
                                                ])
                                              : SizedBox(),
                                      SizedBox(height: 10),
                                      Text(
                                          '${listClassModel.list![index].name}',
                                          style: Styles.semibold(size: 16)),
                                      SizedBox(height: 9),
                                      Text(
                                        '${listClassModel.list![index].description}',
                                        style: Styles.regular(size: 14),
                                      ),
                                      SizedBox(height: 15),
                                      Row(
                                        children: [
                                          listClassModel.list![index]
                                                          .trainerName !=
                                                      null &&
                                                  listClassModel.list![index]
                                                          .trainerName !=
                                                      ''
                                              ? Text('${tr('by')} ${listClassModel.list![index].trainerName}', style: Styles.regular(size: 12))
                                              //? Text('${tr('by')} ${Utility().decrypted128('${listClassModel.list![index].trainerName}')} ', style: Styles.regular(size: 12))
                                              : Text(''),
                                          Expanded(child: SizedBox()),
                                          listClassModel.list![index]
                                                          .liveclassStatus!
                                                          .toLowerCase() ==
                                                      'live' ||
                                                  (currentIndiaTime!
                                                                  .add(Duration(
                                                                      minutes:
                                                                          15))
                                                                  .millisecondsSinceEpoch ~/
                                                              1000 >=
                                                          liveclassList![index]
                                                              .startTimeTs! &&
                                                      currentIndiaTime!
                                                                  .millisecondsSinceEpoch ~/
                                                              1000 <
                                                          liveclassList![index]
                                                              .endTimeTs!)
                                              // (currentIndiaTime!
                                              //                 .add(Duration(
                                              //                     minutes: listClassModel.list![index].duration! +
                                              //                         15))
                                              //                 .millisecondsSinceEpoch /
                                              //             1000 >
                                              //         listClassModel
                                              //             .list![index]
                                              //             .endDate! &&
                                              //     listClassModel
                                              //             .list![index]
                                              //             .liveclassStatus!
                                              //             .toLowerCase() ==
                                              //         'upcoming')
                                              ? InkWell(
                                                  onTap: () {
                                                    if (liveclassList![index]
                                                            .contentType!
                                                            .toLowerCase() ==
                                                        "offlineclass") return;
                                                    if (listClassModel
                                                            .list![index]
                                                            .contentType!
                                                            .toLowerCase() ==
                                                        "liveclass")
                                                      launchUrl(
                                                          Uri.parse(
                                                              '${listClassModel.list![index].url}'),
                                                          mode: LaunchMode
                                                              .externalApplication);
                                                    else if (listClassModel
                                                                .list![index]
                                                                .contentType!
                                                                .toLowerCase() ==
                                                            "zoomclass" ||
                                                        listClassModel
                                                                .list![index]
                                                                .contentType!
                                                                .toLowerCase() ==
                                                            'teamsclass' ||
                                                        listClassModel
                                                                .list![index]
                                                                .contentType!
                                                                .toLowerCase() ==
                                                            'otherclass') {
                                                      setState(() {
                                                        currentZoomUrl =
                                                            listClassModel
                                                                .list![index]
                                                                .zoomUrl;
                                                        currentOpenUrl =
                                                            listClassModel
                                                                .list![index]
                                                                .openUrl;
                                                      });

                                                      if (currentZoomUrl !=
                                                          null) {
                                                        BlocProvider.of<
                                                                    HomeBloc>(
                                                                context)
                                                            .add(
                                                                ZoomOpenUrlEvent(
                                                          contentId:
                                                              listClassModel
                                                                  .list![index]
                                                                  .id,
                                                        ));
                                                        launchUrl(
                                                            Uri.parse(
                                                                '$currentZoomUrl'),
                                                            mode: LaunchMode
                                                                .externalApplication);
                                                      } else {
                                                        BlocProvider.of<
                                                                    HomeBloc>(
                                                                context)
                                                            .add(ZoomOpenUrlEvent(
                                                                contentId:
                                                                    listClassModel
                                                                        .list![
                                                                            index]
                                                                        .id));
                                                      }
                                                    } else
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                              SnackBar(
                                                        content:
                                                            Text("coming_soon")
                                                                .tr(),
                                                      ));
                                                  },
                                                  child: Container(
                                                    decoration: BoxDecoration(
                                                      gradient: LinearGradient(
                                                          colors: listClassModel.list![index].contentType!.toLowerCase() == "liveclass" ||
                                                                  listClassModel
                                                                          .list![
                                                                              index]
                                                                          .contentType!
                                                                          .toLowerCase() ==
                                                                      "zoomclass" ||
                                                                  listClassModel
                                                                          .list![
                                                                              index]
                                                                          .contentType!
                                                                          .toLowerCase() ==
                                                                      'teamsclass' ||
                                                                  listClassModel
                                                                          .list![
                                                                              index]
                                                                          .contentType!
                                                                          .toLowerCase() ==
                                                                      'otherclass'
                                                              ? [
                                                                  ColorConstants()
                                                                      .gradientLeft(),
                                                                  ColorConstants()
                                                                      .gradientRight(),
                                                                ]
                                                              : [
                                                                  liveclassList![index]
                                                                              .contentType!
                                                                              .toLowerCase() ==
                                                                          "offlineclass"
                                                                      ? ColorConstants
                                                                          .WHITE
                                                                      : ColorConstants
                                                                          .GREY_3,
                                                                  liveclassList![index]
                                                                              .contentType!
                                                                              .toLowerCase() ==
                                                                          "offlineclass"
                                                                      ? ColorConstants
                                                                          .WHITE
                                                                      : ColorConstants
                                                                          .GREY_3,
                                                                ]),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    child: Padding(
                                                        child: Text(
                                                            listClassModel
                                                                            .list![
                                                                                index]
                                                                            .contentType!
                                                                            .toLowerCase() ==
                                                                        'otherclass' ||
                                                                    listClassModel
                                                                            .list![
                                                                                index]
                                                                            .contentType!
                                                                            .toLowerCase() ==
                                                                        "liveclass" ||
                                                                    listClassModel
                                                                            .list![
                                                                                index]
                                                                            .contentType!
                                                                            .toLowerCase() ==
                                                                        "zoomclass" ||
                                                                    listClassModel
                                                                            .list![
                                                                                index]
                                                                            .contentType!
                                                                            .toLowerCase() ==
                                                                        'teamsclass'
                                                                ? "join_now"
                                                                : 'in_progress',
                                                            style:
                                                                Styles.regular(
                                                              size: 12,
                                                              color: listClassModel.list![index].contentType!.toLowerCase() == "liveclass" ||
                                                                      listClassModel
                                                                              .list![
                                                                                  index]
                                                                              .contentType!
                                                                              .toLowerCase() ==
                                                                          "zoomclass" ||
                                                                      listClassModel
                                                                              .list![
                                                                                  index]
                                                                              .contentType!
                                                                              .toLowerCase() ==
                                                                          'teamsclass' ||
                                                                      listClassModel
                                                                              .list![
                                                                                  index]
                                                                              .contentType!
                                                                              .toLowerCase() ==
                                                                          'otherclass'
                                                                  ? ColorConstants
                                                                      .WHITE
                                                                  : ColorConstants
                                                                      .BLACK,
                                                            )).tr(),
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                horizontal: liveclassList![index]
                                                                            .contentType!
                                                                            .toLowerCase() ==
                                                                        "offlineclass"
                                                                    ? 4
                                                                    : 18,
                                                                vertical: 8)),
                                                  ))
                                              : listClassModel.list![index]
                                                          .liveclassStatus!
                                                          .toLowerCase() !=
                                                      'completed'
                                                  ? Text('${tr('upcoming')}',
                                                      style: Styles.regular(size: 12))
                                                  : SizedBox(),

                                          // if (listClassModel.list![index]
                                          //             .liveclassStatus!
                                          //             .toLowerCase() !=
                                          //         'completed' &&
                                          //     currentIndiaTime!
                                          //                 .millisecondsSinceEpoch ~/
                                          //             1000 <
                                          //         DateTime.fromMillisecondsSinceEpoch(
                                          //                 liveclassList![index]
                                          //                     .startTimeTs! * 1000)
                                          //             .subtract(
                                          //                 Duration(minutes: 15))
                                          //             .millisecondsSinceEpoch

                                          // !(currentIndiaTime!
                                          //                 .add(Duration(
                                          //                     minutes:
                                          //                         15))
                                          //                 .millisecondsSinceEpoch ~/
                                          //             1000 >=
                                          //         liveclassList![
                                          //                 index]
                                          //             .startTimeTs! &&
                                          //     currentIndiaTime!.millisecondsSinceEpoch ~/
                                          //             1000 <
                                          //         liveclassList![
                                          //                 index]
                                          //             .endTimeTs!
                                          //             )
                                          // )
                                          // Text('${tr('upcoming')}',
                                          //     style:
                                          //         Styles.regular(size: 12)),
                                          Visibility(
                                              child: Text('concluded'.tr(),
                                                  style:
                                                      Styles.regular(size: 12)),
                                              visible: listClassModel
                                                      .list![index]
                                                      .liveclassStatus!
                                                      .toLowerCase() ==
                                                  'completed')
                                        ],
                                      )
                                    ])),
                          ],
                        ),
                      );
                    },
                    itemCount: listClassModel.list?.length ?? 0,
                    scrollDirection: Axis.vertical,
                  ),
                )
              : Expanded(
                  child: Container(
                  height: double.infinity,
                  child: Center(
                    child: Text(
                      'no_active_class',
                      style: Styles.bold(size: 16),
                      textAlign: TextAlign.center,
                    ).tr(),
                  ),
                )),
        ],
      ),
    );
  }

  void _getLiveClass() {
    BlocProvider.of<HomeBloc>(context).add(getLiveClassEvent());
  }

  void _handleLiveClassResponse(getLiveClassState state, LiveclassModel model) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          _isJoyCategoryLoading = true;
          break;
        case ApiStatus.SUCCESS:
          selectedDate = currentIndiaTime!;
          Log.v(state.response!.data!.modules!.liveclass.toString());

          liveclassList = state.response!.data!.modules!.liveclass;

          List<Liveclass>? list = [];

          String searchKey =
              selectedOption == 'Ongoing' ? 'Live' : selectedOption;

          if (selectedOption != 'All')
            for (var element in liveclassList!) {
              if (element.liveclassStatus == searchKey) {
                list.add(element);
              }
            }
          else
            list = liveclassList;
          if (list?.length == 0)
            haveData = false;
          else
            haveData = true;
          model.refreshList(list);

          _isJoyCategoryLoading = false;
          break;
        case ApiStatus.ERROR:
          _isJoyCategoryLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          FirebaseAnalytics.instance.logEvent(name: 'my_classess', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleOpenUrlState(ZoomOpenUrlState state) {
    switch (state.apiState) {
      case ApiStatus.LOADING:
        Log.v("Zoom Open Url Loading....................");

        break;
      case ApiStatus.SUCCESS:
        Log.v("Zoom Open Url Success.................... agian");
        _isJoyCategoryLoading = false;
        setState(() {});
        if (currentZoomUrl != null) return;

        if (state.response?.status == 0) {
          if (currentOpenUrl != null)
            launchUrl(Uri.parse('$currentOpenUrl'),
                mode: LaunchMode.externalApplication);
          else if (currentZoomUrl != null)
            launchUrl(Uri.parse('$currentZoomUrl'),
                mode: LaunchMode.externalApplication);
        } else if (state.response?.data?.list?.joinUrl != null)
          launchUrl(Uri.parse('${state.response?.data?.list?.joinUrl}'),
              mode: LaunchMode.externalApplication);
        else if (currentOpenUrl != null)
          launchUrl(Uri.parse('$currentOpenUrl'),
              mode: LaunchMode.externalApplication);

        break;

      case ApiStatus.ERROR:
        Log.v("Zoom open url Error..........................");
        FirebaseAnalytics.instance.logEvent(name: 'my_classess', parameters: {
          "ERROR": '${state.response?.error}',
        });
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }
}
