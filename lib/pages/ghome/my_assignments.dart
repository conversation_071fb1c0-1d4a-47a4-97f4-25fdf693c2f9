import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_constants.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/my_assignment_response.dart';
import 'package:masterg/data/providers/my_assignment_detail_provider.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/card_loader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/gschool_widget/date_picker.dart';
import 'package:masterg/pages/training_pages/mg_assignment_detail_page.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;

class MyAssignmentPage extends StatefulWidget {
  bool? isViewAll;
  bool? fromDashboard;

  Drawer? drawerWidget;

  MyAssignmentPage(
      {this.isViewAll, this.drawerWidget, this.fromDashboard = false});

  @override
  _MyAssignmentPageState createState() => _MyAssignmentPageState();
}

class _MyAssignmentPageState extends State<MyAssignmentPage> {
  List<AssignmentList>? assignmentList = [];

  int? categoryId = 16;
  Box? box;

  int selectedIndex = 0;
  String selectedOption = 'All';
  bool selectedCalanderView = false;
  DateTime? selectedDate = currentIndiaTime;
  bool showExpAs = true;
  bool assignmentVisible = false;

  @override
  void initState() {
    super.initState();
    if(currentIndiaTime == null){
      currentIndiaTime = DateTime.now();
    }
    _getHomeData();
    categoryId = Utility.getCategoryValue(ApiConstants.ANNOUNCEMENT_TYPE);
  }

  _showPopUpMenu(Offset offset) async {
    final screenSize = MediaQuery.of(context).size;
    double left = offset.dx;
    double top = offset.dy;
    double right = screenSize.width - offset.dx;
    double bottom = screenSize.height - offset.dy;

    showMenu<String>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      position: RelativeRect.fromLTRB(left, top, right, bottom),
      items: [
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/upcoming_live.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('upcoming_assignment').tr()
            ]),
            value: '1', enabled: false),
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/completed_icon.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('assignment_completed').tr()
            ]),
            value: '2', enabled: false),
        PopupMenuItem<String>(
            child: Row(children: [
              SvgPicture.asset(
                'assets/images/pending_icon.svg',
                width: 20,
                height: 20,
                allowDrawingOutsideViewBox: true,
              ),
              SizedBox(width: 20),
              Text('assignment_pending').tr()
            ]),
            value: '3', enabled: false),
      ],
      elevation: 8.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.GREY,
      appBar: widget.fromDashboard == true
          ? PreferredSize(
              preferredSize: const Size.fromHeight(0.0),
              child: SizedBox(),
            )
          : AppBar(
              title: Text('my_assignment', style: Styles.bold(size: 18)).tr(),
              centerTitle: false,
              backgroundColor: Colors.white,
              elevation: 0.0,
              actions: [
                GestureDetector(
                  onTapDown: (TapDownDetails detail) {
                    _showPopUpMenu(detail.globalPosition);
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 15),
                    child: SvgPicture.asset(
                      'assets/images/info_icon.svg',
                      height: 22,
                      width: 22,
                      allowDrawingOutsideViewBox: true,
                    ),
                  ),
                )
              ],
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Colors.black,
                ),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ),
      body: _mainBody(),
    );
  }

  void _getHomeData() async {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context).add(MyAssignmentEvent(box: box));
    setState(() {});
  }

  _mainBody() {
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is MyAssignmentState) {
              _handleAnnouncmentData(state);
            }
          },
          child: _announenmentList(),
        ));
  }

  void _handleAnnouncmentData(MyAssignmentState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          break;
        case ApiStatus.SUCCESS:
          assignmentList!.clear();

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          Log.v(
              "ErrorAnnoucement..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'my_assignment', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  // bool checkViewDate(endDate) {
  //   String endDateString = Utility.convertDateFromMillis(endDate, 'dd-MM-yyyy');
  //   final DateFormat formatter = DateFormat('dd-MM-yyyy');
  //   final String formatted = formatter.format(selectedDate!);
  //   // return true;
  //   if (endDateString == formatted)
  //     return true;
  //   else
  //     return false;
  // }

  bool checkViewDate(int? endDate, int? startDate) {
    if (startDate == null || endDate == null || selectedDate == null) return false;

    DateTime start = DateTime.fromMillisecondsSinceEpoch(startDate * 1000);
    DateTime end = DateTime.fromMillisecondsSinceEpoch(endDate * 1000);

    DateTime selected = selectedDate!; // assuming it's a DateTime

    //you need add extra date then use this code ..days: 1.. selected.isBefore(end.add(const Duration(days: 1)))
    if (selected.isAfter(start.subtract(const Duration(days: 1))) &&
        selected.isBefore(end.add(const Duration()))) {
      return true;
    } else {
      return false;
    }
  }

  _announenmentList() {
    return box != null
        ? ValueListenableBuilder(
      valueListenable: box!.listenable(),
      builder: (bc, Box box, child) {
        if (box.get("myassignment") == null) {
          return CardLoader();
        } else if (box.get("myassignment").isEmpty) {
          return Container(
            height: height(context) * 0.75,
            child: Center(
              child: Text(
                'no_active_assignments',
                style: Styles.bold(size: 16),
                textAlign: TextAlign.center,
              ).tr(),
            ),
          );
        }
        assignmentList = box
            .get("myassignment")
            .map((e) =>
            AssignmentList.fromJson(Map<String, dynamic>.from(e)))
            .cast<AssignmentList>()
            .toList();
        List<AssignmentList>? tempAssignments = [];

        for (var assignment in assignmentList!) {
          if (selectedCalanderView) {
            if (checkViewDate(assignment.endDate, assignment.startDate) == true) {
              if (selectedOption == 'All' ||
                  selectedOption == assignment.status)
                tempAssignments.add(assignment);
            }
          } else if (selectedOption == 'All' ||
              selectedOption == assignment.status)
            tempAssignments.add(assignment);
        }
        assignmentList = tempAssignments;

        return widget.fromDashboard == true
            ? Center(
          child: _rowItem(assignmentList![0], true),
        )
            : Padding(
          padding:
          EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              children: [
                if (widget.fromDashboard == false)
                  Row(
                    children: [
                      if(selectedCalanderView == false)
                        Text(tr('sort_by') + ': ',
                            style: Styles.regular(size: 14)),
                      if(selectedCalanderView == false)
                        DropdownButton<String>(
                          underline: SizedBox(),
                          hint: Text('${selectedOption.toLowerCase()}',
                              style: Styles.bold(size: 14))
                              .tr(),
                          items: <String>[
                            'All',
                            'Upcoming',
                            'Completed',
                            'Pending'
                          ].map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value.toLowerCase()).tr(),
                            );
                          }).toList(),
                          onChanged: (_) {
                            setState(() {
                              selectedOption = _!;
                            });
                          },
                        ),

                      Expanded(child: SizedBox()),
                      InkWell(
                        onTap: () {
                          setState(() {
                            selectedCalanderView = false;
                            assignmentVisible = false;
                          });
                        },
                        child: !selectedCalanderView
                            ? SvgPicture.asset(
                          'assets/images/selected_listview.svg',
                          height: 16,
                          width: 16,
                          allowDrawingOutsideViewBox: true,
                        )
                            : SvgPicture.asset(
                          'assets/images/unselected_listview.svg',
                          height: 16,
                          width: 16,
                          allowDrawingOutsideViewBox: true,
                        ),
                      ),
                      SizedBox(width: 10),
                      InkWell(
                        onTap: () {
                          setState(() {
                            selectedCalanderView = true;
                          });
                        },
                        child: selectedCalanderView
                            ? SvgPicture.asset(
                          'assets/images/selected_calender.svg',
                          height: 20,
                          width: 20,
                          allowDrawingOutsideViewBox: true,
                        )
                            : SvgPicture.asset(
                          'assets/images/unselected_calender.svg',
                          height: 20,
                          width: 20,
                          allowDrawingOutsideViewBox: true,
                        ),
                      )
                    ],
                  ),
                if (selectedCalanderView)
                  Calendar(
                    sendValue: (DateTime date) {
                      setState(() {
                        selectedDate = date;
                        assignmentVisible = false;
                      });
                    },
                  ),
                if (!assignmentVisible && assignmentList?.length != 0)
                  Container(
                    height: height(context) * 0.8,
                    child: Center(
                      child: Text(
                        'no_active_assignments',
                        style: Styles.bold(size: 16),
                        textAlign: TextAlign.center,
                      ).tr(),
                    ),
                  ),
                // ===> Fixed scroll part here
                assignmentList?.length != 0
                    ? ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: assignmentList == null
                        ? 0
                        : assignmentList!.length,
                    itemBuilder: (context, index) {
                      return _rowItem(
                          assignmentList![index], true);
                    })
                    : Container(
                  height: height(context) * 0.75,
                  child: Center(
                    child: Text(
                      'no_active_assignments',
                      style: Styles.bold(size: 16),
                      textAlign: TextAlign.center,
                    ).tr(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    )
        : CardLoader();
  }


  _rowItem(AssignmentList item, bool show) {
    if (!assignmentVisible && show)
      SchedulerBinding.instance.addPostFrameCallback((_) {
        setState(() {
          assignmentVisible = true;
        });
      });

    return Visibility(
      visible: show,
      child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              NextPageRoute(
                  ChangeNotifierProvider<MgAssignmentDetailProvider>(
                      create: (c) => MgAssignmentDetailProvider(
                          TrainingService(ApiService()), item),
                      child: MgAssignmentDetailPage(
                        id: item.contentId,
                        status: item.status?.toLowerCase(),
                      )),
                  isMaintainState: true),
            );
          },
          child: Container(
              padding: widget.fromDashboard == true
                  ? EdgeInsets.only(left: 10, right: 10, top: 17, bottom: 17)
                  : EdgeInsets.all(10),
              width: MediaQuery.of(context).size.width * 0.9,
              margin: EdgeInsets.symmetric(vertical: 10, horizontal: 6),
              decoration: BoxDecoration(
                color: ColorConstants.WHITE,
                borderRadius: BorderRadius.circular(10),
              ),
              child:
                  Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
                if (Utility.isExpired(item.endDate!, currentIndiaTime!) &&
                    item.status != 'Completed') ...[
                  SvgPicture.asset(
                    'assets/images/missed_icon.svg',
                    width: 20,
                    height: 20,
                    allowDrawingOutsideViewBox: true,
                  ),
                ] else if (item.status == 'Completed') ...[
                  SvgPicture.asset(
                    'assets/images/completed_icon.svg',
                    width: 20,
                    height: 20,
                    allowDrawingOutsideViewBox: true,
                  ),
                ] else if (item.status == 'Upcoming') ...[
                  SvgPicture.asset(
                    'assets/images/upcoming_live.svg',
                    width: 20,
                    height: 20,
                    allowDrawingOutsideViewBox: true,
                  ),
                ] else if (item.status == 'Pending') ...[
                  SvgPicture.asset(
                    'assets/images/pending_icon.svg',
                    width: 20,
                    height: 20,
                    allowDrawingOutsideViewBox: true,
                  ),
                ],
                SizedBox(width: 20),
                Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.7,
                        child: Text('${item.title}',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            softWrap: false,
                            style: Styles.bold(size: 16)),
                      ),
                      SizedBox(height: 5),
                      if (item.status == 'Completed') ...[
                        Text('submitted'.tr(), style: Styles.regular(size: 12)),
                        SizedBox(height: 5),
                      ] else if (item.status == 'Upcoming') ...[
                        Row(
                          children: [
                            Text('${tr('submission_date')} :',
                                style: Styles.regular(
                                  size: 12,
                                )),

                            item.submissionDate != null ? Text(
                              '${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item.submissionDate! * 1000))}',
                              style: Styles.regular(
                                size: 12,
                              ),
                              textDirection: ui.TextDirection.ltr,
                            ):SizedBox(),

                          ],
                        ),
                        SizedBox(height: 3),
                        Text(
                            '${tr('deadline')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(
                              DateTime.fromMillisecondsSinceEpoch(
                                  item.endDate! * 1000),
                            )}',
                            style: Styles.regular(size: 12)),
                        SizedBox(height: 5),
                      ] else if (item.status == 'Pending') ...[
                        Text('${tr('${item.status}'.toLowerCase())}',
                            style: Styles.regular(
                                size: 12,
                                color: ColorConstants().primaryColor() ??
                                    ColorConstants().primaryColorAlways())),
                        SizedBox(height: 5),

                        Row(
                          children: [
                            Text('${tr('submission_date')} :',
                                style: Styles.regular(
                                  size: 12,
                                )),

                            item.submissionDate != null ? Text(
                              '${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item.submissionDate! * 1000))}',
                              style: Styles.regular(
                                size: 12,
                              ),
                              textDirection: ui.TextDirection.ltr,
                            ):SizedBox(),

                          ],
                        ),
                        SizedBox(height: 3),
                        Row(
                          children: [
                            Text('${tr('deadline')} :',
                                style: Styles.regular(
                                  size: 12,
                                )),

                            Text(
                              '${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item.endDate! * 1000))}',
                              style: Styles.regular(
                                size: 12,
                              ),
                              textDirection: ui.TextDirection.ltr,
                            ),

                            /*Text(
                              '${Utility.getTime(timestamp: item.submissionDate!  * 1000, dateFormat: 'MM/dd/yyyy, hh:mm aaa', )}',
                              style: Styles.regular(
                                size: 12,
                              ),
                              textDirection: ui.TextDirection.ltr,
                            )*/
                          ],
                        ),

                        SizedBox(height: 5),
                      ],
                      if (item.isGraded == 1 &&
                          item.score != null &&
                          item.score != 0)
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                  text: '${tr('score_earned')}: ',
                                  style: Styles.regular(size: 12)),
                              TextSpan(
                                text: '${item.score.toStringAsFixed(2)}',
                                style: Styles.bold(size: 12),
                              ),
                              TextSpan(
                                  text: '/${item.maximumMarks}',
                                  style: Styles.regular(size: 12)),
                            ],
                          ),
                        ),
                    ]),
              ]))),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
