import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/pages/gcarvaan/createpost/create_post_provider.dart';
import 'package:video_trimmer/video_trimmer.dart';

class TrimmerView extends StatefulWidget {
  final File file;
  final CreatePostProvider provider;

  const TrimmerView(this.file, {super.key, required this.provider});

  @override
  State<TrimmerView> createState() => _TrimmerViewState();
}

class _TrimmerViewState extends State<TrimmerView> {
  final Trimmer _trimmer = Trimmer();

  double _startValue = 0.0;
  double _endValue = 0.0;

  bool _isPlaying = false;
  bool _progressVisibility = false;

  @override
  void initState() {
    super.initState();
    widget.provider.clearList();

    _loadVideo();
  }

  void _loadVideo() async {
    _trimmer.loadVideo(videoFile: widget.file);
  }

  Future<void> _saveVideo() async {
    setState(() {
      _progressVisibility = true;
    });

    await _trimmer.saveTrimmedVideo(
      startValue: _startValue,
      endValue: _endValue,
      onSave: (outputPath) {
        setState(() {
          _progressVisibility = false;
        });
        if (outputPath != null) {
          widget.provider.addToList(outputPath);
          Navigator.of(context).pop();
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (Navigator.of(context).userGestureInProgress) {
          return false;
        } else {
          return true;
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          title: const Text("Video Trimmer"),
        ),
        body: Builder(
          builder: (context) => Center(
            child: Container(
              padding: const EdgeInsets.only(bottom: 10.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: <Widget>[
                  Visibility(
                    visible: _progressVisibility,
                    child: LinearProgressIndicator(
                      //backgroundColor: context.appColors.error,
                    ),
                  ),

                  Expanded(
                    child: VideoViewer(
                      trimmer: _trimmer,
                    ),
                  ),
                  Center(
                    child: TrimViewer(
                      trimmer: _trimmer,
                      viewerHeight: 50.0,
                      viewerWidth: MediaQuery.of(context).size.width,
                      maxVideoLength: const Duration(seconds: 15),
                      onChangeStart: (value) {
                        _startValue = value;
                      },
                      onChangeEnd: (value) {
                        _endValue = value;
                      },
                      onChangePlaybackState: (value) {
                        setState(() {
                          _isPlaying = value;
                        });
                      },
                    ),
                  ),

                  //TODO: Implement button sheet
                  const SizedBox(
                    height: 20.0,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop(false);
                          },
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent),
                          child: const Text('cancel' , style: TextStyle(color: Colors.white),).tr(),
                        ),
                      ),
                      Expanded(
                          child: TextButton(
                            child: _isPlaying
                                ? Icon(
                              Icons.pause,
                              size: 30.0,
                              //color: context.appColors.primaryForeground,
                            )
                                : Icon(
                              Icons.play_arrow,
                              size: 30.0,
                              //color: context.appColors.primaryForeground,
                              color: Colors.white,
                            ),
                            onPressed: () async {
                              final playbackState =
                                  _trimmer.videoPlayerController!.value.isPlaying;
                              await _trimmer.videoPlayerController?.pause();
                              setState(() {
                                _isPlaying = playbackState;
                              });
                            },
                          )),
                      Expanded(
                        child: ElevatedButton(
                          onPressed:
                          _progressVisibility ? null : () => _saveVideo(),
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent),
                          child: const Text('add', style: TextStyle(color: Colors.white)).tr(),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
