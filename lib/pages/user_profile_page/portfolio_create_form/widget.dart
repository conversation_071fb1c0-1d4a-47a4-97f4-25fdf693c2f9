import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:easy_localization/easy_localization.dart';

class CustomTextField extends StatelessWidget {
  final String? hintText;
  final String? style;
  final TextEditingController? controller;
  final int maxLine;
  final bool? validate;
  final String? validationString;
  final int? maxChar;
  final TextInputType? keyboardType;
  const CustomTextField(
      {Key? key,
      required this.hintText,
      this.style,
      this.controller,
      this.maxChar,
      this.maxLine = 1,
      this.validate = false,
      this.keyboardType,
      this.validationString = ""})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
        inputFormatters: keyboardType != null
            ? <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9a-zA-Z]")),
              ]
            : null,
        maxLength: maxChar,
        keyboardType: keyboardType,
        controller: controller,
        validator: (value) {
          if (validate == false) return null;
          if (value == null || value.isEmpty) {
            return validationString;
          }
          return null;
        },
        maxLines: maxLine,
        decoration: InputDecoration(
          hintStyle: Styles.regular(size: 14, color: Color(0xff929BA3)),
          border: OutlineInputBorder(
              borderSide: const BorderSide(width: 1, color: Color(0xffE5E5E5)),
              borderRadius: BorderRadius.circular(10)),
          hintText: hintText,
        ));
  }
}

class GradientText extends StatelessWidget {
  final Widget child;
  const GradientText({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (Rect bounds) {
        return LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: <Color>[
              ColorConstants().gradientLeft(),
              ColorConstants().gradientRight()
            ]).createShader(bounds);
      },
      child: child,
    );
  }
}

class CustomUpload extends StatelessWidget {
  final String uploadText;
  final Function onClick;
  const CustomUpload(
      {Key? key, required this.uploadText, required this.onClick})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onClick();
      },
      child: Container(
        width: width(context),
        height: height(context) * 0.07,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(width: 1.0, color: Color(0xffFFFFFF)),
          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
        ),
        child: Padding(
          padding: const EdgeInsets.only(left: .0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ShaderMask(
                  blendMode: BlendMode.srcIn,
                  shaderCallback: (Rect bounds) {
                    return LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: <Color>[
                          ColorConstants().gradientLeft(),
                          ColorConstants().gradientRight()
                        ]).createShader(bounds);
                  },
                  child: Row(
                    children: [
                      SvgPicture.asset('assets/images/upload_icon.svg'),
                      Text(
                        uploadText,
                        style: Styles.bold(size: 12),
                      ),
                    ],
                  )),
            ],
          ),
        ),
      ),
    );
  }
}

class PortfolioCustomButton extends StatefulWidget {
  final Function clickAction;

  const PortfolioCustomButton({
    Key? key,
    required this.clickAction,
  }) : super(key: key);

  @override
  _PortfolioCustomButtonState createState() => _PortfolioCustomButtonState();
}

class _PortfolioCustomButtonState extends State<PortfolioCustomButton> {
  bool isButtonDisabled = false;

  void handleButtonClick() {
    if (!isButtonDisabled) {
      // Disable the button to prevent multiple clicks
      setState(() {
        isButtonDisabled = true;
      });

      // Execute the click action
      widget.clickAction();

      // Enable the button after a delay (if needed)
      Future.delayed(Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            isButtonDisabled = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: handleButtonClick,
      child: Container(
        height: height(context) * 0.06,
        width: width(context),
        // padding: const EdgeInsets.all(10.0),
        margin: const EdgeInsets.symmetric(vertical: 20),
        decoration: const BoxDecoration(
          color: Color(0xff0E1638),
          borderRadius: BorderRadius.all(Radius.circular(26)),
        ),
        child: Center(
          child: Text(
            "save",
            style: TextStyle(
              fontSize: 14.0,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ).tr(),
        ),
      ),
    );
  }
}

class CustomRating extends StatelessWidget {
  final String title;
  final int percentage;
  final Function onClick;
  final double? progressWidth;

  const CustomRating({
    Key? key,
    required this.title,
    required this.percentage,
    required this.onClick,
    required this.progressWidth,
  }) : super(key: key);

  Color getRankColor(int rank) {
    Color color = ColorConstants.NOVOICE;
    switch (rank) {
      case 1:
        color = ColorConstants.NOVOICE;
        break;
      case 2:
        color = ColorConstants.LEARNER;
        break;
      case 3:
        color = ColorConstants.MASTER;
        break;
      case 4:
        color = ColorConstants.EXPERT;
        break;
      case 5:
        color = ColorConstants.LEADER;
        break;
    }
    return color;
  }

  String getRankName(int rank) {
    String name = tr('novice');
    switch (rank) {
      case 1:
        name = tr('novice');
        break;
      case 2:
        name = tr('learner');
        break;
      case 3:
        name = tr('master');
        break;
      case 4:
        name = tr('expert');
        break;
      case 5:
        name = tr('leader');
        break;
    }
    return name;
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: () {
          onClick();
        },
        child: Container(
            width: width(context),
            height: height(context) * 0.08,
            padding: const EdgeInsets.only(bottom: 5),
            decoration: BoxDecoration(
              color: Color(0xffE9ECF3),
              borderRadius: const BorderRadius.all(Radius.circular(5.0)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width:width(context)*0.55,
                          child: Text(title,
                              softWrap: true,
                              overflow: TextOverflow.ellipsis,
                              style: Styles.semibold(size: 14)),
                        ),
                        Spacer(),
                        // Text('$percentage%', style: Styles.semibold(size: 10)),
                        // SizedBox(width: 5),
                        Text('${getRankName(percentage ~/ 25 + 1)}',
                            style: Styles.semibold(
                                size: 12,
                                color: getRankColor(percentage ~/ 25 + 1))),
                      ],
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    Container(
                      height: 6,
                      width: progressWidth,
                      decoration: BoxDecoration(
                          color: Color(0xffFFFFFF),
                          borderRadius: BorderRadius.circular(10)),
                      child: Stack(
                        children: [
                          Container(
                            height: 10,
                            width: progressWidth! * (percentage / 100),
                            decoration: BoxDecoration(
                                color: getRankColor(percentage ~/ 25 + 1),
                                borderRadius: BorderRadius.circular(10)),
                          ),
                        ],
                      ),
                    ),
                  ]),
            )));
  }
}
