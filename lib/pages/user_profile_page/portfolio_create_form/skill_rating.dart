import 'package:flutter/material.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';

class OpenToWork extends StatelessWidget {
  const OpenToWork({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:AppBar(title:Text('skill & rating')),
      body:Container(
        width: width(context),
        height: height(context) * 0.02,
        decoration: BoxDecoration(
          color: Color(0xffE9ECF3),
          border:
              Border.all(width: 0, color: Color.fromARGB(255, 142, 142, 142)),
          borderRadius: const BorderRadius.all(Radius.circular(5.0)),
        ),
        child: Column(children: [
          ListView.builder(
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemCount: 1,
              itemBuilder: (context, index) {
                return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Card(
                        color: ColorConstants.BG_LIGHT_GREY,
                        elevation: 0.0,
                        child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: ListTile(
                              title: Padding(
                                padding: const EdgeInsets.only(left: 3.0),
                                child: Text(
                                  "master",
                                  style: Styles.textBold(
                                      color: ColorConstants.BLACK),
                                ),
                              ),
                              trailing: Column(
                                children: [
                                  Row(children: [
                                    Text('master',
                        style: TextStyle(
                            fontSize: 10, fontWeight: FontWeight.bold)),
                    SizedBox(width: 5),
                    Text('percent',
                        style: TextStyle(
                            fontSize: 10, fontWeight: FontWeight.bold)),
                                  ]),
                                ],
                              ),
                            ))));
              })
        ]))


      );
    
  }
}