import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:geocoding/geocoding.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import '../../utils/LocationManager.dart';
import '../../utils/config.dart';
import '../../utils/utility.dart';
import '../custom_pages/alert_widgets/alerts_widget.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final nameController = TextEditingController(
      text: Utility()
          .decrypted128('${Preference.getString(Preference.FIRST_NAME)}'));
  final headController = TextEditingController(
      text: Preference.getString(Preference.USER_HEADLINE));
  final countryController = TextEditingController(
      text: Preference.getString(Preference.LOCATION)?.split(',')[1]);
  final cityController = TextEditingController(
      text: Preference.getString(Preference.LOCATION)?.split(',').first);
  final aboutController =
      TextEditingController(text: Preference.getString(Preference.ABOUT_ME));
  bool addingProfile = false;
  final _formKey = GlobalKey<FormState>();

  late double lat = 0.0;
  late double long = 0.0;

  @override
  void initState() {
    LocationManager.initLocationService().then((value) {
      setState(() {
        lat = value!.latitude!;
        long = value.longitude!;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorConstants.WHITE,
        body: ScreenWithLoader(
          isLoading: addingProfile,
          body: BlocListener<HomeBloc, HomeState>(
              listener: (context, state) async {
                if (state is AddProfolioProfileState) {
                  handleAddProfileResponse(state);
                }
              },
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SingleChildScrollView(
                      child: Form(
                    key: _formKey,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Spacer(),
                              Text(
                                "edit_profile",
                                style: Styles.bold(
                                    size: 14, color: Color(0xff0E1638)),
                              ).tr(),
                              Spacer(),
                              InkWell(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: Icon(
                                  Icons.close,
                                  color: Colors.black,
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: 8),
                          Padding(
                            padding: const EdgeInsets.only(left: 4.0),
                            child: Text(
                              "${tr('full_name')}*",
                              style: Styles.regular(
                                  size: 14, color: Color(0xff0E1638)),
                            ),
                          ),
                          SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                              validate: true,
                              validationString: tr('plz_enter_full_name'),
                              controller: nameController,
                              maxChar: 50,
                              hintText: tr('full_name')),
                          const SizedBox(
                            height: 5,
                          ),
                          // const SizedBox(
                          //   height: 10,
                          // ),
                          Padding(
                            padding: const EdgeInsets.only(left: 4.0),
                            child: Text(
                              "${tr('headline')}*",
                              style: Styles.regular(
                                  size: 14, color: Color(0xff0E1638)),
                            ),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                              validate: true,
                              validationString: tr('please_enter_headline'),
                              maxChar: 30,
                              controller: headController,
                              hintText: tr('write_your_headlline')),
                          SizedBox(height: 15),

                          InkWell(
                            onTap: () async {
                              try {
                                List<Placemark> placemarks =
                                    await placemarkFromCoordinates(lat, long);
                                AlertsWidget.showCustomDialog(
                                    context: context,
                                    title: tr('current_Location'),
                                    text: tr('use_your_current_location'),
                                    icon: 'assets/images/circle_alert_fill.svg',
                                    onOkClick: () async {
                                      countryController.text =
                                          placemarks.first.country.toString();
                                      cityController.text =
                                          placemarks.first.locality.toString();
                                    });
                              } catch (e) {
                                Log.v(':: ${e.toString()}');
                              }
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 4.0),
                                  child: Text(
                                    'location',
                                    style: Styles.bold(size: 14),
                                  ).tr(),
                                ),
                                Spacer(),
                                ShaderMask(
                                  blendMode: BlendMode.srcIn,
                                  shaderCallback: (Rect bounds) {
                                    return LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: <Color>[
                                          ColorConstants().gradientLeft(),
                                          ColorConstants().gradientRight()
                                        ]).createShader(bounds);
                                  },
                                  child: SvgPicture.asset(
                                    'assets/images/location.svg',
                                  ),
                                ),
                                SizedBox(
                                  width: 10,
                                ),
                                GradientText(
                                    child: Text(
                                  "use_current_location",
                                  style: TextStyle(fontSize: 12),
                                ).tr())
                              ],
                            ),
                          ),
                          SizedBox(height: 15),
                          Padding(
                            padding: const EdgeInsets.only(left: 4.0),
                            child: Text(
                              "${tr('country')}*",
                              style: Styles.regular(
                                  size: 14, color: Color(0xff0E1638)),
                            ),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                              validate: true,
                              validationString: tr('please_enter_country'),
                              controller: countryController,
                              hintText: tr('enter_country')),
                          const SizedBox(
                            height: 15,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 4.0),
                            child: Text(
                              "${tr('city')}*",
                              style: Styles.regular(
                                  size: 14, color: Color(0xff0E1638)),
                            ),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                              validate: true,
                              validationString: tr('Enter_city'),
                              controller: cityController,
                              hintText: tr('enter_city')),
                          SizedBox(
                            height: 15,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 4.0),
                            child: Text(
                              "${tr('about_me')}*",
                              style: Styles.regular(
                                  size: 14, color: Color(0xff0E1638)),
                            ),
                          ),
                          SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                              validate: true,
                              validationString: tr('plz_enter_about_yourself'),
                              controller: aboutController,
                              maxLine: 10,
                              maxChar: 220,
                              hintText: tr('tell_something_about_yourself')),
                          PortfolioCustomButton(clickAction: () {
                            if (_formKey.currentState!.validate()) {
                              Map<String, dynamic> data = Map();
                              data['name'] = nameController.value.text;
                              data['headline'] = headController.value.text;
                              data['country'] = countryController.value.text;
                              data['city'] = cityController.value.text;
                              data['about_me'] = aboutController.value.text;
                              addProfile(data);
                            }
                          })
                        ]),
                  )),
                ),
              )),
        ));
  }

  void addProfile(Map<String, dynamic> data) {
    BlocProvider.of<HomeBloc>(context)
        .add(AddPortfolioProfileEvent(data: data));
  }

  void handleAddProfileResponse(AddProfolioProfileState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          addingProfile = true;

          break;
        case ApiStatus.SUCCESS:
          Log.v("Add Profile State....................");
          addingProfile = false;
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('profile_updated_successfully').tr(),
          ));

          Navigator.pop(context);

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CompetitionListIDState ..........................${state.error}");
          addingProfile = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'singularis_profile_page', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
