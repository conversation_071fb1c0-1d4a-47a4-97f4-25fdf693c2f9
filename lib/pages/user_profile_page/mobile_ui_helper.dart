import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';

List<PlatformUiSettings>? buildUiSettings(BuildContext context) {
  return [
    AndroidUiSettings(
        toolbarTitle: '',
         toolbarColor: Colors.black,
        toolbarWidgetColor: Colors.white,
        hideBottomControls: true,
        initAspectRatio: CropAspectRatioPreset.original,
        lockAspectRatio: true),
    IOSUiSettings(
      title: '',
    ),
  ];
}
