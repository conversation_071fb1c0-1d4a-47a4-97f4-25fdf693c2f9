enum QuestionType {
  image,
  video,
  audio,
}

enum QuestionStatus { pending, done, skipped }

class Question {
  String userAnswerTxt;
  QuestionStatus questionStatus;
  List<String> userUploadedFileUrl;
  dynamic reviewData;

  Question(
      {this.userAnswerTxt = "",
      required this.questionStatus,
      this.userUploadedFileUrl = const [],
      this.reviewData});

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      userAnswerTxt: json['userAnswerTxt'] ?? "",
      questionStatus: json['question_status'],
      userUploadedFileUrl: List<String>.from(json['userUploadedFileUrl'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userAnswerTxt': userAnswerTxt,
      'userUploadedFileUrl': userUploadedFileUrl,
      'question_status': questionStatus
    };
  }
}
