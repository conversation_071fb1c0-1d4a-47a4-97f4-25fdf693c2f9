import 'dart:developer';

import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

import 'interview_provider.dart';

class VideoPlay extends StatefulWidget {
  final String videoUrl;

  VideoPlay({Key? key, required this.videoUrl}) : super(key: key);

  @override
  _VideoPlayState createState() => _VideoPlayState();
}

class _VideoPlayState extends State<VideoPlay> {
  FlickManager? _flickManager;

  @override
  void initState() {
    super.initState();
    log("init called ", name: 'video play.dart');
    initilizedVideo();
  }

  void initilizedVideo() {
    setState(() {
      _flickManager = FlickManager(
          videoPlayerController:
              VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl)),
          autoPlay: true,
          autoInitialize: true);
    });
  }

  @override
  void dispose() {
    log("dispose called ", name: 'video play.dart');
    _flickManager?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // super.build(context);

    return Consumer<InterviewAssessmentProvider>(
      builder: (context, value, child) {
        if (value.pauseQuestionVideo) {
          _flickManager?.flickControlManager?.pause();
        } else if (_flickManager?.flickVideoManager?.isPlaying == false) {
          // _flickManager?.flickControlManager?.play();
        }

        return _flickManager == null
            ? SizedBox()
            : ClipRRect(
                borderRadius: BorderRadius.circular(0),
                child: Directionality(
                    textDirection: Utility.setDirection(false),
                    child: FlickVideoPlayer(flickManager: _flickManager!)));
      },
    );
  }

  // @override
  // bool get wantKeepAlive => true;
}
