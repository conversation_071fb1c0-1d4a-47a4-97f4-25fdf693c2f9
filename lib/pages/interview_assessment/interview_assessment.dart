import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/interview_assessment/floating_button.dart';
import 'package:masterg/pages/interview_assessment/question_attempt_review.dart';
import 'package:masterg/pages/interview_assessment/question_view.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';

import '../../data/models/response/home_response/test_attempt_response.dart';
import '../../data/models/response/home_response/test_review_response.dart'
    as Review;
import '../custom_pages/alert_widgets/alerts_widget.dart';
import 'interview_provider.dart';
import 'inteview_assessment_progess.dart';

enum AssessmentProgessQuestionStatus { pending, done, skipped }

class InterviewAssessment extends StatefulWidget {
  final List<TestAttemptBean> questionList;
  final List<Review.TestReviewBean> reviewRespList;
  final int durationInMin;
  final Function onSubmit;
  final String title;
  final onFinalSubmit;
  final bool isReview;
  final Function(InterviewAssessmentProvider?) onProviderCreate;
  const InterviewAssessment(
      {Key? key,
      required this.questionList,
      required this.durationInMin,
      required this.onSubmit,
      required this.onFinalSubmit,
      required this.onProviderCreate,
      required this.title,
      required this.isReview,
      required this.reviewRespList})
      : super(key: key);

  @override
  _InterviewAssessmentState createState() => _InterviewAssessmentState();
}

class _InterviewAssessmentState extends State<InterviewAssessment> {
  bool providerUpdate = false;

  @override
  void dispose() {
    Utility.removeAllFilesFromCache();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<InterviewAssessmentProvider>(
          create: (context) {
            List<TestAttemptBean> questionList = widget.questionList;
            if (widget.isReview == true) {
              questionList
                  .addAll(widget.reviewRespList.map((Review.TestReviewBean e) {
                TestAttemptBean data = TestAttemptBean(
                    question: Questions.fromJson(
                        (e.question as Review.Questions).toJson()),
                    id: e.id,
                    title: e.title);
                data.question?.userFile = e.question?.userFile;
                data.question?.answerStatement = e.question?.answerStatement;

                return data;
              }));
            }
            return InterviewAssessmentProvider(
                questionList,
                widget.durationInMin ~/ 60,
                widget.onSubmit,
                widget.onFinalSubmit,
                widget.title,
                widget.isReview);
          },
        ),
      ],
      child: Consumer<InterviewAssessmentProvider>(
        builder: ((context, value, child) {
          if (!providerUpdate) {
            widget.onProviderCreate(value);
            providerUpdate = true;
          }
          return WillPopScope(
            onWillPop: () async {
              bool isRTL = Utility().isRTL(context);

              value.pauseVideo();
              if (value.isReview) {
                await AlertsWidget.showCustomDialog(
                    isRTL: isRTL,
                    context: context,
                    text: "${tr('areYouSureYouWantToExit')}",
                    title: "${tr('review_assessment')}",
                    icon: 'assets/images/circle_alert_fill.svg',
                    onCancelClick: () {},
                    returnContext: (c) {},
                    onOkClick: () async {
                      Navigator.pop(context);
                    });

                return Future.value(false);
              }

              await AlertsWidget.showCustomDialog(
                  isRTL: isRTL,
                  context: context,
                  text: "${tr('app_assessment_submit_three')}",
                  title: "${tr('finish_test')}",
                  icon: 'assets/images/circle_alert_fill.svg',
                  onCancelClick: () {
                    value.playVideo();
                  },
                  returnContext: (c) {
                    value.addContext(c);
                  },
                  onOkClick: () async {
                    await showDialog(
                        context: context,
                        useSafeArea: false,
                        builder: (context) {
                          // value.addContext(context);
                          return QuestionAttemptReview(
                            isRTL: isRTL,
                            title: widget.title,
                            interviewAssessmentProvider: value,
                            disableback: false,
                          );
                        }).then((value) {
                      // value.removeLastContext();
                      if (value == true) {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text('assesssment_submitted').tr(),
                        ));
                      }
                    });
                    value.playVideo();
                  }).then((value) => value.removeLastContext());

              value.playVideo();

              return Future.value(false);
            },
            child: Scaffold(
              backgroundColor: Color(0xffF9FBFC),
              appBar: AppBar(
                  backgroundColor: ColorConstants.WHITE,
                  elevation: 0.4,
                  iconTheme: IconThemeData(color: ColorConstants.BLACK),
                  title: Text(
                    'video_interview',
                    style: Styles.bold(),
                  ).tr()),
              body: ScreenWithLoader(
                isLoading: false,
                body: Column(
                  children: [
                    IgnorePointer(
                      ignoring: value.isCompressingVideo == false &&
                          value.pickingFile == true,
                      child: AssessmentProgress(
                        provider: value,
                      ),
                    ),
                    SizedBox(
                      height:
                          (height(context) * 0.77) - value.floatingButtonHeight,
                      child: PageView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          scrollDirection: Axis.horizontal,
                          controller: value.pageController,
                          itemCount: value.questionList.length,
                          itemBuilder: (context, index) {
                            return QuestionView(
                                key: Key('$index'),
                                currentQuestion: value.questionList[index],
                                index: index);
                          }),
                    ),
                  ],
                ),
              ),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerDocked,
              floatingActionButton: IgnorePointer(
                  ignoring: value.isCompressingVideo == false &&
                      value.pickingFile == true,
                  child: FloatingButton()),
            ),
          );
        }),
      ),
    );
  }
}
