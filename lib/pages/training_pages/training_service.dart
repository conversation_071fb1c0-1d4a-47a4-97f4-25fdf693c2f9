import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:masterg/data/api/api_constants.dart';
import 'package:masterg/data/api/api_response.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Log.dart';

import '../../utils/Log.dart';

class TrainingService {
  TrainingService(this.api);

  ApiService api;

  Map<String, dynamic> get defaultParams => {
        "key": api.env.apiKey,
      };

  Future<dynamic> getTrainings() async {
    try {
      final response = await api.dio.get(ApiConstants.PROGRAMS_LIST,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        Log.v('api call response -- $response');

        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          Log.v('api call response -- ${response}');
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }

  Future<dynamic> getMTraningData(int id) async {
    try {
      final response =
          await api.dio.get(ApiConstants.COURSE_LIST + '?category_id=$id',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }

  Future<dynamic> getTrainingDetail(int? moduleId) async {
    try {
      final response =
          await api.dio.get(ApiConstants.PROGRAMS_LIST + '/$moduleId',
              options: Options(
                  method: 'POST',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        Log.v('api call response -- ${response}');

        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          Log.v('api call response -- ${response}');
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }

  Future<dynamic> getDetailContent(int? moduleId) async {
    Log.v("make api call");

    try {
      final response = await api.dio.get(
          ApiConstants.MODULES +
              '/$moduleId' +
              "/${UserSession.userContentLanguageId}",
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        Log.v('api call response -- ${response}');

        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          Log.v('api call response sucess -- ${response}');
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }

  Future<dynamic> getTrainingDetailedContent(int? programContentId) async {
    //  Utility.hideKeyboard();
    try {
      final response = await api.dio
          .get(ApiConstants.CONTENT_DETAILS + '/${programContentId}',
              options: Options(
                  method: 'POST',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        Log.v('api call response -- ${response}');

        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          Log.v('api call response -- $response');
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }

  Future<dynamic> getAnnouncemnets(Map<String, dynamic> data) async {
    try {
      final response = await api.dio.get(ApiConstants.GET_CONTENT_API,
          queryParameters: data,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        Log.v('api call response -- ${response}');

        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          Log.v('api call response -- ${response}');
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }

  Future<dynamic> getAssignmentDetails(int? id) async {
    try {
      final response =
          await api.dio.get(ApiConstants.ASSIGNMENT_DETAILS + id.toString(),
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        Log.v('api call response -- ${response}');

        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          Log.v('api call response -- ${response}');
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }

  Future<ApiResponse?> submitAssignment(
      {int? id, String? notes, String? path}) async {
    try {
      var formData = FormData.fromMap({
        "content_id": id,
        "user_notes": notes,
        "file":
            await MultipartFile.fromFile(path!, filename: path.split("/").last)
      });
      final response = await api.dio.post(ApiConstants.SUBMIT_ASSIGNMENT,
          data: formData,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "multipart/form-data"));
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, s) {
      Log.v(s);
    }
    return null;
  }

  getAssessmentInstructions(int? programContentId) async {
    try {
      final response = await api.dio.get(
          ApiConstants.ASSESSMENT_INSTRUCTIONS + programContentId.toString(),
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        Log.v('api call response -- ${response}');

        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          Log.v('api call response -- ${response}');
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }


  Future<ApiResponse?> bottombarResponse() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;

      //handle is skill enable or not

      final skillEnableResponse =
          await api.dio.get('${ApiConstants.settings}?type=5',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  // contentType: "application/json",
                  responseType: ResponseType.json));

      if (skillEnableResponse.statusCode == 200 ||
          skillEnableResponse.statusCode == 201) {
        Preference.setBool(
            Preference.ENABLE_SKILL,
            (int.tryParse(
                        '${ApiResponse.success(skillEnableResponse).body['data']['enable_skill']}') ??
                    0) ==
                1);
      }

      final response = await api.dio.get('${ApiConstants.settings}?type=7',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              // contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
      Preference.setString('region', currentTimeZone);
      // print('date time now is $currentTimeZone is set');
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        log('Menu--${response.data}');
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

}
