import 'dart:async';
import 'dart:isolate';
import 'dart:ui';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/test_review_response.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

class TestReviewPage extends StatefulWidget {
  final int? contentId;

  TestReviewPage({this.contentId});

  @override
  _TestReviewPageState createState() => _TestReviewPageState();
  var _pageViewController = PageController();
  int? _currentSection = 0;
  var _currentQuestion = 0;
  int? _currentQuestionId;
  //List<Sections> sections = List();
  List<TestReviewBean> _list = [];
  // Map<int, dynamic> _sections = Map();
  int _currentQuestionNumber = 1;
  ScrollController? _questionController;
}

class IdMapper {
  int? questionId;
  String? color;
  int? timeTaken;

  IdMapper({this.questionId, this.color, this.timeTaken});

  IdMapper.fromJson(Map<String, dynamic> json) {
    questionId = json['questionId'];
    color = json['color'];
    timeTaken = json['timeTaken'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['questionId'] = this.questionId;
    data['color'] = this.color;
    data['timeTaken'] = this.timeTaken;
    return data;
  }
}

class _TestReviewPageState extends State<TestReviewPage>
    with WidgetsBindingObserver {
  //final GlobalKey<ScaffoldState> _key = new GlobalKey<ScaffoldState>();
  final _key = GlobalKey<ScaffoldMessengerState>();
  var _isLoading = false;
  var _scaffoldContext;
  late HomeBloc _authBloc;
  bool _showSolution = false;

  void _handleAttemptTestResponse(ReviewTestState state) {
    try {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          this.setState(() {
            _isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          if (state.response!.data != null) {
            widget._list.clear();
            for (int i = 0;
                i < state.response!.data!.assessmentReview!.questions!.length;
                i++) {
              widget._list.add(
                TestReviewBean(
                    question:
                        state.response!.data!.assessmentReview!.questions![i],
                    id: state.response!.data!.assessmentReview!.questions![i]
                        .questionId,
                    title: state.response!.data!.assessmentReview!.questions![i]
                        .question),
              );
            }

            if (widget._list.length > 0) {
              widget._currentQuestionId =
                  widget._list.first.question!.questionId;
            }
          }
          this.setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.ERROR:
          this.setState(() {
            _isLoading = false;
          });
          FirebaseAnalytics.instance
              .logEvent(name: 'assessment_review', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    } catch (e) {}
  }

  @override
  void initState() {
    super.initState();
    _downloadListener();
    widget._questionController = ScrollController();
  }

  @override
  Widget build(BuildContext context) {
    Application(context);
    _authBloc = BlocProvider.of<HomeBloc>(context);
    return BlocManager(
      initState: (context) {
        if (widget._list.length == 0)
          _authBloc.add(
            ReviewTestEvent(
              request: widget.contentId.toString(),
            ),
          );
      },
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is ReviewTestState) _handleAttemptTestResponse(state);
        },
        child: Builder(builder: (_context) {
          _scaffoldContext = _context;
          return WillPopScope(
            onWillPop: () async => false,
            child: Scaffold(
              appBar: AppBar(
                leading: IconButton(
                  icon: Icon(Icons.arrow_back, color: Colors.black),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                title: Text(
                  'review',
                  style: TextStyle(color: Colors.black),
                ).tr(),
                backgroundColor: Colors.white,
                elevation: 0,
              ),
              backgroundColor: ColorConstants.WHITE,
              key: _key,
              bottomNavigationBar: widget._list.length == 0
                  ? SizedBox()
                  : BottomAppBar(
                      //color: Color.fromRGBO(238, 238, 243, 1),
                      elevation: 0,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            widget._currentQuestion == 0
                                ? SizedBox()
                                : TapWidget(
                                    onTap: () {
                                      widget._pageViewController.previousPage(
                                          duration: Duration(milliseconds: 200),
                                          curve: Curves.ease);
                                    },
                                    child: Container(
                                      width: 100,
                                      padding: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                      child: Text(
                                        "back",
                                        style: Styles.textBold(
                                            size: 16,
                                            color:
                                                Color.fromRGBO(53, 68, 116, 1)),
                                      ).tr(),
                                    ),
                                  ),
                            if ((widget._list.length - 1) !=
                                widget._currentQuestion)
                              TapWidget(
                                onTap: () {
                                  widget._pageViewController.nextPage(
                                      duration: Duration(milliseconds: 200),
                                      curve: Curves.ease);
                                },
                                child: Container(
                                  width: 100,
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                      color: ColorConstants().primaryColor(),
                                      borderRadius: BorderRadius.circular(8)),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "next",
                                        style: Styles.textBold(
                                            size: 16, color: Colors.white),
                                      ).tr(),
                                      const SizedBox(
                                        width: 5,
                                      ),
                                      Icon(Icons.arrow_forward,
                                          size: 15, color: Colors.white)
                                    ],
                                  ),
                                ),
                              )
                          ],
                        ),
                      ),
                    ),
              body: SafeArea(
                child: ScreenWithLoader(
                  body: widget._list.length == 0
                      ? Column(
                          children: [
                            //_heading(),
                            Center(
                              child:
                                  Text(_isLoading ? 'please_wait' : 'no_data')
                                      .tr(),
                            ),
                          ],
                        )
                      : _content(),
                  isLoading: _isLoading,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _content() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12)),
              ),
              child: Column(
                children: [
                  _questionCount(),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 19),
                    child: Divider(),
                  ),
                  _pageView(),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  _pageView() {
    return Expanded(
      child: Container(
        child: PageView.builder(
          itemBuilder: (context, index) {
            return _pageItem(widget._list[index]);
          },
          onPageChanged: (pageNumber) {
            setState(() {
              widget._currentSection = widget._list[pageNumber].id;
              widget._currentQuestionId =
                  widget._list[pageNumber].question!.questionId;
              widget._currentQuestion = pageNumber;
              _showSolution = false;
              int questionsLength = widget._list.length;
              Utility.waitForMili(200).then((value) {
                if (widget._currentQuestion + 2 >= questionsLength * .6) {
                  if (widget._questionController!.position.pixels !=
                      widget._questionController!.position.maxScrollExtent) {
                    widget._questionController!.jumpTo(
                        ((widget._currentQuestion + 2) * 30).toDouble());
                  }
                } else if ((widget._currentQuestion + 2) <=
                    questionsLength * .3) {
                  if (widget._questionController!.position.pixels != 0) {
                    widget._questionController!.jumpTo(0);
                  }
                }
              });
            });
          },
          controller: widget._pageViewController,
          itemCount: widget._list.length,
          physics: NeverScrollableScrollPhysics(),
        ),
      ),
    );
  }

  _questionCount() {
    return widget._list.length == 0
        ? SizedBox()
        : Padding(
            padding: const EdgeInsets.symmetric(horizontal: 19),
            child: Container(
              height: 60,
              width: MediaQuery.of(_scaffoldContext).size.width,
              child: SingleChildScrollView(
                physics: ClampingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                controller: widget._questionController,
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: ClampingScrollPhysics(),
                  itemBuilder: (context, index) {
                    Questions? question = widget._list[index].question;
                    return TapWidget(
                      onTap: () {
                        if (widget._currentQuestionId ==
                            widget._list[index].question!.questionId) {
                          return;
                        }
                        Log.v("HEREE");
                        for (int i = 0; i < widget._list.length; i++) {
                          if (widget._list[i].question!.questionId ==
                              widget._list[index].question!.questionId) {
                            Log.v(widget._list[widget._currentQuestion]
                                .question!.questionId
                                .toString());

                            widget._currentQuestionId =
                                widget._list[index].question!.questionId;
                            widget._currentQuestion = i;
                            widget._pageViewController.animateToPage(i,
                                duration: Duration(milliseconds: 100),
                                curve: Curves.ease);
                            break;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 32),
                        child: Container(
                          width: 35,
                          height: 35,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                  color: Color.fromRGBO(0, 0, 0, 0.05),
                                  offset: Offset(0, 8),
                                  blurRadius: 16)
                            ],
                            color: Colors.grey[300],
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            "${index + 1}",
                            style: index == widget._currentQuestion
                                ? Styles.textBold()
                                : Styles.textLight(),
                          ),
                        ),
                      ),
                    );
                  },
                  scrollDirection: Axis.horizontal,
                  itemCount: widget._list.length,
                ),
              ),
            ),
          );
  }

  _pageItem(TestReviewBean testAttemptBean) {
    return Container(
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _questionNumber(testAttemptBean),
            _size(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                testAttemptBean.question!.question ?? "",
                style: Styles.textRegular(size: 16),
              ),
            ),
            _size(height: 10),
            if (testAttemptBean.question!.questionImage != null)
              for (int i = 0;
                  i < testAttemptBean.question!.questionImage!.length;
                  i++)
                if (testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.mp4') ||
                    testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.mp3'))
                  Container(
                    height: 200,
                    width: double.infinity,
                    alignment: Alignment.center,
                    child: Center(
                      child: InAppWebView(
                          initialOptions: InAppWebViewGroupOptions(
                              crossPlatform: InAppWebViewOptions(
                                mediaPlaybackRequiresUserGesture: true,
                                useShouldOverrideUrlLoading: true,
                              ),
                              ios: IOSInAppWebViewOptions(
                                  allowsInlineMediaPlayback: true,
                                  allowsLinkPreview: false)),
                          initialUrlRequest: URLRequest(url: WebUri(testAttemptBean.question!.questionImage![i]))),
                    ),
                  )
                else
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.network(
                        testAttemptBean.question!.questionImage![i]),
                  ),
            _size(height: 10),
            _solutionType(testAttemptBean.question!.questionTypeId.toString(),
                testAttemptBean),
            _size(height: 10),
          ],
        ),
      ),
    );
  }

  _size({double height = 10}) {
    return SizedBox(
      height: height,
    );
  }

  _questionNumber(TestReviewBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Text(
            "Q.${(widget._currentQuestion + 1).toString().padLeft(2, "0")}",
            style: Styles.textBold(size: 22),
          ),
          Spacer(),
        ],
      ),
    );
  }

  String getTime({required double time}) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    return "${twoDigits((time / 60).truncate())}:${twoDigits((time % 60).truncate())}";
  }

  _solutionType(String type, TestReviewBean testAttemptBean) {
    switch (type) {
      case "1":
        return _multiChoose(testAttemptBean); //MULTIPLE_CHOICE

      case "2":
        return _options(testAttemptBean); //SINGLE_INTEGER

      case "3":
        return _multiChoose(testAttemptBean); //MULTIPLE_RESPONSE

      case "4":
        return _chooseOne(testAttemptBean); //FILL_IN_THE_BLANK

      case "5":
        return _chooseOne(testAttemptBean); //TRUE_FALSE

      case "6":
      //  return _subjective(testAttemptBean); //SUBJECTIVE

      case "7":
        return Container(); //MATCHING

    }
  }

  Future download2(String url, String savePath) async {
    try {
      _key.currentState!.showSnackBar(
        SnackBar(
          content: Text(
            "Downloading start.",
            style: Styles.boldWhite(),
          ),
          backgroundColor: ColorConstants.BLACK,
          duration: Duration(seconds: 2),
        ),
      );
      final taskId = await FlutterDownloader.enqueue(
        url: url,
        savedDir: savePath,
        showNotification: true,
        openFileFromNotification: true,
      );
      Log.v(taskId);
    } catch (e) {
      Log.v(e);
    }
  }

  @pragma('vm:entry-point')
  static void downloadCallback(
      String id, DownloadTaskStatus status, int progress) {
    final SendPort send =
        IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, status, progress]);
  }

  ReceivePort _port = ReceivePort();

  _downloadListener() {
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      String? id = data[0];
      DownloadTaskStatus? status = data[1];
      int? progress = data[2];
      if (status.toString() == "DownloadTaskStatus(3)" &&
          progress == 100 &&
          id != null) {
        String query = "SELECT * FROM task WHERE task_id='" + id + "'";
        var tasks = FlutterDownloader.loadTasksWithRawQuery(query: query);
        //if the task exists, open it
        FlutterDownloader.open(taskId: id);
      }
    });
    // FlutterDownloader.registerCallback(downloadCallback);
  }

  _options(TestReviewBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.questionOptions!
                  .length,
              (index) {
                Color borderColor;
                if (widget._list[widget._currentQuestion].question!
                        .questionOptions![index].optionId ==
                    int.parse(widget._list[widget._currentQuestion].question!
                        .correctOptions!.first)) {
                  borderColor = Color(0xff66bb6a);
                } else if (widget._list[widget._currentQuestion].question!
                            .questionOptions![index].optionId !=
                        int.parse(widget._list[widget._currentQuestion]
                            .question!.correctOptions!.first) &&
                    widget._list[widget._currentQuestion].question!
                            .questionOptions![index].userAnswer ==
                        1) {
                  borderColor = Colors.red;
                } else {
                  borderColor = Colors.grey;
                }
                return Column(
                  children: [
                    Container(
                      width: MediaQuery.of(_scaffoldContext).size.width,
                      height: 80,
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            child: Card(
                              elevation: 5,
                              margin: const EdgeInsets.only(right: 20),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                side:
                                    BorderSide(color: borderColor, width: 1.5),
                              ),
                              child: Container(
                                width:
                                    MediaQuery.of(_scaffoldContext).size.width -
                                        40,
                                height: 55,
                                alignment: Alignment.centerLeft,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 15),
                                child: Text(
                                  widget
                                      ._list[widget._currentQuestion]
                                      .question!
                                      .questionOptions![index]
                                      .optionStatement!,
                                  style: Styles.textRegular(size: 12),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 15,
                            left: 30,
                            child: widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .questionOptions![index]
                                        .userAnswer ==
                                    1
                                ? Container(
                                    color: Colors.grey[300],
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 3.0, horizontal: 8),
                                      child: Text(
                                        'correct_answer',
                                        style: Styles.textBold(size: 10),
                                      ).tr(),
                                    ),
                                  )
                                : SizedBox(),
                          )
                        ],
                      ),
                    ),
                    _size(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  _multiChoose(TestReviewBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.questionOptions!
                  .length,
              (index) {
                Color borderColor;
                if (widget._list[widget._currentQuestion].question!
                        .questionOptions![index].optionId ==
                    int.parse(widget._list[widget._currentQuestion].question!
                        .correctOptions!.first)) {
                  borderColor = Color(0xff66bb6a);
                } else if (widget._list[widget._currentQuestion].question!
                            .questionOptions![index].optionId !=
                        int.parse(widget._list[widget._currentQuestion]
                            .question!.correctOptions!.first) &&
                    widget._list[widget._currentQuestion].question!
                            .questionOptions![index].userAnswer ==
                        1) {
                  borderColor = Colors.red;
                } else {
                  borderColor = Colors.grey;
                }
                return Column(
                  children: [
                    Container(
                      width: MediaQuery.of(_scaffoldContext).size.width,
                      height: 80,
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            child: Card(
                              elevation: 5,
                              margin: const EdgeInsets.only(right: 20),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                side:
                                    BorderSide(color: borderColor, width: 1.5),
                              ),
                              child: Container(
                                width:
                                    MediaQuery.of(_scaffoldContext).size.width -
                                        40,
                                height: 55,
                                alignment: Alignment.centerLeft,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 15),
                                child: Text(
                                  widget
                                      ._list[widget._currentQuestion]
                                      .question!
                                      .questionOptions![index]
                                      .optionStatement!,
                                  style: Styles.textRegular(size: 12),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 15,
                            left: 30,
                            child: widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .questionOptions![index]
                                        .optionId ==
                                    int.parse(widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .correctOptions!
                                        .first)
                                ? Container(
                                    color: Colors.grey[300],
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 3.0, horizontal: 8),
                                      child: Text(
                                        'correct_answer',
                                        style: Styles.textBold(size: 10),
                                      ).tr(),
                                    ),
                                  )
                                : SizedBox(),
                          ),
                          Positioned(
                            top: 15,
                            right: 30,
                            child: widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .questionOptions![index]
                                        .userAnswer ==
                                    1
                                ? Container(
                                    color: Colors.grey[300],
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 3.0, horizontal: 8),
                                      child: Text(
                                        'your_answer',
                                        style: Styles.textBold(size: 10),
                                      ).tr(),
                                    ),
                                  )
                                : SizedBox(),
                          )
                        ],
                      ),
                    ),
                    _size(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  _chooseOne(TestReviewBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.questionOptions!
                  .length,
              (index) {
                Color borderColor;
                if (widget._list[widget._currentQuestion].question!
                        .questionOptions![index].optionId ==
                    int.parse(widget._list[widget._currentQuestion].question!
                        .correctOptions!.first)) {
                  borderColor = Color(0xff66bb6a);
                } else if (widget._list[widget._currentQuestion].question!
                            .questionOptions![index].optionId !=
                        int.parse(widget._list[widget._currentQuestion]
                            .question!.correctOptions!.first) &&
                    widget._list[widget._currentQuestion].question!
                            .questionOptions![index].userAnswer ==
                        1) {
                  borderColor = Colors.red;
                } else {
                  borderColor = Colors.grey;
                }
                return Column(
                  children: [
                    Container(
                      width: MediaQuery.of(_scaffoldContext).size.width,
                      height: 80,
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            child: Card(
                              elevation: 5,
                              margin: const EdgeInsets.only(right: 20),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                side:
                                    BorderSide(color: borderColor, width: 1.5),
                              ),
                              child: Container(
                                width:
                                    MediaQuery.of(_scaffoldContext).size.width -
                                        40,
                                height: 55,
                                alignment: Alignment.centerLeft,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 15),
                                child: Text(
                                  widget
                                      ._list[widget._currentQuestion]
                                      .question!
                                      .questionOptions![index]
                                      .optionStatement!,
                                  style: Styles.textRegular(size: 12),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    _size(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
