import 'dart:async';
import 'dart:ui';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/save_answer_request.dart';
import 'package:masterg/data/models/response/home_response/test_attempt_response.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

class TestAttemptPage extends StatefulWidget {
  final int? contentId;
  final bool isReview;
  final bool isResumed;

  TestAttemptPage(
      {this.contentId, this.isReview = false, this.isResumed = false});

  @override
  _TestAttemptPageState createState() => _TestAttemptPageState();

  Timer? _allTimer;

  var _pageViewController = PageController();
  int currentSection = 0;
  var currentQuestion = 0;
  var currentQuestionNumber = 1;
  int? currentQuestionId;
  ScrollController? _questionController;
  bool? imageClick;
  List<TestAttemptBean> _list = [];
  Map<int, bool> attemptList = Map<int, bool>();
  int? _durationMins = 0;
  bool _isSubmit = false;
  bool showSubmitDialog = false;
  StopWatchTimer _stopWatchTimer = StopWatchTimer();
  bool _pageChange = true;
  bool _isOptionSelected = false;
  bool _isResumedLoading = false;
  bool lastSave = false;
  bool _isEverythingOver = false;
  DateTime? endTime;
  bool _savedAnswer = false;
  bool _isContinued = false;
  bool isSavedManually = false;
}

class IdMapper {
  int? questionId;
  String? color;
  int? timeTaken;

  IdMapper({this.questionId, this.color, this.timeTaken});

  IdMapper.fromJson(Map<String, dynamic> json) {
    questionId = json['questionId'];
    color = json['color'];
    timeTaken = json['timeTaken'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['questionId'] = this.questionId;
    data['color'] = this.color;
    data['timeTaken'] = this.timeTaken;
    return data;
  }
}

class _TestAttemptPageState extends State<TestAttemptPage>
    with WidgetsBindingObserver {
  //final GlobalKey<ScaffoldState> _key = new GlobalKey<ScaffoldState>();
  final _key = GlobalKey<ScaffoldMessengerState>();
  var _isLoading = false;
  var _scaffoldContext;
  late HomeBloc _authBloc;
  String? _title;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.inactive) {
      //widget._stopWatchTimer.onExecute.add(StopWatchExecute.stop);
      widget._stopWatchTimer.onStopTimer(); //add new 13-aug-2025
    }
    if (state == AppLifecycleState.resumed) {
      //widget._stopWatchTimer.onExecute.add(StopWatchExecute.start);
      widget._stopWatchTimer.onStartTimer(); //add new 13-aug-2025
    }
  }

  void _handleAttemptTestResponse(AttemptTestState state) {
    try {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          this.setState(() {
            _isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          if (state.response!.data != null) {
            widget._list.clear();
            widget._durationMins =
                state.response!.data!.assessmentDetails!.durationInMinutes;
            _title = state.response!.data!.assessmentDetails!.title;

            if (state.response!.data!.assessmentDetails!.questionCount ==
                null) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                duration: Duration(milliseconds: 500),
                content: Text('no_data').tr(),
              ));
            }
            if (widget._durationMins == null || widget._durationMins == '')
              widget._durationMins = 1800;
            else
              widget._durationMins = widget._durationMins! * 60;
            //widget._stopWatchTimer.onExecute.add(StopWatchExecute.start);
            widget._stopWatchTimer.onStartTimer(); //add new 13-aug-2025
            widget._allTimer = Timer.periodic(Duration(seconds: 1), (timer) {
              if (widget._allTimer!.tick == widget._durationMins) {
                widget._isEverythingOver = true;
              }
              setState(() {});
            });
            for (int i = 0;
                i < state.response!.data!.assessmentDetails!.questions!.length;
                i++) {
              widget._list.add(
                TestAttemptBean(
                    question:
                        state.response!.data!.assessmentDetails!.questions![i],
                    id: state.response!.data!.assessmentDetails!.questions![i]
                        .questionId,
                    isVisited: 0,
                    title: state.response!.data!.assessmentDetails!
                        .questions![i].question),
              );
            }
          }
          Utility.waitFor(2).then((value) {
            widget._isResumedLoading = false;
            widget._pageViewController.jumpToPage(widget.currentQuestion);
          });
          _isLoading = false;
          break;
        case ApiStatus.ERROR:
          this.setState(() {
            _isLoading = false;
          });
          FirebaseAnalytics.instance
              .logEvent(name: 'assessment_attempt', parameters: {
            "ERROR": '${state.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    } catch (e) {}
  }

  void _handleSaveAnswerResponse(SaveAnswerState state) {
    try {
      setState(() {
        switch (state.apiState) {
          case ApiStatus.LOADING:
            _isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            widget._isOptionSelected = false;
            widget._savedAnswer = false;
            widget.isSavedManually = false;

            bool _pageChanged = false;
            if (widget._isContinued == false) {
              if (widget._isEverythingOver == false) {
                _pageChanged = true;
                widget._pageViewController.nextPage(
                  duration: Duration(milliseconds: 100),
                  curve: Curves.ease,
                );
                // }
              } else {
                _submitAnswers();
              }

              _isLoading = false;
            } else {
              _isLoading = false;
              widget._isContinued = false;
              widget._pageChange = true;
            }
            break;
          case ApiStatus.ERROR:
            _isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e) {}
  }

  void _handleSubmitAnswerResponse(SubmitAnswerState state) {
    try {
      setState(() {
        //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
        widget._stopWatchTimer.onResetTimer(); //add new 13-aug-2025
        widget._allTimer!.cancel();
        widget._isSubmit = true;
        switch (state.apiState) {
          case ApiStatus.LOADING:
            _isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            _isLoading = false;

            AlertsWidget.alertWithOkBtn(
              context: _scaffoldContext,
              onOkClick: () {
                Navigator.pop(context);
              },
              text: "${tr('app_assessment_submit_one')}",
            );
            break;
          case ApiStatus.ERROR:
            Navigator.pop(context);
            _isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e) {}
  }

  @override
  void dispose() {
    if (widget._isSubmit || widget._list.length == 0) {
      widget._stopWatchTimer.dispose();
      widget._allTimer?.cancel();
    }
    super.dispose();
  }

  @override
  void initState() {
    widget._questionController = ScrollController();
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    _downloadListener();
  }

  @override
  Widget build(BuildContext context) {
    Application(context);
    _authBloc = BlocProvider.of<HomeBloc>(context);
    return BlocManager(
      initState: (context) {
        if (widget._list.length == 0) {
          _authBloc.add(
            AttemptTestEvent(
              request: widget.contentId.toString(),
            ),
          );
        }
      },
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is AttemptTestState) _handleAttemptTestResponse(state);
          if (state is SaveAnswerState) _handleSaveAnswerResponse(state);
          if (state is SubmitAnswerState) _handleSubmitAnswerResponse(state);
          // if (state is UploadImageState) _handleUploadImageResponse(state);
        },
        child: Builder(builder: (_context) {
          _scaffoldContext = _context;
          return WillPopScope(
            onWillPop: () async => false,
            child: Scaffold(
              backgroundColor: ColorConstants.WHITE,
              key: _key,
              bottomNavigationBar:
                  widget._list.length == 0 ? SizedBox() : _buildBottomAppBar(),
              body: SafeArea(
                child: ScreenWithLoader(
                  body: widget._list.length == 0
                      ? Column(
                          children: [
                            _heading(false),
                            Center(
                              child:
                                  Text(_isLoading ? 'please_wait' : 'no_data')
                                      .tr(),
                            ),
                          ],
                        )
                      : _content(),
                  isLoading: _isLoading,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  _saveClick() {
    if (widget._savedAnswer == false) {
      widget._savedAnswer = true;

      _authBloc.add(
        SaveAnswerEvent(
          request: SaveAnswerRequest(
            contentId: widget.contentId.toString(),
            durationSec: widget._allTimer!.tick.toString(),
            questionId: widget
                ._list[widget.currentQuestion].question!.questionId
                .toString(),
            optionId:
                widget._list[widget.currentQuestion].question!.selectedOption,
          ),
        ),
      );
      if (widget._list[widget.currentQuestion].isBookmark) {
        if (widget
            ._list[widget.currentQuestion].question!.selectedOption.isEmpty) {
          widget._list[widget.currentQuestion].color = ColorConstants.REVIEWED;
        } else {
          widget._list[widget.currentQuestion].color =
              ColorConstants.ANSWERED_REVIEWS;
        }
      } else {
        if (widget
            ._list[widget.currentQuestion].question!.selectedOption.isEmpty) {
          widget._list[widget.currentQuestion].color =
              ColorConstants.NOT_ANSWERED;
        } else {
          widget._list[widget.currentQuestion].color = ColorConstants.ANSWERED;
        }
      }
      widget._list[widget.currentQuestion].question!.timeTaken =
          widget._stopWatchTimer.secondTime.value;
      _setTimer();
    }
  }

  void _setTimer() {
    if ((widget._list.length - 1) != widget.currentQuestion) {
      if (widget._list[widget.currentQuestion + 1].question?.timeTaken !=
              null &&
          widget._list[widget.currentQuestion + 1].question!.timeTaken != 0) {
        widget._stopWatchTimer.setPresetSecondTime(
            widget._list[widget.currentQuestion + 1].question!.timeTaken!);
      } else {
        widget._stopWatchTimer.onResetTimer();
        //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
        widget._stopWatchTimer.onStartTimer();
        //widget._stopWatchTimer.onExecute.add(StopWatchExecute.start);
      }
      widget._pageChange = false;
    }
  }

  _heading(bool iscontent) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Row(
        children: [
          TapWidget(
            onTap: () {
              if (iscontent) {
                AlertsWidget.alertWithOkCancelBtn(
                  context: _scaffoldContext,
                  onOkClick: () {
                    //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
                    widget._stopWatchTimer.onResetTimer(); //add new 13-aug-2025
                    widget._allTimer!.cancel();
                    _saveClick();
                    _submitAnswers();
                  },
                  text: "${tr('Yapp_assessment_submit_two')}",
                  title: "${tr('finish_test')}",
                );
              } else {
                Navigator.pop(context);
              }
            },
            child: Container(
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(10))),
              child: Icon(
                Icons.arrow_back,
                color: ColorConstants.DARK_BLUE,
              ),
            ),
          ),
          Expanded(
              child: Text(
            _title ?? "",
            textAlign: TextAlign.center,
            style: Styles.regular(size: 20),
          ))
        ],
      ),
    );
  }

  Widget _content() {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _size(),
          _heading(true),
          _timerSubmit(),
          _size(),
          Expanded(
            child: Container(
              child: Column(
                children: [
                  _size(height: 20),
                  _questionCount(),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 19),
                    child: Divider(),
                  ),
                  _pageView(),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  _pageView() {
    return Expanded(
      child: Container(
        child: PageView.builder(
          itemBuilder: (context, index) {
            return widget._isResumedLoading
                ? Center(
                    child: CircularProgressIndicator(),
                  )
                : _pageItem(widget._list[index]);
          },
          onPageChanged: (pageNumber) {
            setState(() {
              widget.currentSection = 0;
              widget.currentQuestionId =
                  widget._list[pageNumber].question!.questionId;
              widget.currentQuestion = pageNumber;

              widget._list[pageNumber].isVisited = 1;

              final questionsLength = widget._list.length;

              Utility.waitForMili(200).then((value) {
                if (widget.currentQuestion + 2 >= questionsLength * .6) {
                  if (widget._questionController!.position.pixels !=
                      widget._questionController!.position.maxScrollExtent) {
                    widget._questionController!
                        .jumpTo(((widget.currentQuestion + 2) * 30).toDouble());
                  }
                } else if ((widget.currentQuestion + 2) <=
                    questionsLength * .3) {
                  if (widget._questionController!.position.pixels != 0) {
                    widget._questionController!.jumpTo(0);
                  }
                }
              });
            });
          },
          controller: widget._pageViewController,
          itemCount: widget._list.length,
          physics: NeverScrollableScrollPhysics(),
        ),
      ),
    );
  }

  _pageItem(TestAttemptBean testAttemptBean) {
    return Container(
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _size(),
            _questionNumber(testAttemptBean),
            _size(height: 10),
            if (testAttemptBean.question!.questionImage != null)
              for (int i = 0;
                  i < testAttemptBean.question!.questionImage!.length;
                  i++)
                if (testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.mp4') ||
                    testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.mp3'))
                  Container(
                    height: 300,
                    width: 300,
                    alignment: Alignment.center,
                    child: Center(
                      child: InAppWebView(
                          initialOptions: InAppWebViewGroupOptions(
                              crossPlatform: InAppWebViewOptions(
                                mediaPlaybackRequiresUserGesture: true,
                                useShouldOverrideUrlLoading: true,
                              ),
                              ios: IOSInAppWebViewOptions(
                                  allowsInlineMediaPlayback: true,
                                  allowsLinkPreview: false)),
                          initialUrlRequest: URLRequest(
                              url: WebUri(testAttemptBean.question!.questionImage![i]))),
                    ),
                  )
                else
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.network(
                        testAttemptBean.question!.questionImage![i]),
                  ),
            _size(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                testAttemptBean.question!.question ?? "",
                style: Styles.textExtraBold(size: 16),
              ),
            ),
            _size(height: 10),
            _solutionType(testAttemptBean.question!.questionTypeId.toString(),
                testAttemptBean),
            _size(height: 10),
          ],
        ),
      ),
    );
  }

  _size({double height = 10}) {
    return SizedBox(
      height: height,
    );
  }

  String _getAllTime() {
    String _timeH = "";
    String _timeM = "";
    String _timeS = "";
    var localTime;

    localTime = widget._durationMins! - widget._allTimer!.tick;
    if ((localTime / 3600).truncate() < 10) {
      _timeH = "0${(localTime / 3600).truncate()}";
    } else {
      _timeH = (localTime / 3600).truncate().toString();
    }
    _timeM = (((localTime / 60).truncate()) % 60).toString().padLeft(2, '0');
    if ((localTime % 60).truncate() < 10) {
      _timeS = "0${(localTime % 60).truncate()}";
    } else {
      _timeS = (localTime % 60).truncate().toString();
    }

    if (widget._allTimer!.tick == widget._durationMins && !widget._isSubmit) {
      _submitAnswers();
    }

    return "$_timeH:$_timeM:$_timeS";
  }

  _timerSubmit() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.08),
                      blurRadius: 32,
                      offset: Offset(0, 4))
                ]),
            child: Text(
              "${tr('time_left')} - ${_getAllTime()}",
              style: Styles.textBold(size: 16),
            ),
          ),
          Spacer(),
          TapWidget(
            onTap: () {
              if (widget.isReview) {
                Navigator.pop(context);
              } else {
                if (!widget._isOptionSelected) {
                  AlertsWidget.alertWithOkCancelBtn(
                    context: _scaffoldContext,
                    onOkClick: () {
                      //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
                      widget._stopWatchTimer.onResetTimer();
                      widget._allTimer!.cancel();
                      _submitAnswers();
                    },
                    text: "${tr('app_assessment_submit_three')}",
                    title: "${tr('finish_test')}",
                  );
                } else {
                  AlertsWidget.alertWithOkBtn(
                    context: _scaffoldContext,
                    onOkClick: () {
                      widget.showSubmitDialog = true;
                    },
                    text: "${tr('app_assessment_submit_three')}",
                  );
                }
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.08),
                        blurRadius: 32,
                        offset: Offset(0, 4))
                  ]),
              child: Text(
                'submit',
                style: Styles.textBold(size: 16),
              ).tr(),
            ),
          ),
        ],
      ),
    );
  }

  _questionCount() {
    return widget._list.length == 0
        ? SizedBox()
        : Padding(
            padding: const EdgeInsets.symmetric(horizontal: 19),
            child: Container(
              height: 60,
              width: MediaQuery.of(_scaffoldContext).size.width,
              child: ListView.builder(
                  controller: widget._questionController,
                  shrinkWrap: true,
                  physics: ClampingScrollPhysics(),
                  itemBuilder: (context, index) {
                    return TapWidget(
                      onTap: () {
                        if (widget._isOptionSelected) {
                          AlertsWidget.alertWithOkBtn(
                              context: _scaffoldContext,
                              text: "${tr('app_assessment_submit_three')}tion");
                          return;
                        }
                        if (widget.currentQuestionId ==
                            widget._list[index].question!.questionId) {
                          return;
                        }
                        for (int i = 0; i < widget._list.length; i++) {
                          if (widget._list[i].question!.questionId ==
                              widget._list[index].question!.questionId) {
                            widget.currentQuestionId =
                                widget._list[index].question!.questionId;
                            widget.currentQuestion = i;
                            //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
                            widget._stopWatchTimer.onResetTimer();
                            widget._pageViewController.animateToPage(i,
                                duration: Duration(milliseconds: 100),
                                curve: Curves.ease);
                            Utility.waitForMili(200).then((value) {
                              widget._stopWatchTimer = StopWatchTimer();

                              widget._stopWatchTimer.setPresetSecondTime(widget
                                  ._list[widget.currentQuestion]
                                  .question!
                                  .timeTaken!);
                             //widget._stopWatchTimer.onExecute.add(StopWatchExecute.start);
                             widget._stopWatchTimer.onStartTimer();
                            });

                            break;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 32),
                        child: Container(
                          width: 35,
                          height: 35,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.grey,
                                  offset: Offset(0, 8),
                                  blurRadius: 30)
                            ],
                            color: widget._list[index].color,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            "${index + 1}",
                            style: index == widget.currentQuestion
                                ? Styles.textBold()
                                : Styles.textLight(),
                          ),
                        ),
                      ),
                    );
                  },
                  scrollDirection: Axis.horizontal,
                  itemCount: widget._list.length),
            ),
          );
  }

  _questionNumber(TestAttemptBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Text(
            "${tr('q')}${(widget.currentQuestion + 1).toString().padLeft(2, '0')}",
            style: Styles.textRegular(size: 22),
          ),
          SizedBox(
            width: 10,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 0.0),
            child: TapWidget(
              onTap: () {
                widget._list[widget.currentQuestion].isBookmark =
                    !widget._list[widget.currentQuestion].isBookmark;
                if (widget._list[widget.currentQuestion].isBookmark) {
                  if (widget._list[widget.currentQuestion].question!
                      .selectedOption.isEmpty) {
                    widget._list[widget.currentQuestion].color =
                        ColorConstants.REVIEWED;
                  } else {
                    widget._list[widget.currentQuestion].color =
                        ColorConstants.ANSWERED_REVIEWS;
                  }
                } else {
                  if (widget._list[widget.currentQuestion].question!
                      .selectedOption.isEmpty) {
                    widget._list[widget.currentQuestion].color =
                        ColorConstants.NOT_ANSWERED;
                  } else {
                    widget._list[widget.currentQuestion].color =
                        ColorConstants.ANSWERED;
                  }
                }
                setState(() {});
              },
              child: Icon(
                widget._list[widget.currentQuestion].isBookmark
                    ? Icons.bookmark_outlined
                    : Icons.bookmark_border_outlined,
                color: ColorConstants.REVIEWED,
                size: 25,
              ),
            ),
          ),
          SizedBox(
            width: 10,
          ),
          Visibility(
            visible: (widget._list[widget.currentQuestion].question
                        ?.selectedOption.length ??
                    0) >
                0,
            child: TapWidget(
              onTap: () {
                setState(() {
                  widget._list[widget.currentQuestion].question!.selectedOption
                      .clear();
                  for (var data in widget
                      ._list[widget.currentQuestion].question!.options!) {
                    data.selected = false;
                  }

                  widget._isOptionSelected = false;
                });
              },
              child: Text(
                'clear_response',
                style: Styles.textBold(),
              ).tr(),
            ),
          ),
          Spacer(),
        ],
      ),
    );
  }

  _solutionType(String type, TestAttemptBean testAttemptBean) {
    switch (type) {
      case "1":
        return _multiChoose(testAttemptBean); //MULTIPLE_CHOICE
      case "2":
        return _options(testAttemptBean); //SINGLE_INTEGER
      case "3":
        return _chooseOne(testAttemptBean); //MULTIPLE_RESPONSE
      case "4":
        return _chooseOne(testAttemptBean); //FILL_IN_THE_BLANK
      case "5":
        return _chooseOne(testAttemptBean); //TRUE_FALSE
      case "6":

      case "7":
        return Container(); //MATCHING

    }
  }

  Future download2(String url, String savePath) async {
    try {
      _key.currentState!.showSnackBar(
        SnackBar(
          content: Text(
            'downloading_start',
            style: Styles.boldWhite(),
          ).tr(),
          backgroundColor: ColorConstants.BLACK,
          duration: Duration(seconds: 2),
        ),
      );
      final taskId = await FlutterDownloader.enqueue(
        url: url,
        savedDir: savePath,
        showNotification: true,
        headers: {"auth": "test_for_sql_encoding"},
        openFileFromNotification: true,
      );
      Log.v(taskId);
    } catch (e) {
      Log.v(e);
    }
  }

  @pragma('vm:entry-point')
  static void downloadCallback(
    String id,
    DownloadTaskStatus status,
    int progress,
  ) {
    print(
      'Callback on background isolate: '
      'task ($id) is in status ($status) and process ($progress)',
    );

    IsolateNameServer.lookupPortByName('downloader_send_port')
        ?.send([id, status, progress]);
  }

  // @pragma('vm:entry-point')
  // static void downloadCallback(
  //     String id,
  //     int status,
  //     int progress,
  //     ) {
  //   print(
  //     'Callback on background isolate: '
  //         'task ($id) is in status ($status) and process ($progress)',
  //   );

  //   IsolateNameServer.lookupPortByName('downloader_send_port')
  //       ?.send([id, status, progress]);
  // }

  //ReceivePort _port = ReceivePort();

  _downloadListener() {
    // FlutterDownloader.registerCallback(downloadCallback, step: 1);
  }

  _options(TestAttemptBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          Column(
            children: List.generate(
              widget._list[widget.currentQuestion].question!.options!.length,
              (index) => Column(
                children: [
                  TapWidget(
                    onTap: () {
                      widget._isOptionSelected = true;
                      setState(() {
                        widget._list[widget.currentQuestion].question!
                            .selectedOption
                            .clear();
                        widget._list[widget.currentQuestion].question!
                            .selectedOption
                            .add(widget._list[widget.currentQuestion].question!
                                .options![index].optionId);
                        for (var data = 0;
                            data <
                                widget._list[widget.currentQuestion].question!
                                    .options!.length;
                            data++) {
                          if (widget._list[widget.currentQuestion].question!
                                  .options![data].optionId ==
                              widget._list[widget.currentQuestion].question!
                                  .selectedOption.first) {
                            widget._list[widget.currentQuestion].question!
                                .options![data].selected = true;
                          } else {
                            widget._list[widget.currentQuestion].question!
                                .options![data].selected = false;
                          }
                        }
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          color: widget._list[widget.currentQuestion].question!
                                  .options![index].selected
                              ? ColorConstants.SELECTED_GREEN
                              : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                                color: Color.fromRGBO(0, 0, 0, 0.05),
                                blurRadius: 16,
                                offset: Offset(0, 8))
                          ]),
                      child: Container(
                        width: MediaQuery.of(_scaffoldContext).size.width,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 25),
                        child: Text(
                          widget._list[widget.currentQuestion].question!
                              .options![index].optionStatement!,
                          style: widget._list[widget.currentQuestion].question!
                                  .options![index].selected
                              ? Styles.boldWhite(size: 18)
                              : Styles.textRegular(
                                  size: 18,
                                ),
                        ),
                      ),
                    ),
                  ),
                  _size(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _multiChoose(TestAttemptBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textRegular(size: 20),
            ),
          ),
          Column(
            children: List.generate(
              widget._list[widget.currentQuestion].question!.options!.length,
              (index) => Column(
                children: [
                  TapWidget(
                    onTap: () {
                      widget._isOptionSelected = true;
                      setState(() {
                        widget._list[widget.currentQuestion].question!
                            .selectedOption
                            .clear();
                        widget._list[widget.currentQuestion].question!
                            .selectedOption
                            .add(widget._list[widget.currentQuestion].question!
                                .options![index].optionId);
                        for (var data = 0;
                            data <
                                widget._list[widget.currentQuestion].question!
                                    .options!.length;
                            data++) {
                          if (widget._list[widget.currentQuestion].question!
                                  .options![data].optionId ==
                              widget._list[widget.currentQuestion].question!
                                  .selectedOption.first) {
                            widget._list[widget.currentQuestion].question!
                                .options![data].selected = true;
                          } else {
                            widget._list[widget.currentQuestion].question!
                                .options![data].selected = false;
                          }
                        }
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          color: widget._list[widget.currentQuestion].question!
                                  .options![index].selected
                              ? ColorConstants.SELECTED_GREEN
                              : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                                color: Color.fromRGBO(0, 0, 0, 0.05),
                                blurRadius: 16,
                                offset: Offset(0, 8))
                          ]),
                      child: Container(
                        width: MediaQuery.of(_scaffoldContext).size.width,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 25),
                        child: Text(
                          widget._list[widget.currentQuestion].question!
                              .options![index].optionStatement!,
                          style: widget._list[widget.currentQuestion].question!
                                  .options![index].selected
                              ? Styles.boldWhite(size: 18)
                              : Styles.textRegular(
                                  size: 18,
                                  color: Color.fromRGBO(28, 37, 85, 0.58)),
                        ),
                      ),
                    ),
                  ),
                  _size(height: 7),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _chooseOne(TestAttemptBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          Column(
            children: List.generate(
              widget._list[widget.currentQuestion].question!.options!.length,
              (index) => Column(
                children: [
                  TapWidget(
                    onTap: () {
                      widget._isOptionSelected = true;
                      setState(() {
                        widget._list[widget.currentQuestion].question!
                            .selectedOption
                            .clear();
                        widget._list[widget.currentQuestion].question!
                            .selectedOption
                            .add(widget._list[widget.currentQuestion].question!
                                .options![index].optionId);
                        for (var data = 0;
                            data <
                                widget._list[widget.currentQuestion].question!
                                    .options!.length;
                            data++) {
                          if (widget._list[widget.currentQuestion].question!
                                  .options![data].optionId ==
                              widget._list[widget.currentQuestion].question!
                                  .selectedOption.first) {
                            widget._list[widget.currentQuestion].question!
                                .options![data].selected = true;
                          } else {
                            widget._list[widget.currentQuestion].question!
                                .options![data].selected = false;
                          }
                        }
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          color: widget._list[widget.currentQuestion].question!
                                  .options![index].selected
                              ? ColorConstants.SELECTED_GREEN
                              : Colors.grey[300],
                          borderRadius: BorderRadius.circular(5),
                          boxShadow: [
                            BoxShadow(
                                color: Color.fromRGBO(0, 0, 0, 0.05),
                                blurRadius: 16,
                                offset: Offset(0, 8))
                          ]),
                      child: Container(
                        width: MediaQuery.of(_scaffoldContext).size.width,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 20),
                        child: Text(
                          widget._list[widget.currentQuestion].question!
                              .options![index].optionStatement!,
                          style: widget._list[widget.currentQuestion].question!
                                  .options![index].selected
                              ? Styles.boldWhite(size: 18)
                              : Styles.textRegular(
                                  size: 18,
                                ),
                        ),
                      ),
                    ),
                  ),
                  _size(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _submitAnswers() {
    _authBloc.add(SubmitAnswerEvent(request: widget.contentId.toString()));
  }

  void _handlePreviousButton() {
    widget._pageViewController.previousPage(
      duration: Duration(milliseconds: 100),
      curve: Curves.ease,
    );
  }

  _buildBottomAppBar() {
    if ((widget._list.length - 1) == widget.currentQuestion) {
      widget.lastSave = true;
    }
    return BottomAppBar(
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            widget.currentQuestion == 0
                ? SizedBox()
                : TapWidget(
                    onTap: () {
                      if (widget._isOptionSelected) {
                        AlertsWidget.alertWithOkBtn(
                          context: _scaffoldContext,
                          onOkClick: () {
                            widget.showSubmitDialog = true;
                          },
                          text: "${tr('app_assessment_submit_three')}",
                        );
                        return;
                      }
                      _handlePreviousButton();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          color: Color.fromRGBO(157, 191, 242, 1),
                          borderRadius: BorderRadius.circular(8)),
                      child: Text(
                        'back',
                        style: Styles.textBold(
                            size: 16, color: Color.fromRGBO(53, 68, 116, 1)),
                      ).tr(),
                    ),
                  ),
            TapWidget(
              onTap: () {
                if ((widget._list.length - 1) == widget.currentQuestion) {
                  widget.lastSave = true;
                  AlertsWidget.alertWithOkCancelBtn(
                      context: _scaffoldContext,
                      onOkClick: () {
                        //widget._stopWatchTimer.onExecute.add(StopWatchExecute.reset);
                        widget._stopWatchTimer.onResetTimer();
                        widget._allTimer!.cancel();
                        _saveClick();
                        widget._isEverythingOver = true;
                      },
                      text: "${tr('app_assessment_submit_three')}",
                      title: "${tr('finish_test')}",
                      onCancelClick: () {
                        widget._isOptionSelected = false;
                        widget._isContinued = true;
                        widget.lastSave = false;
                        _saveClick();
                      });
                } else {
                  _saveClick();
                  widget.isSavedManually = true;
                }
              },
              child: Container(
                width: 100,
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: ColorConstants().primaryColor(),
                    borderRadius: BorderRadius.circular(8)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      ((widget._list.length - 1) == widget.currentQuestion)
                          ? "save"
                          : "next",
                      style: Styles.textBold(size: 16, color: Colors.white),
                    ).tr(),
                    const SizedBox(
                      width: 5,
                    ),
                    Icon(Icons.arrow_forward, size: 15, color: Colors.white)
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
