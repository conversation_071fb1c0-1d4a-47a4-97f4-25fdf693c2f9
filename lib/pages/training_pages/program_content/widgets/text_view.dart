import 'dart:ui' as ui;
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/CommonHTMLWebview.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/CommonWebView.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:url_launcher/url_launcher.dart';

class TextView extends StatelessWidget {
  final LearningShots learningShorts;
  const TextView({super.key, required this.learningShorts});

  @override
  Widget build(BuildContext context) {
    bool isButtonActive = false;
    Color bgColor;
    if (learningShorts.contentType?.toLowerCase() == 'text') {
      isButtonActive = true;
    } else {
      isButtonActive = false;
    }

    bgColor = !isButtonActive
        ? ColorConstants.GREY_2
        : ColorConstants().primaryColorAlways();
    return Container(
      color: ColorConstants.COURSE_BG,
      height: MediaQuery.of(context).size.height * 0.24,
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${tr('submit_before')} : ',
                style: Styles.bold(size: 14, color: ColorConstants.WHITE),
              ),
              Text(
                '${learningShorts.dueDate != null ? Utility.convertDateFromMillis(learningShorts.dueDate!, Strings.REQUIRED_DATE_HH_MM_AAA_DD_MMM_YYYY) : ''}',
                style: Styles.bold(size: 14, color: ColorConstants.WHITE),
                textDirection: ui.TextDirection.ltr,
              ),
            ],
          ),
          SizedBox(height: 15),
          Text('${learningShorts.title}',
              style: Styles.bold(size: 16, color: ColorConstants.WHITE)),
          // if (learningShorts.description != null)
          //   Text('${learningShorts.description}',
          //       maxLines: 2,
          //       overflow: TextOverflow.ellipsis,
          //       style: Styles.semibold(size: 14, color: ColorConstants.WHITE)),
          // SizedBox(height: 8),
          Spacer(),
          InkWell(
            onTap: () {
              // final url = extractUrl(input: learningShorts.description);
              // _launchUrl(url!);

              if (isButtonActive) {
                print('${'url is ${learningShorts.content}'}');

                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) {
                      return CommonHTMLWebView(
                          title: '${learningShorts.title}',
                          url: '${learningShorts.description}');
                      // CommonWebView(url: '${learningShorts.content}');
                    },
                  ),
                );
              }
            },
            child: Container(
                width: width(context),
                height: 38,
                margin: const EdgeInsets.symmetric(horizontal: 18, vertical: 4),
                decoration: BoxDecoration(
                    color: bgColor,
                    gradient: bgColor == ColorConstants().primaryColorAlways()
                        ? LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: <Color>[
                                ColorConstants().gradientLeft(),
                                ColorConstants().gradientRight()
                              ])
                        : null,
                    borderRadius: BorderRadius.circular(8)),
                child: Center(
                  child: Text(
                    'open_text_view',
                    style: Styles.regular(
                        size: 14,
                        color: bgColor == ColorConstants.GREY_2
                            ? ColorConstants.GREY_3
                            : ColorConstants().primaryForgroundColor()),
                    textAlign: TextAlign.center,
                  ).tr(),
                )),
          )
        ],
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  String? extractUrl({String? input}) {
    final urlPattern = RegExp(r'(https?:\/\/[^\s]+)');
    final match = urlPattern.firstMatch(input!);
    return match != null ? match.group(0) : '';
  }
}
