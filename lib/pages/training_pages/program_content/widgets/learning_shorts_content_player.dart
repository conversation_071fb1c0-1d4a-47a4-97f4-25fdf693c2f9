import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_notes.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:smooth_video_progress/smooth_video_progress.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:math' as math;

import '../../../../blocs/home_bloc.dart';
import 'common_webview_page.dart';

class LearningShortsContentPlayer extends StatefulWidget {
  final bool isNoteView;
  final LearningShots learningShorts;
  const LearningShortsContentPlayer(
      {super.key, required this.isNoteView, required this.learningShorts});

  @override
  State<LearningShortsContentPlayer> createState() => _LearningShortsState();
}

class _LearningShortsState extends State<LearningShortsContentPlayer> {
  VideoPlayerController? _controller;
  int currentMin = 0;
  int prevMin = 0;

  @override
  void initState() {
    if (widget.isNoteView) {
    } else {
      _controller = VideoPlayerController.networkUrl(
          Uri.parse("${widget.learningShorts.url}"));
    }
    super.initState();
  }

  @override
  void dispose() {
    if (!widget.isNoteView) {
      _controller?.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          color: Colors.black,
          child: RotatedBox(
            quarterTurns: widget.isNoteView ? 0 : 1,
            child: Stack(
              children: [
                Center(
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height,
                    width: double.infinity,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(0),
                      child: Stack(
                        children: [
                          Hero(
                            tag: 'videoPlayer',
                            child: widget.isNoteView
                                ? widget.learningShorts.url.toString().toLowerCase().contains('pdf') ? CompetitionNotes(
                                    notesUrl: widget.learningShorts.url,
                                    // id: selectedContentId
                              title: widget.learningShorts.title,
                                    id: widget.learningShorts.programContentId,

                                  ): widget.learningShorts.url.toString().toLowerCase().contains('jpg') ||
                                widget.learningShorts.url.toString().toLowerCase().contains('jpeg') ||
                                widget.learningShorts.url.toString().toLowerCase().contains('png') ? CommonWebviewPage(strUrl: widget.learningShorts.url)
                                : CommonWebviewPage(strUrl: 'https://view.officeapps.live.com/op/embed.aspx?src=${widget.learningShorts.url}',)
                                : VisibilityDetector(
                                    key: Key("popUpUrlVideoPlayer"),
                                    onVisibilityChanged: (VisibilityInfo info) {
                                      if (info.visibleFraction == 1.0) {
                                        currentMin = 0;
                                        prevMin = 0;
                                        listenVideoChanges(_controller);
                                      }
                                    },
                                    child: VideoPlayer(_controller!)),
                          ),
                          if (!widget.isNoteView)
                            Positioned.fill(
                              child: ValueListenableBuilder(
                                valueListenable: _controller!,
                                builder:
                                    (context, VideoPlayerValue value, child) {
                                  //Do Something with the value.
                                  return Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      GestureDetector(
                                          onTap: () {
                                            _controller?.seekTo(Duration(
                                                seconds: _controller!.value
                                                        .position.inSeconds -
                                                    10));
                                          },
                                          child: SvgPicture.asset(
                                            'assets/images/rewind.svg',
                                            color: ColorConstants.WHITE,
                                            height: 30,
                                            width: 30,
                                            allowDrawingOutsideViewBox: true,
                                          )),
                                      GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _controller!.value.isPlaying
                                                ? _controller?.pause()
                                                : _controller?.play();
                                          });
                                        },
                                        child: !value.isPlaying
                                            ? SvgPicture.asset(
                                                'assets/images/play.svg',
                                                color: ColorConstants.WHITE,
                                                height: 30,
                                                width: 30,
                                                allowDrawingOutsideViewBox:
                                                    true,
                                              )
                                            : Icon(Icons.pause,
                                                color: ColorConstants.WHITE,
                                                size: 30),
                                      ),
                                      GestureDetector(
                                          onTap: () {
                                            _controller?.seekTo(Duration(
                                                seconds: _controller!.value
                                                        .position.inSeconds +
                                                    10));
                                          },
                                          child: SvgPicture.asset(
                                            'assets/images/forward.svg',
                                            color: ColorConstants.WHITE,
                                            height: 30,
                                            width: 30,
                                            allowDrawingOutsideViewBox: true,
                                          )),
                                    ],
                                  );
                                },
                              ),
                            ),
                          if (!widget.isNoteView)
                            Positioned(
                                bottom: 0,
                                left: 0,
                                right: 0,
                                child: Transform.rotate(
                                  angle:
                                      Utility().isRTL(context) ? -math.pi : 0,
                                  child: Container(
                                    width: width(context),
                                    child: SmoothVideoProgress(
                                      controller: _controller!,
                                      builder: (context, position, duration,
                                              child) =>
                                          SliderTheme(
                                        data: SliderThemeData(
                                          trackShape: CustomTrackShape(11),
                                        ),
                                        child: Slider(
                                          onChangeStart: (_) =>
                                              _controller?.pause(),
                                          onChangeEnd: (_) =>
                                              _controller?.play(),
                                          onChanged: (value) =>
                                              _controller?.seekTo(Duration(
                                                  milliseconds: value.toInt())),
                                          value: position.inMilliseconds
                                              .toDouble(),
                                          min: 0,
                                          max: duration.inMilliseconds
                                              .toDouble(),
                                        ),
                                      ),
                                    ),
                                  ),
                                )),
                        ],
                      ),
                    ),
                  ),
                ),
                if (!widget.isNoteView)
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Icon(
                        Icons.cancel,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void listenVideoChanges(_controller) {
    _controller.addListener(() {
      currentMin = _controller.value.position.inMinutes;
      if (currentMin != 0 && prevMin != currentMin) {
        prevMin = currentMin;
        _updateCourseCompletion(currentMin, 0);
      }
    });
  }

  void _updateCourseCompletion(bookmark, int completionPer) async {
    //change bookmark with 25
    BlocProvider.of<HomeBloc>(context).add(UpdateVideoCompletionEvent(
        bookmark: bookmark,
        contentId: widget.learningShorts.programContentId,
        completionPercent: completionPer));
    setState(() {});
  }
}

class CustomTrackShape extends RoundedRectSliderTrackShape {
  final movefromleft;

  CustomTrackShape(this.movefromleft);

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final trackHeight = sliderTheme.trackHeight;
    final trackLeft = offset.dx;
    final trackTop = offset.dy + (parentBox.size.height - trackHeight!) / 2;
    final trackWidth = parentBox.size.width * 0.95;
    return Rect.fromLTWH(
        trackLeft + movefromleft, trackTop, trackWidth, trackHeight);
  }
}
