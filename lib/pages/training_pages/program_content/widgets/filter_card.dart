import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/pages/training_pages/program_content/program_content_provider.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class FilterCard extends StatelessWidget {
  final ProgramContentProvider value;
  const FilterCard({super.key, required this.value});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  value.changeFilterType(filterType: FilterType.none);
                  // isAllSelected = true;

                  // selectedContentId = null;

                  // _controller.pause();

                  // selectedType = 'Classes';

                  // setState(() {});
                },
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: value.appliedFilter == FilterType.none
                          ? LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ])
                          : null,
                      color: value.appliedFilter == FilterType.none
                          ? ColorConstants().primaryColor()
                          : ColorConstants.WHITE),
                  child: Text('modules',
                          style: Styles.regular(
                              size: 14,
                              color: value.appliedFilter == FilterType.none
                                  ? ColorConstants.WHITE
                                  : ColorConstants.GREY_3))
                      .tr(),
                ),
              ),
              InkWell(
                onTap: () {
                  value.changeFilterType(filterType: FilterType.classes);
                  // selectedType = 'Classes';
                  // isAllSelected = false;
                  // selectedData = null;
                  // selectedContentId = null;
                  // _controller.pause();

                  // setState(() {});
                  // print(
                  //     "value is now check ${selectedData.contentType.toString()}");
                },
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: value.appliedFilter == FilterType.classes
                          ? LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ])
                          : null,
                      color: value.appliedFilter == FilterType.classes
                          ? ColorConstants().primaryColor()
                          : ColorConstants.WHITE),
                  child: Text('classes',
                          style: Styles.regular(
                              size: 14,
                              color: value.appliedFilter == FilterType.classes
                                  ? ColorConstants.WHITE
                                  : ColorConstants.GREY_3))
                      .tr(),
                ),
              ),
              InkWell(
                  onTap: () {
                    value.changeFilterType(filterType: FilterType.videos);
                    // selectedType = 'Videos';
                    // isAllSelected = false;

                    // selectedContentId = null;

                    // _controller.pause();

                    // setState(() {});
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: value.appliedFilter == FilterType.videos
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : null,
                        color: value.appliedFilter == FilterType.videos
                            ? ColorConstants().primaryColor()
                            : ColorConstants.WHITE),
                    child: Text('videos',
                            style: Styles.regular(
                                size: 14,
                                color: value.appliedFilter == FilterType.videos
                                    ? ColorConstants.WHITE
                                    : ColorConstants.GREY_3))
                        .tr(),
                  )),
              InkWell(
                  onTap: () {
                    value.changeFilterType(filterType: FilterType.notes);
                    // setState(() {
                    // selectedType = 'Notes';
                    // selectedContentId = null;
                    // isAllSelected = false;

                    // _controller.pause();
                    // });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: value.appliedFilter == FilterType.notes
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : null,
                        color: value.appliedFilter == FilterType.notes
                            ? ColorConstants().primaryColor()
                            : ColorConstants.WHITE),
                    child: Text('notes',
                            style: Styles.regular(
                                size: 14,
                                color: value.appliedFilter == FilterType.notes
                                    ? ColorConstants.WHITE
                                    : ColorConstants.GREY_3))
                        .tr(),
                  )),
              InkWell(
                  onTap: () {
                    value.changeFilterType(filterType: FilterType.assignment);
                    // setState(() {
                    // selectedType = 'Assignment';
                    // selectedContentId = null;
                    // isAllSelected = false;

                    // _controller.pause();
                    // });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: value.appliedFilter == FilterType.assignment
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : null,
                        color: value.appliedFilter == FilterType.assignment
                            ? ColorConstants().primaryColor()
                            : ColorConstants.WHITE),
                    child: Text('assignment',
                            style: Styles.regular(
                                size: 14,
                                color:
                                    value.appliedFilter == FilterType.assignment
                                        ? ColorConstants.WHITE
                                        : ColorConstants.GREY_3))
                        .tr(),
                  )),
              InkWell(
                  onTap: () {
                    value.changeFilterType(filterType: FilterType.quiz);
                    // setState(() {
                    // selectedType = 'Quiz';
                    // selectedContentId = null;
                    // isAllSelected = false;

                    // _controller.pause();
                    // });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: value.appliedFilter == FilterType.quiz
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : null,
                        color: value.appliedFilter == FilterType.quiz
                            ? ColorConstants().primaryColor()
                            : ColorConstants.WHITE),
                    child: Text('assessment',
                            style: Styles.regular(
                                size: 14,
                                color: value.appliedFilter == FilterType.quiz
                                    ? ColorConstants.WHITE
                                    : ColorConstants.GREY_3))
                        .tr(),
                  )),
              InkWell(
                  onTap: () {
                    value.changeFilterType(filterType: FilterType.scorm);
                    // setState(() {
                    // selectedType = 'Scorm';
                    // selectedContentId = null;
                    // isAllSelected = false;

                    // _controller.pause();
                    // });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: value.appliedFilter == FilterType.scorm
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : null,
                        color: value.appliedFilter == FilterType.scorm
                            ? ColorConstants().primaryColor()
                            : ColorConstants.WHITE),
                    child: Text('scorm',
                            style: Styles.regular(
                                size: 14,
                                color: value.appliedFilter == FilterType.scorm
                                    ? ColorConstants.WHITE
                                    : ColorConstants.GREY_3))
                        .tr(),
                  )),
              InkWell(
                  onTap: () {
                    value.changeFilterType(filterType: FilterType.intractive);
                    // setState(() {
                    // selectedType = 'Scorm';
                    // selectedContentId = null;
                    // isAllSelected = false;

                    // _controller.pause();
                    // });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: value.appliedFilter == FilterType.intractive
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : null,
                        color: value.appliedFilter == FilterType.intractive
                            ? ColorConstants().primaryColor()
                            : ColorConstants.WHITE),
                    child: Text('intractive',
                            style: Styles.regular(
                                size: 14,
                                color:
                                    value.appliedFilter == FilterType.intractive
                                        ? ColorConstants.WHITE
                                        : ColorConstants.GREY_3))
                        .tr(),
                  )),
              InkWell(
                  onTap: () {
                    value.changeFilterType(filterType: FilterType.text);
                    // setState(() {
                    // selectedType = 'Scorm';
                    // selectedContentId = null;
                    // isAllSelected = false;

                    // _controller.pause();
                    // });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: value.appliedFilter == FilterType.text
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : null,
                        color: value.appliedFilter == FilterType.text
                            ? ColorConstants().primaryColor()
                            : ColorConstants.WHITE),
                    child: Text('text',
                            style: Styles.regular(
                                size: 14,
                                color: value.appliedFilter == FilterType.text
                                    ? ColorConstants.WHITE
                                    : ColorConstants.GREY_3))
                        .tr(),
                  ))
            ],
          ),
        ));
  }
}
