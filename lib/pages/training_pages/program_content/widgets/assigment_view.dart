import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/providers/assignment_detail_provider.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_notes.dart';
import 'package:masterg/pages/training_pages/assignment_detail_page.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../utils/Strings.dart';
import 'learning_short_video_player.dart';
import 'learning_shorts_content_player.dart';
import 'dart:ui' as ui;

class AssigmentView extends StatelessWidget {
  final Assignments assigment;
  const AssigmentView({super.key, required this.assigment});

  @override
  Widget build(BuildContext context) {
    String title;

    if (assigment.totalAttempts == 0)
      title = tr('start_assignment');
    else
      title = '${tr('re_submit')} / ${tr('review')}';

    return Container(
      color: ColorConstants.COURSE_BG,
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      height: MediaQuery.of(context).size.height * 0.24,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [

          Row(
            children: [
              Text(
                ' ${tr('submission_date')} : ',
                style: Styles.bold(size: 14, color: ColorConstants.WHITE),
              ),
              Text(
                '${assigment.submissionDate != null ? Utility.convertDateFromMillis(assigment.submissionDate!, Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS) : ''}',
                style: Styles.bold(size: 14, color: ColorConstants.WHITE),
                textDirection: ui.TextDirection.ltr,
              ),
            ],
          ),
          SizedBox(height: 5),

          Row(
            children: [
              Text(
                '${tr('end_date')} : ',
                style: Styles.bold(size: 14, color: ColorConstants.WHITE),
              ),
              Text(
                '${assigment.endDate != null ? Utility.convertDateFromMillis(assigment.endDate!, Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS) : ''}',
                style: Styles.bold(size: 14, color: ColorConstants.WHITE),
                textDirection: ui.TextDirection.ltr,
              ),
            ],
          ),
          SizedBox(height: 15),
          Text('${assigment.title}',
              style: Styles.bold(size: 16, color: ColorConstants.WHITE)),
          SizedBox(height: 8),
          Row(
            children: [
              if (assigment.isGraded == 1) ...[
                Text(
                  '${assigment.overallScore != null ? '${assigment.overallScore}/${assigment.maximumMarks} ${tr('marks')}' : '${assigment.maximumMarks} ${tr('marks')}'} • ${assigment.allowMultiple == 1 ? tr('multiple_attempt') : '1 ${tr('attempt')}'}',
                  style: Styles.semibold(size: 14, color: ColorConstants.WHITE),
                ),
              ] else
                Text(
                  '${tr('non_graded')} • ${assigment.allowMultiple == 1 ? '${tr('multiple_attempt')}' : '1 ${tr('attempt')}'}',
                  style: Styles.semibold(size: 14, color: ColorConstants.WHITE),
                ),
            ],
          ),
          Spacer(),
          InkWell(
            onTap: () {
              Navigator.push(
                  context,
                  NextPageRoute(
                      ChangeNotifierProvider<AssignmentDetailProvider>(
                          create: (c) => AssignmentDetailProvider(
                              TrainingService(ApiService()), assigment,
                              fromCompletiton: true,
                              id: assigment.programContentId),
                          child: AssignmentDetailPage(
                            id: assigment.programContentId,
                          )),
                      isMaintainState: true));
            },
            child: Container(
                width: width(context),
                height: 38,
                margin: const EdgeInsets.symmetric(horizontal: 18, vertical: 4),
                decoration: BoxDecoration(
                    color: ColorConstants().primaryColorAlways(),
                    gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: <Color>[
                          ColorConstants().gradientLeft(),
                          ColorConstants().gradientRight()
                        ]),
                    borderRadius: BorderRadius.circular(8)),
                child: Center(
                  child: Text(
                    '$title',
                    style: Styles.regular(
                        size: 14,
                        color: ColorConstants().primaryForgroundColor()),
                    textAlign: TextAlign.center,
                  ).tr(),
                )),
          )
        ],
      ),
    );
  }
}
