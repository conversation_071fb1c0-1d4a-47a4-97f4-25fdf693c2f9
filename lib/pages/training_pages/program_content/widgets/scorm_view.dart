import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/CommonWebView.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

import '../../../../utils/Strings.dart';

class ScormView extends StatelessWidget {
  final Scorm scrom;
  const ScormView({super.key, required this.scrom});

  @override
  Widget build(BuildContext context) {
    bool isButtonActive = false;
    Color bgColor;
    if (Utility.classStatus(
            scrom.startDate!, scrom.endDate!, currentIndiaTime!) ==
        0) {
      isButtonActive = true;
    } else {
      isButtonActive = false;
    }

    bgColor = !isButtonActive
        ? ColorConstants.GREY_2
        : ColorConstants().primaryColorAlways();
    return Container(
      color: ColorConstants.COURSE_BG,
      height: MediaQuery.of(context).size.height * 0.24,
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${tr('submit_before')} : ',
                style: Styles.bold(size: 14, color: ColorConstants.WHITE),
              ),
              Text(
                '${scrom.endDate != null ? Utility.convertDateFromMillis(scrom.endDate!, Strings.REQUIRED_DATE_HH_MM_AAA_DD_MMM_YYYY) : ''}',
                style: Styles.bold(size: 14, color: ColorConstants.WHITE),
                textDirection: ui.TextDirection.ltr,
              ),
            ],
          ),
          SizedBox(height: 15),
          Text('${scrom.title}',
              style: Styles.bold(size: 16, color: ColorConstants.WHITE)),
          if (scrom.description != null)
            Text('${scrom.description}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: Styles.semibold(size: 14, color: ColorConstants.WHITE)),
          SizedBox(height: 8),
          Spacer(),
          InkWell(
            onTap: () {
              if (isButtonActive) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) {
                      return CommonWebView(url: scrom.url);
                    },
                  ),
                );
              }
            },
            child: Container(
                width: width(context),
                height: 38,
                margin: const EdgeInsets.symmetric(horizontal: 18, vertical: 4),
                decoration: BoxDecoration(
                    color: bgColor,
                    gradient: bgColor == ColorConstants().primaryColorAlways()
                        ? LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: <Color>[
                                ColorConstants().gradientLeft(),
                                ColorConstants().gradientRight()
                              ])
                        : null,
                    borderRadius: BorderRadius.circular(8)),
                child: Center(
                  child: Text(
                    'open_scorm',
                    style: Styles.regular(
                        size: 14,
                        color: bgColor == ColorConstants.GREY_2
                            ? ColorConstants.GREY_3
                            : ColorConstants().primaryForgroundColor()),
                    textAlign: TextAlign.center,
                  ).tr(),
                )),
          )
        ],
      ),
    );
  }
}
