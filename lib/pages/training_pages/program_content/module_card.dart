import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/providers/training_content_provider.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/training_pages/program_content/training_detail_page.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../utils/Strings.dart';
import '../../../utils/utility.dart';

class ModuleCourseCard extends StatefulWidget {
  final Function? sendValue;
  final DateTime now;
  final Function onVideoPause;
  final Function onValueUpdate;
  final TrainingContentProvier trainingContentProvider;

  const ModuleCourseCard({
    super.key,
    this.sendValue,
    required this.now,
    required this.onVideoPause,
    required this.onValueUpdate,
    required this.trainingContentProvider,
  });

  @override
  State<ModuleCourseCard> createState() => _ModuleCourseCardState();
}

class _ModuleCourseCardState extends State<ModuleCourseCard> {
  late BuildContext mContext;
  bool isFirstLoaded = false;
  List<dynamic>? shortContent;

  @override
  void initState() {
    super.initState();
    // filterContent();
  }

  // Future<void> filterContent() async {
  //   print("not filder conent");
  //   shortContent = widget
  //       .trainingContentProvider.trainingModuleResponse.data?.shortContent;

  //   setState(() {
  //     shortContent = shortContent?.where((element) {
  //       // Your existing filtering logic

  //       if (isAllSelected == true) return true;
  //       if (selectedType == 'Classes') {
  //         return element['sorted_content'] == 'sessions';
  //       } else if (selectedType == 'Quiz') {
  //         return element['sorted_content'] == 'assessments';
  //       } else if (selectedType == 'Assignment') {
  //         return element['sorted_content'] == 'assignments';
  //       } else if (selectedType == 'Notes') {
  //         return element['sorted_content'] == 'learning_shots' &&
  //             element['content_type'] == 'notes';
  //       } else if (selectedType == 'Videos') {
  //         return element['sorted_content'] == 'learning_shots' &&
  //             (element['content_type'] == 'video' ||
  //                 element['content_type'] == 'video_yts');
  //       } else if (selectedType == 'Scorm') {
  //         return element['sorted_content'] == 'scorm';
  //       }
  //       return false;
  //     }).toList();
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    try {
      shortContent = widget.trainingContentProvider.sortedContent;
    } catch (e) {
      shortContent = [];
    }
    if (shortContent == null || shortContent?.length == 0) return SizedBox();

    return ListView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      // itemCount: shortContent?.length,
      itemCount: shortContent?.length,
      itemBuilder: (context, index) {
        if (shortContent?[index]['sorted_content'] == 'sessions') {
          dynamic sessionData = Sessions.fromJson(shortContent?[index]);
          return _moduleCard(
              leadingid: Utility.classStatus(
                  int.parse('${sessionData.startDate}'),
                  int.parse(
                    '${sessionData.endDate}',
                  ),
                  widget.now),
              time:
                  ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(sessionData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''} ',
              title: '${sessionData.title}',
              description: sessionData.contentType?.toLowerCase() ==
                      'teamsclass'
                  ? 'teams'
                  : '${sessionData.contentType?.toLowerCase() == 'otherclass' ? 'weblink' : sessionData.contentType?.toLowerCase() == 'liveclass' || sessionData.contentType?.toLowerCase() == 'zoomclass' ? 'Live' : 'Classroom'}',
              type: 'session',
              data: sessionData,
              index: index,
              context: context,
              programContentId: sessionData.programContentId,
              showLiveStatus: Utility.isBetween(
                  int.parse('${sessionData.startDate}'),
                  int.parse('${sessionData.endDate}'),
                  currentIndiaTime!));
        } else if (shortContent?[index]['sorted_content'] == 'assessments') {
          dynamic assessmentData = Assessments.fromJson(shortContent?[index]);

          return _moduleCard(
              leadingid: Utility.classStatus(
                  int.parse('${assessmentData.startDate}'),
                  int.parse(
                    '${assessmentData.endDate}',
                  ),
                  widget.now),
              time:
                  ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(assessmentData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
              title: '${assessmentData.title}',
              description: '${capitalize(assessmentData.contentType)}',
              type: 'assessment',
              data: assessmentData,
              index: index,
              context: context,
              programContentId: assessmentData.programContentId);
        } else if (shortContent?[index]['sorted_content'] == 'assignments') {
          dynamic assignmentData = Assignments.fromJson(shortContent?[index]);

          return _moduleCard(
              leadingid: Utility.classStatus(
                  int.parse('${assignmentData.startDate}'),
                  int.parse('${assignmentData.endDate}'),
                  widget.now),
              time:
                  ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(assignmentData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
              title: '${assignmentData.title}',
              description: '${capitalize(assignmentData.contentType)}',
              type: 'assignment',
              data: assignmentData,
              index: index,
              context: context,
              programContentId: assignmentData.programContentId);
        } else if (shortContent?[index]['sorted_content'] == 'learning_shots') {
          dynamic learningShorts = LearningShots.fromJson(shortContent?[index]);

          return _moduleCard(
              leadingid: learningShorts.completion == 100.0
                  ? 2
                  : learningShorts.completion != 0.0
                      ? 3
                      : 1,
              time:
                  ' ${Utility().isRTL(context) ? '' : '•'} ${learningShorts.contentType == 'notes' ? learningShorts.noPages : learningShorts.durationInMinutes} ${learningShorts.contentType == 'notes' ? tr('page') : learningShorts.durationInMinutes == 0 || learningShorts.durationInMinutes == 1 ? tr('min') : tr('mins')} ${Utility().isRTL(context) ? '•' : ''}',
              // time:
              //     ' ${Utility().isRTL(context) ? '' : '•'} ${learningShorts.durationInMinutes} ${learningShorts.durationInMinutes == 1 ? tr('min') : tr('mins')} ${Utility().isRTL(context) ? '•' : ''}',
              title: '${learningShorts.title}',
              description: '${capitalize(learningShorts.contentType)}',
              type: 'learningShots',
              data: learningShorts,
              index: index,
              context: context,
              programContentId: learningShorts.programContentId);
        } else if (shortContent?[index]['sorted_content'] == 'scorm') {
          dynamic scormContent = Scorm.fromJson(shortContent?[index]);

          return _moduleCard(
              leadingid: Utility.classStatus(
                  int.parse('${scormContent.startDate}'),
                  int.parse('${scormContent.endDate}'),
                  widget.now),
              time:
                  ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(scormContent.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
              title: '${scormContent.title}',
              description: '${capitalize(scormContent.contentType)}',
              type: 'scorm',
              data: scormContent,
              index: index,
              context: context,
              programContentId: scormContent.programContentId);
        }

        return SizedBox();
      },
    );
  }

  void handleClick(data, type, title, programContentId) {
    // print("chanage url before ${data.url}");
    // data.url = data.url.toString().replaceFirst(
    //     'https://mesclearn-media.s3.me-central-1.amazonaws.com',
    //     'https://di882be5vsgg3.cloudfront.net');
    // print("chanage url after ${data.url}");
    try {
      if (type == 'learningShots') {
        if (data.contentType == "notes") {
          // selectedType = 'Notes';
          widget.onVideoPause();
          // _controller.pause();
          widget.onVideoPause();
          widget.sendValue!(
              VideoPlayerController.networkUrl(Uri.parse('${data.url}')),
              title,
              true,
              '${data.url}',
              '${data.image}',
              data);
        } else {
          opacityLevel = 0.0;
          String videoUrl = data.url;
          // selectedType = 'Videos';

          setState(() {
            showLoading = true;
          });
          final controller = data.contentType.toLowerCase() != 'video_yts'
              ? VideoPlayerController.networkUrl(Uri.parse('$videoUrl'))
              : YoutubePlayerController(
                  initialVideoId: YoutubePlayer.convertUrlToId('$videoUrl')!,
                  flags: YoutubePlayerFlags(
                    autoPlay: true,
                    mute: false,
                  ),
                );

          // _controller.pause();
          widget.onVideoPause();
          widget.sendValue!(
            controller,
            title,
            false,
            '',
            '',
            data,
            isYoutubeController: data.contentType.toLowerCase() == 'video_yts',
          );

          //video loader still loading even video is played
          // await Future.delayed(Duration(seconds: 1), () {
          //   setState(() {
          //     showLoading = false;
          //   });
          // });
        }
      } else if (type == 'assessment') {
        // selectedType = 'Quiz';
        // _controller.pause();
        widget.onVideoPause();
        widget.sendValue!(
            VideoPlayerController.networkUrl(Uri.parse('${data.url}')),
            title,
            true,
            '${data.url}',
            'https://qa.learningoxygen.com/images/programs/2095.jpeg',
            data);
      } else if (type == 'assignment') {
        // selectedType = 'Assignment';
        // _controller.pause();
        widget.onVideoPause();
        widget.sendValue!(
            VideoPlayerController.networkUrl(Uri.parse('${data.url}')),
            title,
            true,
            '${data.url}',
            'https://qa.learningoxygen.com/images/programs/2095.jpeg',
            data);
      } else if (type == 'session') {
        // selectedType = 'Classes';
        // _controller.pause();
        widget.onVideoPause();
        widget.sendValue!(
          VideoPlayerController.networkUrl(Uri.parse('${data.url}')),
          title,
          true,
          '${data.url}',
          '${data.image}',
          data,
        );
      } else if (type == 'scorm') {
        // selectedType = 'Scorm';
        Scorm scorm = data;
        // _controller.pause();
        widget.onVideoPause();
        widget.sendValue!(
          VideoPlayerController.networkUrl(Uri.parse('${scorm.url}')),
          title,
          true,
          '${scorm.url}',
          '',
          scorm,
        );
      }
    } catch (e, stackTrace) {
      print('type value  is $stackTrace ');
    }

    setState(() {
      selectedContentId = programContentId;
    });
    widget.onValueUpdate();
  }

  Widget _moduleCard(
      {required String time,
      required String title,
      required String description,
      required String type,
      required dynamic data,
      required int index,
      required context,
      int? programContentId,
      int leadingid = 1,
      bool showNotificationIcon = false,
      bool showLiveStatus = false}) {
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (index == 0 && selectedContentId == null) {
    //     Future.delayed(Duration(milliseconds: 400)).then((value) {
    //       handleClick(data, type, title, programContentId);
    //     });
    //   }
    // });
    return InkWell(
      onTap: () async {
        handleClick(data, type, title, programContentId);
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
            color: ui.Color.fromARGB(255, 255, 255, 255),
            borderRadius: BorderRadius.circular(12),
            border: programContentId == selectedContentId
                ? Border.all(width: 0.6)
                : null),
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            leadingid == 0
                ? SvgPicture.asset(
                    'assets/images/live_icon.svg',
                    height: 20.0,
                    width: 20.0,
                    allowDrawingOutsideViewBox: true,
                  )
                : leadingid == 1
                    ? SvgPicture.asset(
                        'assets/images/empty_circle.svg',
                        height: 20.0,
                        width: 20.0,
                        allowDrawingOutsideViewBox: true,
                      )
                    : leadingid == 2
                        ? Icon(
                            Icons.check_circle,
                            color: ColorConstants.GREEN,
                            size: 20.0,
                          )
                        : leadingid == 3
                            ? SvgPicture.asset(
                                'assets/images/ongoing_icon.svg',
                                height: 20.0,
                                width: 20.0,
                                allowDrawingOutsideViewBox: true,
                              )
                            : SvgPicture.asset(
                                'assets/images/pending_icon.svg',
                                height: 20.0,
                                width: 20.0,
                                allowDrawingOutsideViewBox: true,
                              ),
            SizedBox(
              width: 20,
            ),
            Container(
              width: MediaQuery.of(context).size.width * (0.8 - 0.1),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: showLiveStatus == true
                            ? MediaQuery.of(context).size.width * (0.6 - 0.1)
                            : MediaQuery.of(context).size.width * (0.8 - 0.1),
                        child: Text(
                          '$title',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          style: Styles.semibold(size: 16),
                        ),
                      ),
                      if (showLiveStatus)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SvgPicture.asset('assets/images/live_icon.svg',
                                height: 15.0,
                                width: 15.0,
                                allowDrawingOutsideViewBox: true),
                            Text(
                              // data!.sessions!.contentType ==
                              //         'teamsclass'
                              //     ? '${tr('teams')}'
                              //     :
                              data.contentType == 'liveclass' ||
                                      data!.sessions!
                                              .elementAt(index)
                                              .contentType ==
                                          'zoomclass'
                                  ? '${tr('Live_now')}'
                                  : tr('ongoing'),
                              style: Styles.regular(
                                  size: 12, color: ColorConstants.RED),
                            )
                          ],
                        )
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        '${description.toLowerCase() == 'video_yts' ? tr('video') : tr('$description'.toLowerCase())}',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.regular(
                          size: 14,
                        ),
                      ),
                      // SizedBox(width: 5),
                      Text(
                        '$time',
                        textDirection: ui.TextDirection.ltr,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.regular(
                          size: 14,
                        ),
                      ),
                    ],
                  ),
                  if (type == 'learningShots')
                    Container(
                      height: 10,
                      width: MediaQuery.of(context).size.width * (0.8 - 0.1),
                      margin: EdgeInsets.only(top: 5),
                      decoration: BoxDecoration(
                          color: ColorConstants.GREY,
                          borderRadius: BorderRadius.circular(10)),
                      child: Stack(
                        children: [
                          Container(
                            height: 10,
                            width: MediaQuery.of(context).size.width *
                                (0.8 - 0.1) *
                                data.completion /
                                100,
                            decoration: BoxDecoration(
                                color: Color(0xffFFB72F),
                                borderRadius: BorderRadius.circular(10)),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            Expanded(
              child: SizedBox(),
            ),
            if (showNotificationIcon)
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorConstants.ICON_BG_GREY,
                  shape: BoxShape.circle,
                ),
                child: Container(
                  child: SvgPicture.asset(
                    'assets/images/notification_icon.svg',
                    height: 20.0,
                    width: 20.0,
                    allowDrawingOutsideViewBox: true,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void selectFirstContentOnLoad(shortContent) {
    print("value sis ${shortContent.length}");
    if (selectedContentId != null && shortContent.length == 0) return;
    print("value sis ${shortContent.length} return ");
    if (shortContent[0]['sorted_content'] == 'sessions') {
      dynamic sessionData = Sessions.fromJson(shortContent[0]);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (selectedContentId == null) {
          // setState(() {
          selectedContentId = sessionData.programContentId;
          // });
          widget.onVideoPause();
          String? videoUrl = sessionData.url;
          dynamic controller =
              VideoPlayerController.networkUrl(Uri.parse('$videoUrl'));
          widget.sendValue!(
            controller,
            '${sessionData.title}',
            true,
            '${sessionData.url}',
            '${sessionData.image}',
            sessionData,
            isYoutubeController: true,
          );
        }
      });
    } else if (shortContent[0]['sorted_content'] == 'assessments') {
      dynamic assessmentData = Assessments.fromJson(shortContent[0]);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (selectedContentId == null) {
          widget.onVideoPause();

          widget.onVideoPause();
          widget.sendValue!(
            VideoPlayerController.networkUrl(
                Uri.parse('${assessmentData.url}')),
            '${assessmentData.title}',
            true,
            '${assessmentData.url}',
            '',
            assessmentData,
          );

          // setState(() {
          selectedContentId = assessmentData.programContentId;
          // });
        }
      });
    } else if (shortContent[0]['sorted_content'] == 'assignments') {
      dynamic assignmentData = Assignments.fromJson(shortContent[0]);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // setState(() {
        if (selectedContentId == null) {
          selectedContentId = assignmentData.programContentId;
          widget.onVideoPause();
          // _controller.pause();
          widget.onVideoPause();
          widget.sendValue!(
              VideoPlayerController.networkUrl(
                  Uri.parse('${assignmentData.url}')),
              '${assignmentData.title}',
              true,
              '${assignmentData.url}',
              'https://qa.learningoxygen.com/images/programs/2095.jpeg',
              assignmentData);
        }
        // });
      });
    } else if (shortContent[0]['sorted_content'] == 'learning_shots') {
      dynamic learningShorts = LearningShots.fromJson(shortContent[0]);

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (selectedContentId == null) {
          // setState(() {
          if (learningShorts.contentType != "notes") {
            opacityLevel = 0.0;

            selectedContentId = learningShorts.programContentId;

            if (learningShorts.contentType.toLowerCase() == 'video_yts') {
              widget.onVideoPause();
            }

            final controller = learningShorts.contentType.toLowerCase() ==
                    'video_yts'
                ? YoutubePlayerController(
                    initialVideoId:
                        YoutubePlayer.convertUrlToId('${learningShorts.url}')!,
                    flags: YoutubePlayerFlags(
                      autoPlay: true,
                      mute: false,
                    ),
                  )
                : VideoPlayerController.networkUrl(Uri.parse(
                    '${learningShorts.url}',
                  ));

            widget.sendValue!(
              controller,
              '${learningShorts.title}',
              false,
              '',
              '',
              null,
              isYoutubeController:
                  learningShorts.contentType.toLowerCase() == 'video_yts',
            );
          } else {
            selectedContentId = learningShorts.programContentId;

            widget.onVideoPause();
            widget.sendValue!(
                VideoPlayerController.networkUrl(
                    Uri.parse('${learningShorts.url}')),
                '${learningShorts.title}',
                true,
                '${learningShorts.url}',
                '${learningShorts.image}',
                learningShorts);
          }
          // });
        }
      });
    } else if (shortContent[0]['sorted_content'] == 'scorm') {
      dynamic scormContent = Scorm.fromJson(shortContent[0]);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // setState(() {
        if (selectedContentId == null) {
          selectedContentId = scormContent.programContentId;

          widget.onVideoPause();
          widget.sendValue!(
              VideoPlayerController.networkUrl(
                  Uri.parse('${scormContent.url}')),
              '${scormContent.title}',
              true,
              '${scormContent.url}',
              'https://qa.learningoxygen.com/images/programs/2095.jpeg',
              scormContent);
        }
        // });
      });
    }
    print("selecte first content start end $selectedContentId");
  }

  String capitalize(String? letter) {
    return "${letter![0].toUpperCase()}${letter.substring(1).toLowerCase()}";
  }
}
