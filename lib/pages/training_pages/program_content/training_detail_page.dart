import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/providers/training_content_provider.dart';
import 'package:masterg/data/providers/training_detail_provider.dart';
import 'package:masterg/pages/training_pages/program_content/training_modules.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:provider/provider.dart';

import '../../../utils/config.dart';
// import 'dart:html';

double opacityLevel = 1.0;
int? selectedContentId;
dynamic selectedData;
String selectedType = '';

bool showLoading = false;

class TrainingDetailPage extends StatefulWidget {
  @override
  State<TrainingDetailPage> createState() => _TrainingDetailPageState();
}

class _TrainingDetailPageState extends State<TrainingDetailPage> {
  late BuildContext mContext;

  bool _showMessage = false; // To control what to display


  @override
  void initState() {
    super.initState();
    Timer(Duration(seconds: 15), () {
      setState(() {
        _showMessage = true; // Show the message after 5 seconds
      });
    });
  }


  @override
  Widget build(BuildContext context) {
    mContext = context;

    return OrientationBuilder(
        builder: (BuildContext context, Orientation orientation) {
      if (kIsWeb) orientation = Orientation.portrait;
      return Consumer<TrainingDetailProvider>(
          builder: (context, traininDetailProvider, child) {
            if(traininDetailProvider.modules?.length != 0){
              _showMessage = true;
            }

        return Scaffold(
          key: traininDetailProvider.scaffoldKey,
          resizeToAvoidBottomInset: false,
          backgroundColor: ColorConstants.BG_GREY,
          appBar: orientation == Orientation.portrait
              ? AppBar(
                  toolbarHeight: orientation == Orientation.portrait ? null : 0,
                  title: orientation == Orientation.portrait
                      ? Text(
                          traininDetailProvider.program!.name ??
                              '${tr(APK_DETAILS["package_name"] == "com.singulariswow.mec" ? 'course_module' : 'my_course')}',
                          style: Styles.bold(size: 18))
                      : null,
                  centerTitle: false,
                  backgroundColor: orientation == Orientation.portrait
                      ? Colors.white
                      : Colors.transparent,
                  elevation: 0.0,
                  leading: IconButton(
                    icon: Icon(
                      Icons.arrow_back,
                      color: Colors.black,
                    ),
                    onPressed: () {
                      selectedData = null;
                      selectedContentId = null;

                      Navigator.pop(context);
                    },
                  ),
                )
              : null,
          body: traininDetailProvider.modules?.length == 0
              ? Center(
                  child: _showMessage == false ? CircularProgressIndicator():
                  APK_DETAILS["package_name"] == "com.singulariswow.mec" ? Text('no_module_available').tr() : Text('no_program_available').tr()
                )
              : TrainingModules(
                  module: traininDetailProvider.modules,
                ),
        );
      });
    });
  }
}