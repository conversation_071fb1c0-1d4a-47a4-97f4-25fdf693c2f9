// import 'dart:convert';
// import 'dart:math';

// import 'package:easy_localization/easy_localization.dart';
// import 'package:expandable/expandable.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:masterg/blocs/bloc_manager.dart';
// import 'package:masterg/blocs/home_bloc.dart';
// import 'package:masterg/data/api/api_service.dart';
// import 'package:masterg/data/models/response/home_response/assignment_detail_response.dart';
// import 'package:masterg/data/models/response/home_response/training_module_response.dart';
// import 'package:masterg/data/providers/assessment_detail_provider.dart';
// import 'package:masterg/data/providers/assignment_detail_provider.dart';
// import 'package:masterg/data/providers/my_course_provider.dart';
// import 'package:masterg/data/providers/training_content_provider.dart';
// import 'package:masterg/data/providers/training_detail_provider.dart';
// import 'package:masterg/main.dart';
// import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
// import 'package:masterg/pages/custom_pages/certificate_container.dart';
// import 'package:masterg/pages/custom_pages/custom_widgets/CommonWebView.dart';
// import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
// import 'package:masterg/pages/singularis/competition/competition_navigation/competition_notes.dart';
// import 'package:masterg/pages/training_pages/assessment_page.dart';
// import 'package:masterg/pages/training_pages/assignment_detail_page.dart';
// import 'package:masterg/pages/training_pages/program_content/module_card.dart';
// import 'package:masterg/pages/training_pages/training_service.dart';
// import 'package:masterg/utils/Log.dart';
// import 'package:masterg/utils/Styles.dart';
// import 'package:masterg/utils/constant.dart';
// import 'package:masterg/utils/resource/colors.dart';
// import 'package:page_transition/page_transition.dart';
// import 'package:provider/provider.dart';
// import 'package:smooth_video_progress/smooth_video_progress.dart';
// import 'package:url_launcher/url_launcher.dart';
// import 'package:video_player/video_player.dart';
// import 'package:visibility_detector/visibility_detector.dart';
// import 'package:youtube_player_flutter/youtube_player_flutter.dart';
// import 'dart:math' as math;
// import 'dart:ui' as ui;
// import '../../../data/models/response/home_response/training_detail_response.dart';
// import '../../../utils/Strings.dart';
// import '../../../utils/config.dart';
// import '../../../utils/utility.dart';
// // import 'dart:html';

// double opacityLevel = 1.0;
// int? selectedContentId;
// dynamic selectedData;
// String selectedType = '';

// bool showLoading = false;

// class TrainingDetailPage extends StatefulWidget {
//   @override
//   State<TrainingDetailPage> createState() => _TrainingDetailPageState();
// }

// class _TrainingDetailPageState extends State<TrainingDetailPage> {
//   late BuildContext mContext;
//   late String selectedItemName = '';
//   bool isNoteView = false;
//   String noteUrl = '';
//   String noteImgUrl = '';

//   Function? onClickRoute;
//   bool isAllSelected = true;

//   double popupHeight = 300;
//   bool? isYoutubeView;

//   //set first module default open and close
//   List<ExpandableController> _expandableController = [
//     ExpandableController(initialExpanded: false)
//   ];
//   bool isJoinClassLoading = false;
//   int currentPer = 0;
//   TrainingContentProvier? currentTrainingProvider;

//   bool moduleOnceOpened = false;
//   int currentMin = 0, prevMin = 0;

//   late VideoPlayerController _controller;
//   late YoutubePlayerController _ytController;

//   String? currentZoomUrl;
//   String? currentOpenUrl;

//   @override
//   void initState() {
//     super.initState();

//     _controller = VideoPlayerController.networkUrl(Uri.parse(''))
//       ..initialize().then((_) {});
//     _ytController = YoutubePlayerController(initialVideoId: '');
//   }

//   @override
//   Widget build(BuildContext context) {
//     mContext = context;

//     return OrientationBuilder(
//         builder: (BuildContext context, Orientation orientation) {
//       if (kIsWeb) orientation = Orientation.portrait;
//       return Consumer<TrainingDetailProvider>(
//           builder: (context, traininDetailProvider, child) {
//         if (_expandableController.length == 1) {
//           _expandableController.addAll(List.generate(
//               max(0, traininDetailProvider.modules!.length - 1),
//               (index) => new ExpandableController(initialExpanded: false)));
//         }

// // isJoinClassLoading ||
// //                   (selectedData != null &&
// //                       (selectedData is LearningShots &&
// //                               ((selectedData as LearningShots)
// //                                           .contentType
// //                                           ?.toLowerCase() ==
// //                                       'video' &&
// //                                   _controller.value.isInitialized == false) ||
// //                           (selectedData is LearningShots &&
// //                               (selectedData as LearningShots)
// //                                       .contentType
// //                                       ?.toLowerCase() ==
// //                                   'video_yts' &&
// //                               _ytController.value.isReady == false))),

//         bool loadingValue = isJoinClassLoading;

//         if (selectedData != null && selectedData is LearningShots) {
//           if (selectedData.contentType?.toString().toLowerCase() == 'video' &&
//               _controller.value.isInitialized == false) {
//             loadingValue = true;
//           }
//           // if (selectedData.contentType?.toString().toLowerCase() ==
//           //         'video_yts' &&
//           //     _ytController.value.isReady == false) {
//           //   loadingValue = true;
//           // }
//         }

//         return Scaffold(
//             key: traininDetailProvider.scaffoldKey,
//             resizeToAvoidBottomInset: false,
//             backgroundColor: ColorConstants.BG_GREY,
//             appBar: orientation == Orientation.portrait
//                 ? AppBar(
//                     toolbarHeight:
//                         orientation == Orientation.portrait ? null : 0,
//                     title: orientation == Orientation.portrait
//                         ? Text(
//                             traininDetailProvider.program!.name ??
//                                 '${tr(APK_DETAILS["package_name"] == "com.singulariswow.mec" ? 'course_module' : 'my_course')}',
//                             style: Styles.bold(size: 18))
//                         : null,
//                     centerTitle: false,
//                     backgroundColor: orientation == Orientation.portrait
//                         ? Colors.white
//                         : Colors.transparent,
//                     elevation: 0.0,
//                     leading: IconButton(
//                       icon: Icon(
//                         Icons.arrow_back,
//                         color: Colors.black,
//                       ),
//                       onPressed: () {
//                         selectedData = null;
//                         selectedContentId = null;
//                         _controller.pause();

//                         Navigator.pop(context);
//                       },
//                     ),
//                   )
//                 : null,
//             body: ScreenWithLoader(
//               isLoading: loadingValue,
//               body: IgnorePointer(
//                 ignoring: loadingValue,
//                 child: Stack(
//                   children: [
//                     BlocManager(
//                       initState: (context) {},
//                       child: BlocListener<HomeBloc, HomeState>(
//                         listener: (context, state) async {
//                           if (state is ZoomOpenUrlState)
//                             handleOpenUrlState(state);
//                         },
//                         child: MultiProvider(
//                           providers: [
//                             ChangeNotifierProvider<MyCourseProvider>(
//                               create: (context) =>
//                                   MyCourseProvider(_controller),
//                             ),
//                           ],
//                           child: traininDetailProvider.apiStatus ==
//                                   ApiStatus.LOADING
//                               ? Center(
//                                   child: CircularProgressIndicator(),
//                                 )
//                               : Container(
//                                   margin: EdgeInsets.only(
//                                     bottom: traininDetailProvider
//                                             .certificateAvailable
//                                         ? height(context) * 0.07
//                                         : 0,
//                                   ),
//                                   child: _content(traininDetailProvider,
//                                       context, orientation, currentIndiaTime!),
//                                 ),
//                         ),
//                       ),
//                     ),
//                     if (traininDetailProvider.certificateAvailable &&
//                         orientation == Orientation.portrait)
//                       Positioned(
//                         bottom: 0,
//                         left: 0,
//                         right: 0,
//                         child: InkWell(
//                           onTap: () {},
//                           child: CertificateContainer(),
//                         ),
//                       ),
//                     if (selectedContentId == null && !moduleOnceOpened)
//                       Container(
//                           height: double.infinity,
//                           width: double.infinity,
//                           color: ColorConstants.WHITE,
//                           child: Center(child: CircularProgressIndicator())),
//                   ],
//                 ),
//               ),
//             ));
//       });
//     });
//   }

//   void handleOpenUrlState(ZoomOpenUrlState state) {
//     switch (state.apiState) {
//       case ApiStatus.LOADING:
//         Log.v("Zoom Open Url Loading....................");
//         isJoinClassLoading = true;
//         setState(() {});
//         break;
//       case ApiStatus.SUCCESS:
//         Log.v(
//             "Zoom Open Url Success.................... check ${state.response?.status}");
//         isJoinClassLoading = false;
//         setState(() {});
//         if (currentZoomUrl != null) return;

//         if (state.response?.status == 0) {
//           if (currentOpenUrl != null)
//             launchUrl(Uri.parse('$currentOpenUrl'),
//                 mode: LaunchMode.externalApplication);
//           else if (currentZoomUrl != null)
//             launchUrl(Uri.parse('$currentZoomUrl'),
//                 mode: LaunchMode.externalApplication);
//         } else if (state.response?.data?.list?.joinUrl != null)
//           launchUrl(Uri.parse('${state.response?.data?.list?.joinUrl}'),
//               mode: LaunchMode.externalApplication);
//         else if (currentOpenUrl != null)
//           launchUrl(Uri.parse('$currentOpenUrl'),
//               mode: LaunchMode.externalApplication);

//         break;

//       case ApiStatus.ERROR:
//         isJoinClassLoading = false;
//         setState(() {});
//         Log.v("Zoom open url Error..........................");
//         break;
//       case ApiStatus.INITIAL:
//         break;
//     }
//   }

//   Future<void> openAssignment(dynamic data) async {
//     await Navigator.push(
//       context,
//       NextPageRoute(
//           ChangeNotifierProvider<AssignmentDetailProvider>(
//               create: (c) => AssignmentDetailProvider(
//                   TrainingService(ApiService()), data,
//                   fromCompletiton: true, id: data.programContentId),
//               child: AssignmentDetailPage(
//                 id: data.programContentId,
//               )),
//           isMaintainState: true),
//     ).then((value) {
//       currentTrainingProvider?.getTraningDetail();
//       currentTrainingProvider?.addListener(() {
//         setState(() {
//           selectedData = currentTrainingProvider
//               ?.trainingModuleResponse.data?.module![0].content?.assignments
//               ?.where((element) =>
//                   element.programContentId == selectedData.programContentId)
//               .first;
//         });
//       });
//     });
//     return null;
//   }

//   Future<void> openAssessment(dynamic data, String programName,
//       bool isCertified, int? programId) async {
//     await Navigator.push(
//             context,
//             NextPageRoute(
//                 ChangeNotifierProvider<AssessmentDetailProvider>(
//                     create: (context) => AssessmentDetailProvider(
//                         TrainingService(ApiService()), data),
//                     child: AssessmentDetailPage(
//                         isCertified: isCertified,
//                         programName: programName,
//                         programId: programId)),
//                 isMaintainState: true))
//         .then((value) {
//       currentTrainingProvider?.getTraningDetail();
//       currentTrainingProvider?.addListener(() {
//         setState(() {
//           selectedData = currentTrainingProvider
//               ?.trainingModuleResponse.data?.module![0].content?.assessments
//               ?.where((element) =>
//                   element.programContentId == selectedData.programContentId)
//               .first;
//         });
//       });
//     });
//   }

//   void listenVideoChanges(_controller) {
//     _controller.addListener(() {
//       currentMin = _controller.value.position.inMinutes;
//       if (currentMin != 0 && prevMin != currentMin) {
//         prevMin = currentMin;
//         _updateCourseCompletion(currentMin, 0);
//       }
//     });
//   }

//   void _updateCourseCompletion(bookmark, int completionPer) async {
//     //change bookmark with 25
//     BlocProvider.of<HomeBloc>(context).add(UpdateVideoCompletionEvent(
//         bookmark: bookmark,
//         contentId: selectedContentId,
//         completionPercent: completionPer));
//     setState(() {});
//   }

//   bool containsContent(Modules? module, String selectedType) {
//     if (module == null) {
//       return false;
//     }

//     // Extract counts from the module
//     int? note = module.note;
//     int? video = module.video;
//     int? sessions = module.sessions;
//     int? assignments = module.assignments;
//     int? assessments = module.assessments;
//     int? scorms = module.scorms;

//     // Use switch statement to check for the selected content type
//     switch (selectedType) {
//       case 'Classes':
//         return sessions != null && sessions > 0;

//       case 'Videos':
//         return video != null && video > 0;

//       case 'Notes':
//         return note != null && note > 0;

//       case 'Assignment':
//         return assignments != null && assignments > 0;

//       case 'Quiz':
//         return assessments != null && assessments > 0;

//       case 'Scorm':
//         return scorms != null && scorms > 0;

//       default:
//         return false;
//     }
//   }

//   @override
//   void dispose() {
//     selectedData = null;
//     selectedContentId = null;

//     try {
//       _controller.pause();
//       _controller.dispose();
//       _ytController.pause();
//       _ytController.dispose();
//     } catch (e, stackTrace) {
//       Log.v("stacktrace $stackTrace");
//     }
//     SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
//     super.dispose();
//   }

//   Widget _content(TrainingDetailProvider trainingDetailProvider, context,
//       Orientation orientation, DateTime now) {
//     String title = '';
//     String trainerName = '';
//     bool isButtonActive = true;
//     // if (selectedData == null) return SizedBox();

//     try {
//       if (selectedData is Assignments && selectedContentId != null) {
//         if (selectedData?.totalAttempts == 0)
//           title = tr('start_assignment');
//         else
//           title = '${tr('re_submit')} / ${tr('review')}';
//       } else if (selectedData is Sessions && selectedContentId != null) {
//         try {
//           selectedData = Sessions.fromJson(selectedData);
//         } catch (e) {}
//         trainerName = selectedData?.trainerName;

//         if (selectedData?.liveclassAction.toString().toLowerCase() ==
//                 'concluded' ||
//             selectedData?.liveclassStatus.toString().toLowerCase() ==
//                 'recorded') {
//           title = tr('concluded');
//           isButtonActive = false;
//         }

//         if (selectedData?.liveclassAction.toString().toLowerCase() == 'live' ||
//             selectedData?.liveclassAction.toString().toLowerCase() ==
//                 'join class') {
//           if (Utility.classStatus(
//                   selectedData?.startDate!, selectedData?.endDate!, now) ==
//               2) {
//             isButtonActive = false;
//           } else {
//             isButtonActive = true;
//           }

//           title = '${tr('join_now')}';
//         }
//         if (selectedData?.contentType.toString().toLowerCase() ==
//             'offlineclass') {
//           title = tr('in_progress');
//           isButtonActive = false;
//         }
//         if (selectedData?.liveclassAction.toString().toLowerCase() ==
//             'scheduled') {
//           title = tr('scheduled');
//           isButtonActive = false;
//         }
//       } else if (selectedData is Assessments && selectedContentId != null) {
//         title = tr('start_assessment');
//       } else if (selectedData is LearningShots && selectedContentId != null) {
//         if ((selectedData as LearningShots).contentType?.toLowerCase() ==
//             'notes') {
//           title = '${tr('view_note')}';
//         } else {
//           title = tr('start_video');
//         }
//       } else if (selectedData is Assessments && selectedContentId != null) {
//         if (selectedData?.status == 'Active') {
//           if (selectedData?.attemptsRemaining == selectedData?.attemptAllowed) {
//             title = tr('attempt');
//           } else {
//             title = '${tr('reattempt')} / ${tr('review')}';
//           }
//         }
//       } else if (selectedData is Scorm && selectedContentId != null) {
//         title = '${tr('open_scorm')}';
//         if (Utility.classStatus(
//                 selectedData?.startDate!, selectedData?.endDate!, now) ==
//             0) {
//           isButtonActive = true;
//         } else {
//           isButtonActive = false;
//         }
//       }
//     } catch (e) {
//       return SizedBox(
//         child: Text('$e and $selectedType'),
//       );
//     }

//     // try {
//     //   if (selectedType == 'Assignment' && selectedContentId != null) {
//     //     if (selectedData?.totalAttempts == 0)
//     //       title = tr('start_assignment');
//     //     else
//     //       title = '${tr('re_submit')} / ${tr('review')}';
//     //   } else if (selectedType == 'Classes' && selectedContentId != null) {
//     //     try {
//     //       selectedData = Sessions.fromJson(selectedData);
//     //     } catch (e) {}
//     //     trainerName = selectedData?.trainerName;

//     //     if (selectedData?.liveclassAction.toString().toLowerCase() ==
//     //             'concluded' ||
//     //         selectedData?.liveclassStatus.toString().toLowerCase() ==
//     //             'recorded') {
//     //       title = tr('concluded');
//     //       isButtonActive = false;
//     //     }

//     //     if (selectedData?.liveclassAction.toString().toLowerCase() == 'live' ||
//     //         selectedData?.liveclassAction.toString().toLowerCase() ==
//     //             'join class') {
//     //       if (Utility.classStatus(
//     //               selectedData?.startDate!, selectedData?.endDate!, now) ==
//     //           2) {
//     //         isButtonActive = false;
//     //       } else {
//     //         isButtonActive = true;
//     //       }

//     //       title = '${tr('join_now')}';
//     //     }
//     //     if (selectedData?.contentType.toString().toLowerCase() ==
//     //         'offlineclass') {
//     //       title = tr('in_progress');
//     //       isButtonActive = false;
//     //     }
//     //     if (selectedData?.liveclassAction.toString().toLowerCase() ==
//     //         'scheduled') {
//     //       title = tr('scheduled');
//     //       isButtonActive = false;
//     //     }
//     //   } else if (selectedType == 'Assessments' && selectedContentId != null) {
//     //     title = tr('start_assessment');
//     //   } else if (selectedType == 'Notes' && selectedContentId != null) {
//     //     title = '${tr('view_note')}';
//     //   } else if (selectedType == 'Videos' && selectedContentId != null) {
//     //     title = tr('start_video');
//     //   } else if (selectedType == 'Quiz' && selectedContentId != null) {
//     //     if (selectedData?.status == 'Active') {
//     //       if (selectedData?.attemptsRemaining == selectedData?.attemptAllowed) {
//     //         title = tr('attempt');
//     //       } else {
//     //         title = '${tr('reattempt')} / ${tr('review')}';
//     //       }
//     //     }
//     //   } else if (selectedType == 'Scorm' && selectedContentId != null) {
//     //     title = '${tr('open_scorm')}';
//     //     if (Utility.classStatus(
//     //             selectedData?.startDate!, selectedData?.endDate!, now) ==
//     //         0) {
//     //       isButtonActive = true;
//     //     } else {
//     //       isButtonActive = false;
//     //     }
//     //   }
//     // } catch (e) {
//     //   // return SizedBox(
//     //   //   child: Text('$e and $selectedType'),
//     //   // );
//     // }
//     Color? bgColor = !isButtonActive
//         ? ColorConstants.GREY_2
//         : selectedType == 'Quiz'
//             ? ColorConstants().primaryColorAlways()
//             : selectedType == 'Quiz'
//                 ? selectedData?.liveclassAction.toString().toLowerCase() ==
//                         'scheduled'
//                     ? ColorConstants.GREY_2
//                     : ColorConstants().primaryColorAlways()
//                 : selectedType == 'Assignment' && !isButtonActive
//                     ? ColorConstants.GREY_2
//                     : ColorConstants().primaryColorAlways();
//     // bgColor = ColorConstants().primaryColorAlways();
//     if (kIsWeb) {
//       // ignore: undefined_prefixed_name
//       // ui.platformViewRegistry.registerViewFactory(
//       //     'hello-world-html',
//       //     (int viewId) => IFrameElement()
//       //       ..src =
//       //           'https://www.youtube.com/embed/${_ytController.initialVideoId}'
//       //       ..style.border = 'none');
//     }
//     return Column(
//       children: [
//         selectedContentId != null
//             //  && showLoading == false
//             ? Column(
//                 children: [
//                   Container(
//                     width: MediaQuery.of(context).size.width,
//                     height: kIsWeb && MediaQuery.of(context).size.width > 500
//                         ? MediaQuery.of(context).size.height * 0.5
//                         : orientation == Orientation.landscape
//                             ? MediaQuery.of(context).size.height
//                             : min(height(context) * 0.3,
//                                 width(context) / _controller.value.aspectRatio),
//                     child: ClipRRect(
//                       borderRadius: BorderRadius.circular(0),
//                       child: AspectRatio(
//                         aspectRatio: !isNoteView
//                             ? _controller.value.aspectRatio
//                             : 16 / 9,
//                         child: Stack(
//                           children: [
//                             Hero(
//                               tag: 'videoPlayer',
//                               child: GestureDetector(
//                                   onTap: () {
//                                     setState(() {
//                                       opacityLevel =
//                                           opacityLevel == 1.0 ? 0.0 : 1.0;
//                                     });
//                                   },
//                                   child: !isNoteView
//                                       ? isYoutubeView == true
//                                           ? VisibilityDetector(
//                                               key: Key("urlVideoPlayer"),
//                                               onVisibilityChanged:
//                                                   (VisibilityInfo info) {
//                                                 if (info.visibleFraction ==
//                                                     1.0) {
//                                                   currentMin = 0;
//                                                   prevMin = 0;
//                                                   listenVideoChanges(
//                                                       _ytController);
//                                                 }
//                                               },
//                                               child: kIsWeb
//                                                   ? HtmlElementView(
//                                                       viewType:
//                                                           'hello-world-html')
//                                                   : YoutubePlayer(
//                                                       controller: _ytController,
//                                                       showVideoProgressIndicator:
//                                                           true,
//                                                     ),
//                                             )
//                                           : Center(
//                                               child: AspectRatio(
//                                                 aspectRatio: _controller
//                                                     .value.aspectRatio,
//                                                 child: VisibilityDetector(
//                                                     key: Key("urlVideoPlayer"),
//                                                     onVisibilityChanged:
//                                                         (VisibilityInfo info) {
//                                                       if (info.visibleFraction ==
//                                                           1.0) {
//                                                         currentMin = 0;
//                                                         prevMin = 0;
//                                                         listenVideoChanges(
//                                                             _controller);
//                                                       }
//                                                     },
//                                                     // child: Text(
//                                                     //     '${_controller.value}'),

//                                                     child: VideoPlayer(
//                                                         _controller)),
//                                               ),
//                                             )

//                                       //remove due to vertical video
//                                       // : VisibilityDetector(
//                                       //     key: Key("urlVideoPlayer"),
//                                       //     onVisibilityChanged:
//                                       //         (VisibilityInfo info) {
//                                       //       if (info.visibleFraction == 1.0) {
//                                       //         currentMin = 0;
//                                       //         prevMin = 0;
//                                       //         listenVideoChanges(_controller);
//                                       //       }
//                                       //     },
//                                       //     child: VideoPlayer(_controller))
//                                       : Stack(
//                                           children: [
//                                             // selectedType == 'Notes'
//                                             selectedData is LearningShots &&
//                                                     (selectedData
//                                                                 as LearningShots)
//                                                             .contentType
//                                                             ?.toLowerCase() ==
//                                                         'notes'
//                                                 ? Positioned.fill(
//                                                     child: Image.asset(
//                                                       'assets/images/note_bg.png',
//                                                       fit: BoxFit.cover,
//                                                     ),
//                                                   )
//                                                 : Container(
//                                                     color: ColorConstants
//                                                         .COURSE_BG,
//                                                     width: double.infinity,
//                                                   ),
//                                             if (selectedData is Sessions)
//                                               Container(
//                                                   margin: EdgeInsets.symmetric(
//                                                       vertical: 8,
//                                                       horizontal: 8),
//                                                   child: Column(
//                                                     children: [
//                                                       Row(
//                                                         mainAxisAlignment:
//                                                             MainAxisAlignment
//                                                                 .spaceBetween,
//                                                         children: [
//                                                           Text(
//                                                             '${selectedData?.startDate != null ? Utility.convertDateFromMillis(selectedData?.startDate, Strings.CLASS_TIME_FORMAT) : ''} - ${selectedData?.endDate != null ? Utility.convertDateFromMillis(selectedData?.endDate, Strings.CLASS_TIME_FORMAT) : ''} | ${selectedData?.startDate != null ? Utility.convertDateFromMillis(selectedData?.startDate, Strings.DATE_MONTH) : ''}',
//                                                             style: Styles.bold(
//                                                                 color:
//                                                                     ColorConstants
//                                                                         .WHITE,
//                                                                 size: 14),
//                                                             textDirection: ui
//                                                                 .TextDirection
//                                                                 .ltr,
//                                                           ),
//                                                           Container(
//                                                             height: 20,
//                                                             padding: EdgeInsets
//                                                                 .symmetric(
//                                                                     horizontal:
//                                                                         8),
//                                                             decoration: BoxDecoration(
//                                                                 color:
//                                                                     ColorConstants
//                                                                         .WHITE,
//                                                                 borderRadius:
//                                                                     BorderRadius
//                                                                         .circular(
//                                                                             4)),
//                                                             child: Center(
//                                                                 child: Text(selectedData?.contentType ==
//                                                                             'otherclass'
//                                                                         ? 'weblink'
//                                                                         : selectedData?.contentType ==
//                                                                                 'teamsclass'
//                                                                             ? 'teams'
//                                                                             : selectedData?.contentType == 'liveclass' || selectedData?.contentType == 'zoomclass'
//                                                                                 ? 'live'
//                                                                                 : 'classroom')
//                                                                     .tr()),
//                                                           )
//                                                         ],
//                                                       ),
//                                                       SizedBox(height: 10),
//                                                       SizedBox(height: 5),
//                                                       Text(
//                                                         Utility().decrypted128(
//                                                             '$trainerName'),
//                                                         style: Styles.bold(
//                                                           size: 14,
//                                                           color: ColorConstants
//                                                               .WHITE,
//                                                         ),
//                                                       ),
//                                                       SizedBox(height: 10),
//                                                       Text(
//                                                         '${selectedData?.title}',
//                                                         style: Styles.regular(
//                                                           size: 18,
//                                                           color: ColorConstants
//                                                               .WHITE,
//                                                         ),
//                                                       )
//                                                     ],
//                                                   )),

//                                             if (selectedData is Assignments ||
//                                                 selectedData is Assessments ||
//                                                 selectedData is Scorm &&
//                                                     // selectedType == 'Assignment' ||
//                                                     // selectedType == 'Quiz' ||
//                                                     // selectedType == 'Scorm' &&
//                                                     selectedContentId != null)
//                                               Container(
//                                                 margin: EdgeInsets.symmetric(
//                                                     vertical: 8, horizontal: 8),
//                                                 child: Column(
//                                                   crossAxisAlignment:
//                                                       CrossAxisAlignment.start,
//                                                   children: [
//                                                     Row(
//                                                       children: [
//                                                         Text(
//                                                           '${tr('submit_before')} : ',
//                                                           style: Styles.bold(
//                                                               size: 14,
//                                                               color:
//                                                                   ColorConstants
//                                                                       .WHITE),
//                                                         ),
//                                                         Text(
//                                                           '${selectedData?.endDate != null ? Utility.convertDateFromMillis(selectedData?.endDate, Strings.REQUIRED_DATE_HH_MM_AAA_DD_MMM_YYYY) : ''}',
//                                                           style: Styles.bold(
//                                                               size: 14,
//                                                               color:
//                                                                   ColorConstants
//                                                                       .WHITE),
//                                                           textDirection: ui
//                                                               .TextDirection
//                                                               .ltr,
//                                                         ),
//                                                         Spacer(),
//                                                         if (selectedData
//                                                             is Assessments)
//                                                           Text(
//                                                             '${selectedData?.durationInMinutes} ${selectedData?.durationInMinutes == 0 || selectedData?.durationInMinutes == 1 ? tr('min') : tr('mins')}',
//                                                             // '${selectedType}',
//                                                             style: Styles.bold(
//                                                                 size: 14,
//                                                                 color:
//                                                                     ColorConstants
//                                                                         .WHITE),
//                                                           ),
//                                                       ],
//                                                     ),
//                                                     SizedBox(height: 15),
//                                                     Text(
//                                                         '${selectedData?.title}',
//                                                         style: Styles.bold(
//                                                             size: 16,
//                                                             color:
//                                                                 ColorConstants
//                                                                     .WHITE)),
//                                                     if (selectedData is Scorm &&
//                                                         selectedData
//                                                                 ?.description !=
//                                                             null)
//                                                       Text(
//                                                           '${selectedData?.description}',
//                                                           maxLines: 2,
//                                                           overflow: TextOverflow
//                                                               .ellipsis,
//                                                           style: Styles.semibold(
//                                                               size: 14,
//                                                               color:
//                                                                   ColorConstants
//                                                                       .WHITE)),
//                                                     SizedBox(height: 8),
//                                                     Row(
//                                                       children: [
//                                                         if (selectedData
//                                                             is Assignment)
//                                                           Row(
//                                                             children: [
//                                                               if (selectedData
//                                                                       .isGraded ==
//                                                                   1) ...[
//                                                                 Text(
//                                                                   '${selectedData.overallScore != null ? '${selectedData.overallScore}/${selectedData?.maximumMarks} ${tr('marks')}' : '${selectedData?.maximumMarks} ${tr('marks')}'} • ${selectedData?.allowMultiple == 1 ? tr('multiple_attempt') : '1 ${tr('attempt')}'}',
//                                                                   style: Styles
//                                                                       .semibold(
//                                                                           size:
//                                                                               14,
//                                                                           color:
//                                                                               ColorConstants.WHITE),
//                                                                 ),
//                                                               ] else
//                                                                 Text(
//                                                                   '${tr('non_graded')} • ${selectedData?.allowMultiple == 1 ? '${tr('multiple_attempt')}' : '1 ${tr('attempt')}'}',
//                                                                   style: Styles
//                                                                       .semibold(
//                                                                           size:
//                                                                               14,
//                                                                           color:
//                                                                               ColorConstants.WHITE),
//                                                                 ),
//                                                             ],
//                                                           ),
//                                                         if (selectedData
//                                                             is Assessments) ...[
//                                                           selectedData?.attemptsRemaining !=
//                                                                   selectedData
//                                                                       .attemptAllowed
//                                                               ? Text(
//                                                                   '${selectedData?.overallScore}/${selectedData?.maximumMarks} ${tr('marks')} • ${selectedData?.attemptAllowed == 0 ? '${tr('unlimited_attempt')}' : '${selectedData?.attemptsRemaining} ${tr('attempt_available')}'} ',
//                                                                   style: Styles.regular(
//                                                                       size: 14,
//                                                                       color: ColorConstants
//                                                                           .WHITE),
//                                                                 )
//                                                               : Text(
//                                                                   '${selectedData?.maximumMarks} ${tr('marks')} • ${selectedData?.attemptAllowed == 0 ? '${tr('unlimited_attempt')}' : '${selectedData?.attemptsRemaining} ${tr('attempt_available')}'} ',
//                                                                   style: Styles.regular(
//                                                                       size: 14,
//                                                                       color: ColorConstants
//                                                                           .WHITE),
//                                                                 ),
//                                                         ]
//                                                       ],
//                                                     ),
//                                                   ],
//                                                 ),
//                                               ),
//                                             !(selectedData is Sessions &&
//                                                     selectedData
//                                                             ?.liveclassAction
//                                                             .toString()
//                                                             .toLowerCase() ==
//                                                         'concluded')
//                                                 ? Positioned(
//                                                     bottom: 24,
//                                                     left: 50,
//                                                     right: 50,
//                                                     child: GestureDetector(
//                                                       onTap: () {
//                                                         if (isButtonActive) {
//                                                           _controller.pause();
//                                                           if (selectedData
//                                                               is Scorm) {
//                                                             Navigator.push(
//                                                               context,
//                                                               MaterialPageRoute(
//                                                                 builder:
//                                                                     (context) {
//                                                                   return CommonWebView(
//                                                                       url: selectedData
//                                                                           .url);
//                                                                 },
//                                                               ),
//                                                             );
//                                                             return;
//                                                           } else if (selectedData
//                                                               is Sessions) {
//                                                             if (selectedData
//                                                                         .contentType ==
//                                                                     'zoomclass' ||
//                                                                 selectedData
//                                                                         .contentType ==
//                                                                     'teamsclass') {
//                                                               setState(() {
//                                                                 currentZoomUrl =
//                                                                     selectedData
//                                                                         .zoomUrl;
//                                                                 currentOpenUrl =
//                                                                     selectedData
//                                                                         .openUrl;
//                                                               });

//                                                               if (currentZoomUrl !=
//                                                                   null) {
//                                                                 BlocProvider.of<
//                                                                             HomeBloc>(
//                                                                         context)
//                                                                     .add(
//                                                                         ZoomOpenUrlEvent(
//                                                                   contentId:
//                                                                       selectedData
//                                                                           .programContentId,
//                                                                 ));
//                                                                 launchUrl(
//                                                                     Uri.parse(
//                                                                         '$currentZoomUrl'),
//                                                                     mode: LaunchMode
//                                                                         .externalApplication);
//                                                               } else {
//                                                                 BlocProvider.of<
//                                                                             HomeBloc>(
//                                                                         context)
//                                                                     .add(ZoomOpenUrlEvent(
//                                                                         contentId:
//                                                                             selectedData.programContentId));
//                                                               }
//                                                             } else {
//                                                               launchUrl(Uri.parse(
//                                                                   '${selectedData?.liveclassUrl}'));
//                                                             }
//                                                           }
//                                                           if (selectedData
//                                                                       is LearningShots &&
//                                                                   (selectedData
//                                                                               as LearningShots)
//                                                                           .contentType
//                                                                           ?.toLowerCase() ==
//                                                                       'notes'
//                                                               // selectedType ==
//                                                               //   'Notes'

//                                                               ) {
//                                                             playVideo(context);
//                                                           } else if (selectedData
//                                                               is Assignment) {
//                                                             openAssignment(
//                                                                     selectedData)
//                                                                 .then((value) {
//                                                               trainingDetailProvider =
//                                                                   Provider.of<
//                                                                           TrainingDetailProvider>(
//                                                                       context);
//                                                             });
//                                                           } else if (selectedData
//                                                                   is Assessments &&
//                                                               selectedData
//                                                                       ?.status
//                                                                       ?.toLowerCase() !=
//                                                                   'pending') {
//                                                             openAssessment(
//                                                                     selectedData,
//                                                                     trainingDetailProvider
//                                                                         .program!
//                                                                         .name!,
//                                                                     selectedData
//                                                                             .overallResult
//                                                                             .toString()
//                                                                             .toLowerCase() ==
//                                                                         'pass',
//                                                                     trainingDetailProvider
//                                                                         .program!
//                                                                         .id!)
//                                                                 .then((value) {
//                                                               try {
//                                                                 trainingDetailProvider =
//                                                                     Provider.of<
//                                                                             TrainingDetailProvider>(
//                                                                         context,
//                                                                         listen:
//                                                                             false);
//                                                               } catch (e) {
//                                                                 Log.v('$e');
//                                                               }
//                                                             });
//                                                           }
//                                                         } else {
//                                                           if (selectedData
//                                                               is Sessions)
//                                                             ScaffoldMessenger
//                                                                     .of(context)
//                                                                 .showSnackBar(
//                                                                     SnackBar(
//                                                               content: Text(
//                                                                       "coming_soon")
//                                                                   .tr(),
//                                                             ));
//                                                         }
//                                                       },
//                                                       child: Container(
//                                                           width: 150,
//                                                           height: 38,
//                                                           decoration: BoxDecoration(
//                                                               color: bgColor,
//                                                               gradient: bgColor ==
//                                                                       ColorConstants()
//                                                                           .primaryColorAlways()
//                                                                   ? LinearGradient(
//                                                                       begin: Alignment
//                                                                           .centerLeft,
//                                                                       end: Alignment.centerRight,
//                                                                       colors: <Color>[
//                                                                           ColorConstants()
//                                                                               .gradientLeft(),
//                                                                           ColorConstants()
//                                                                               .gradientRight()
//                                                                         ])
//                                                                   : null,
//                                                               borderRadius:
//                                                                   BorderRadius
//                                                                       .circular(
//                                                                           8)),
//                                                           child: Center(
//                                                             child: Text(
//                                                               '$title',
//                                                               style: Styles.regular(
//                                                                   size: 14,
//                                                                   color: bgColor ==
//                                                                           ColorConstants
//                                                                               .GREY_2
//                                                                       ? ColorConstants
//                                                                           .GREY_3
//                                                                       : ColorConstants()
//                                                                           .primaryForgroundColor()),
//                                                               textAlign:
//                                                                   TextAlign
//                                                                       .center,
//                                                             ),
//                                                           )),
//                                                     ),
//                                                   )
//                                                 : Positioned(
//                                                     bottom: 24,
//                                                     left: 50,
//                                                     right: 50,
//                                                     child: Container(
//                                                       width: 150,
//                                                       height: 38,
//                                                       decoration: BoxDecoration(
//                                                           color: ColorConstants
//                                                               .GREY_2,
//                                                           borderRadius:
//                                                               BorderRadius
//                                                                   .circular(8)),
//                                                       child: Center(
//                                                         child: Text(
//                                                           'your_class_finished',
//                                                           style: Styles.regular(
//                                                               size: 14,
//                                                               color:
//                                                                   ColorConstants
//                                                                       .GREY_3),
//                                                           textAlign:
//                                                               TextAlign.center,
//                                                         ).tr(),
//                                                       ),
//                                                     )),
//                                           ],
//                                         )),
//                               transitionOnUserGestures: true,
//                             ),
//                             if (!isNoteView &&
//                                 isYoutubeView == false &&
//                                 _controller.value.hasError)
//                               Positioned.fill(
//                                   child: AnimatedOpacity(
//                                 opacity: opacityLevel,
//                                 duration: const Duration(seconds: 2),
//                                 child: Visibility(
//                                   visible: opacityLevel == 1.0,
//                                   child: Row(
//                                     mainAxisAlignment:
//                                         MainAxisAlignment.spaceEvenly,
//                                     children: [
//                                       GestureDetector(
//                                           onTap: () {
//                                             _controller.seekTo(Duration(
//                                                 seconds: _controller.value
//                                                         .position.inSeconds -
//                                                     10));
//                                           },
//                                           child: SvgPicture.asset(
//                                             'assets/images/rewind.svg',
//                                             color: ColorConstants.WHITE,
//                                             height: 30,
//                                             width: 30,
//                                             allowDrawingOutsideViewBox: true,
//                                           )),
//                                       GestureDetector(
//                                         onTap: () {
//                                           setState(() {
//                                             _controller.value.isPlaying
//                                                 ? _controller.pause()
//                                                 : _controller.play();
//                                           });
//                                         },
//                                         child: !_controller.value.isPlaying
//                                             ? SvgPicture.asset(
//                                                 'assets/images/play.svg',
//                                                 color: ColorConstants.WHITE,
//                                                 height: 30,
//                                                 width: 30,
//                                                 allowDrawingOutsideViewBox:
//                                                     true,
//                                               )
//                                             : Icon(Icons.pause,
//                                                 color: ColorConstants.WHITE,
//                                                 size: 30),
//                                       ),
//                                       GestureDetector(
//                                           onTap: () {
//                                             _controller.seekTo(Duration(
//                                                 seconds: _controller.value
//                                                         .position.inSeconds +
//                                                     10));
//                                           },
//                                           child: SvgPicture.asset(
//                                             'assets/images/forward.svg',
//                                             color: ColorConstants.WHITE,
//                                             height: 30,
//                                             width: 30,
//                                             allowDrawingOutsideViewBox: true,
//                                           )),
//                                     ],
//                                   ),
//                                 ),
//                               )),
//                             if (!isNoteView && isYoutubeView == false)
//                               Positioned(
//                                 bottom: 6,
//                                 right: Utility().isRTL(context) ? null : 6,
//                                 left: Utility().isRTL(context) ? 6 : null,
//                                 child: GestureDetector(
//                                     onTap: () {
//                                       playVideo(context);
//                                     },
//                                     child: SvgPicture.asset(
//                                       'assets/images/full_screen_video.svg',
//                                       color: ColorConstants.WHITE,
//                                       height: 26,
//                                       width: 26,
//                                       allowDrawingOutsideViewBox: true,
//                                     )),
//                               ),
//                             if (!isNoteView && isYoutubeView == false)
//                               Positioned(
//                                   bottom: 6,
//                                   left: Utility().isRTL(context) ? null : 6,
//                                   right: Utility().isRTL(context) ? 6 : null,
//                                   child: ValueListenableBuilder(
//                                     valueListenable: _controller,
//                                     builder: (context, VideoPlayerValue value,
//                                         child) {
//                                       return Text(
//                                         '${value.position.toString().substring(0, 7)}/${value.duration.toString().substring(0, 7)}',
//                                         style: Styles.regular(
//                                             color: ColorConstants.WHITE),
//                                       );
//                                     },
//                                   )),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               )
//             : isAllSelected == true && showLoading == true
//                 ? Container(
//                     width: MediaQuery.of(context).size.width,
//                     height: MediaQuery.of(context).size.height * 0.2,
//                     color: ColorConstants.GREY_4,
//                     child: Center(child: CircularProgressIndicator()),
//                   )
//                 : Container(
//                     width: MediaQuery.of(context).size.width,
//                     height: kIsWeb && MediaQuery.of(context).size.width > 500
//                         ? MediaQuery.of(context).size.height * 0.5
//                         : MediaQuery.of(context).size.height * 0.25,
//                     color: ColorConstants.WHITE,
//                     child: showLoading == true
//                         ? Center(child: CircularProgressIndicator())
//                         : Column(
//                             crossAxisAlignment: CrossAxisAlignment.center,
//                             mainAxisAlignment: MainAxisAlignment.center,
//                             children: [
//                               SvgPicture.asset(
//                                 'assets/images/no_content_found.svg',
//                                 color: ColorConstants.GREY_2,
//                                 height: height(context) * 0.07,
//                                 fit: BoxFit.cover,
//                               ),
//                               SizedBox(
//                                 height: 10,
//                               ),
//                               Text(
//                                 '${tr('no_content_found')}',
//                                 style: Styles.regular(),
//                               ),
//                             ],
//                           ),
//                   ),
//         if (!isNoteView &&
//             isYoutubeView == false &&
//             selectedContentId != null &&
//             showLoading == false)
//           Transform.rotate(
//             angle: Utility().isRTL(context) ? -math.pi : 0,
//             child: Container(
//               height: height(context) * 0.05,
//               child: SmoothVideoProgress(
//                 controller: _controller,
//                 builder: (context, position, duration, child) => SliderTheme(
//                   data: SliderThemeData(
//                     // here
//                     trackShape: CustomTrackShape(11),
//                   ),
//                   child: Slider(
//                     onChangeStart: (_) => _controller.pause(),
//                     onChangeEnd: (_) => _controller.play(),
//                     onChanged: (value) => _controller
//                         .seekTo(Duration(milliseconds: value.toInt())),
//                     value: position.inMilliseconds.toDouble(),
//                     min: 0,
//                     max: duration.inMilliseconds.toDouble(),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         if (selectedData != null &&
//             APK_DETAILS['package_name'] != 'com.singularis.mescdigilibrary' &&
//             selectedContentId != null &&
//             orientation == Orientation.portrait)
//           Container(
//             color: ColorConstants.WHITE,
//             width: MediaQuery.of(context).size.width,
//             padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//             child: Column(
//               // mainAxisAlignment: MainAxisAlignment.start,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                     (selectedData is Sessions ||
//                             (selectedData is LearningShots &&
//                                 ['video', 'video_yts'].contains(
//                                     (selectedData as LearningShots)
//                                         .contentType
//                                         ?.toLowerCase())))
//                         ? '${tr('now_playing')}'
//                         : tr('${selectedData.contentType.toLowerCase()}'),
//                     style: Styles.regular(
//                       size: 14,
//                     )),
//                 Text('$selectedItemName', style: Styles.semibold(size: 16)),
//               ],
//             ),
//           ),
//         if (APK_DETAILS['package_name'] != 'com.singularis.mescdigilibrary' &&
//             orientation == Orientation.portrait)
//           SingleChildScrollView(
//               scrollDirection: Axis.horizontal,
//               child: Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     InkWell(
//                       onTap: () {
//                         isAllSelected = true;

//                         selectedContentId = null;

//                         _controller.pause();

//                         // selectedType = 'Classes';

//                         setState(() {});
//                       },
//                       child: Container(
//                         padding:
//                             EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                         margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
//                         decoration: BoxDecoration(
//                             borderRadius: BorderRadius.circular(12),
//                             gradient: isAllSelected == true
//                                 ? LinearGradient(
//                                     begin: Alignment.centerLeft,
//                                     end: Alignment.centerRight,
//                                     colors: <Color>[
//                                         ColorConstants().gradientLeft(),
//                                         ColorConstants().gradientRight()
//                                       ])
//                                 : null,
//                             color: isAllSelected == true
//                                 ? ColorConstants().primaryColor()
//                                 : ColorConstants.WHITE),
//                         child: Text('modules',
//                                 style: Styles.regular(
//                                     size: 14,
//                                     color: isAllSelected == true
//                                         ? ColorConstants.WHITE
//                                         : ColorConstants.GREY_3))
//                             .tr(),
//                       ),
//                     ),
//                     InkWell(
//                       onTap: () {
//                         selectedType = 'Classes';
//                         isAllSelected = false;
//                         selectedData = null;
//                         selectedContentId = null;
//                         _controller.pause();

//                         setState(() {});
//                         print(
//                             "value is now check ${selectedData.contentType.toString()}");
//                       },
//                       child: Container(
//                         padding:
//                             EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                         margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
//                         decoration: BoxDecoration(
//                             borderRadius: BorderRadius.circular(12),
//                             gradient: selectedType == 'Classes' &&
//                                     isAllSelected == false
//                                 ? LinearGradient(
//                                     begin: Alignment.centerLeft,
//                                     end: Alignment.centerRight,
//                                     colors: <Color>[
//                                         ColorConstants().gradientLeft(),
//                                         ColorConstants().gradientRight()
//                                       ])
//                                 : null,
//                             color: selectedType == 'Classes' &&
//                                     isAllSelected == false
//                                 ? ColorConstants().primaryColor()
//                                 : ColorConstants.WHITE),
//                         child: Text('classes',
//                                 style: Styles.regular(
//                                     size: 14,
//                                     color: selectedType == 'Classes' &&
//                                             isAllSelected == false
//                                         ? ColorConstants.WHITE
//                                         : ColorConstants.GREY_3))
//                             .tr(),
//                       ),
//                     ),
//                     InkWell(
//                         onTap: () {
//                           selectedType = 'Videos';
//                           isAllSelected = false;

//                           selectedContentId = null;

//                           _controller.pause();

//                           setState(() {});
//                         },
//                         child: Container(
//                           padding:
//                               EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                           margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
//                           decoration: BoxDecoration(
//                               borderRadius: BorderRadius.circular(12),
//                               gradient: selectedType == 'Videos' &&
//                                       isAllSelected == false
//                                   ? LinearGradient(
//                                       begin: Alignment.centerLeft,
//                                       end: Alignment.centerRight,
//                                       colors: <Color>[
//                                           ColorConstants().gradientLeft(),
//                                           ColorConstants().gradientRight()
//                                         ])
//                                   : null,
//                               color: selectedType == 'Videos' &&
//                                       isAllSelected == false
//                                   ? ColorConstants().primaryColor()
//                                   : ColorConstants.WHITE),
//                           child: Text('videos',
//                                   style: Styles.regular(
//                                       size: 14,
//                                       color: selectedType == 'Videos' &&
//                                               isAllSelected == false
//                                           ? ColorConstants.WHITE
//                                           : ColorConstants.GREY_3))
//                               .tr(),
//                         )),
//                     InkWell(
//                         onTap: () {
//                           setState(() {
//                             selectedType = 'Notes';
//                             selectedContentId = null;
//                             isAllSelected = false;

//                             _controller.pause();
//                           });
//                         },
//                         child: Container(
//                           padding:
//                               EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                           margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
//                           decoration: BoxDecoration(
//                               borderRadius: BorderRadius.circular(12),
//                               gradient: selectedType == 'Notes' &&
//                                       isAllSelected == false
//                                   ? LinearGradient(
//                                       begin: Alignment.centerLeft,
//                                       end: Alignment.centerRight,
//                                       colors: <Color>[
//                                           ColorConstants().gradientLeft(),
//                                           ColorConstants().gradientRight()
//                                         ])
//                                   : null,
//                               color: selectedType == 'Notes' &&
//                                       isAllSelected == false
//                                   ? ColorConstants().primaryColor()
//                                   : ColorConstants.WHITE),
//                           child: Text('notes',
//                                   style: Styles.regular(
//                                       size: 14,
//                                       color: selectedType == 'Notes' &&
//                                               isAllSelected == false
//                                           ? ColorConstants.WHITE
//                                           : ColorConstants.GREY_3))
//                               .tr(),
//                         )),
//                     InkWell(
//                         onTap: () {
//                           setState(() {
//                             selectedType = 'Assignment';
//                             selectedContentId = null;
//                             isAllSelected = false;

//                             _controller.pause();
//                           });
//                         },
//                         child: Container(
//                           padding:
//                               EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                           margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
//                           decoration: BoxDecoration(
//                               borderRadius: BorderRadius.circular(12),
//                               gradient: selectedType == 'Assignment' &&
//                                       isAllSelected == false
//                                   ? LinearGradient(
//                                       begin: Alignment.centerLeft,
//                                       end: Alignment.centerRight,
//                                       colors: <Color>[
//                                           ColorConstants().gradientLeft(),
//                                           ColorConstants().gradientRight()
//                                         ])
//                                   : null,
//                               color: selectedType == 'Assignment' &&
//                                       isAllSelected == false
//                                   ? ColorConstants().primaryColor()
//                                   : ColorConstants.WHITE),
//                           child: Text('assignment',
//                                   style: Styles.regular(
//                                       size: 14,
//                                       color: selectedType == 'Assignment' &&
//                                               isAllSelected == false
//                                           ? ColorConstants.WHITE
//                                           : ColorConstants.GREY_3))
//                               .tr(),
//                         )),
//                     InkWell(
//                         onTap: () {
//                           setState(() {
//                             selectedType = 'Quiz';
//                             selectedContentId = null;
//                             isAllSelected = false;

//                             _controller.pause();
//                           });
//                         },
//                         child: Container(
//                           padding:
//                               EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                           margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
//                           decoration: BoxDecoration(
//                               borderRadius: BorderRadius.circular(12),
//                               gradient: selectedType == 'Quiz' &&
//                                       isAllSelected == false
//                                   ? LinearGradient(
//                                       begin: Alignment.centerLeft,
//                                       end: Alignment.centerRight,
//                                       colors: <Color>[
//                                           ColorConstants().gradientLeft(),
//                                           ColorConstants().gradientRight()
//                                         ])
//                                   : null,
//                               color: selectedType == 'Quiz' &&
//                                       isAllSelected == false
//                                   ? ColorConstants().primaryColor()
//                                   : ColorConstants.WHITE),
//                           child: Text('quiz',
//                                   style: Styles.regular(
//                                       size: 14,
//                                       color: selectedType == 'Quiz' &&
//                                               isAllSelected == false
//                                           ? ColorConstants.WHITE
//                                           : ColorConstants.GREY_3))
//                               .tr(),
//                         )),
//                     InkWell(
//                         onTap: () {
//                           setState(() {
//                             selectedType = 'Scorm';
//                             selectedContentId = null;
//                             isAllSelected = false;

//                             _controller.pause();
//                           });
//                         },
//                         child: Container(
//                           padding:
//                               EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                           margin: EdgeInsets.only(top: 6, right: 6, bottom: 8),
//                           decoration: BoxDecoration(
//                               borderRadius: BorderRadius.circular(12),
//                               gradient: selectedType == 'Scorm' &&
//                                       isAllSelected == false
//                                   ? LinearGradient(
//                                       begin: Alignment.centerLeft,
//                                       end: Alignment.centerRight,
//                                       colors: <Color>[
//                                           ColorConstants().gradientLeft(),
//                                           ColorConstants().gradientRight()
//                                         ])
//                                   : null,
//                               color: selectedType == 'Scorm' &&
//                                       isAllSelected == false
//                                   ? ColorConstants().primaryColor()
//                                   : ColorConstants.WHITE),
//                           child: Text('scorm',
//                                   style: Styles.regular(
//                                       size: 14,
//                                       color: selectedType == 'Scorm' &&
//                                               isAllSelected == false
//                                           ? ColorConstants.WHITE
//                                           : ColorConstants.GREY_3))
//                               .tr(),
//                         ))
//                   ],
//                 ),
//               )),
//         if (orientation == Orientation.portrait)
//           Expanded(
//             child: ListView.builder(
//                 scrollDirection: Axis.vertical,
//                 shrinkWrap: true,
//                 itemCount: trainingDetailProvider.modules!.length,
//                 itemBuilder: (context, index) {
//                   return FutureBuilder(
//                       future: selectedContentId == null
//                           ? Future.delayed(Duration(milliseconds: 1000 * index))
//                           : Future.delayed(Duration(milliseconds: 0)),
//                       // future: Future.delayed(Duration(milliseconds: 0)),
//                       builder: (context, snapshot) {
//                         if (snapshot.connectionState ==
//                                 ConnectionState.waiting &&
//                             moduleOnceOpened == false) return SizedBox();

//                         // if (isAllSelected == false &&
//                         //     !containsContent(
//                         //         trainingDetailProvider.modules
//                         //             ?.elementAt(index),
//                         //         selectedType)) {
//                         //   return SizedBox();
//                         // }
//                         // String? subjectName = trainingDetailProvider.skills[
//                         //         '${trainingDetailProvider.modules?.elementAt(index).skillId}']
//                         //     ?['name'];

//                         String? subjectName = '';

//                         try {
//                           subjectName = trainingDetailProvider.skills[
//                                   '${trainingDetailProvider.modules?.elementAt(index).skillId}']
//                               ?['name'];
//                         } catch (e) {}
//                         return Container(
//                           padding: const EdgeInsets.symmetric(horizontal: 4),
//                           margin: EdgeInsets.only(
//                               top: subjectName != null &&
//                                       (trainingDetailProvider.modules
//                                               ?.elementAt(index)
//                                               .skillId !=
//                                           trainingDetailProvider.modules
//                                               ?.elementAt(max(0, index - 1))
//                                               .skillId)
//                                   ? 8
//                                   : 0),
//                           decoration: BoxDecoration(color: Colors.white),
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               if (subjectName != null &&
//                                   (index == 0 ||
//                                       trainingDetailProvider.modules
//                                               ?.elementAt(index)
//                                               .skillId !=
//                                           trainingDetailProvider.modules
//                                               ?.elementAt(max(0, index - 1))
//                                               .skillId)) ...[
//                                 Container(
//                                   width: double.infinity,
//                                   // margin: const EdgeInsets.all(4),

//                                   decoration: BoxDecoration(
//                                     // color: ColorConstants()
//                                     //     .primaryColorAlways()
//                                     //     .withOpacity(0.8),
//                                     borderRadius: BorderRadius.circular(6),
//                                     // border: Border.all()
//                                   ),
//                                   padding: const EdgeInsets.only(
//                                       top: 12, left: 12, right: 12),
//                                   child: Text(
//                                     '$subjectName',
//                                     style: Styles.bold(
//                                       lineHeight: 1,
//                                       size: 16,
//                                       // color: ColorConstants.WHITE
//                                     ),
//                                   ),
//                                 ),
//                                 Divider(
//                                   thickness: 1,
//                                 ),
//                               ],
//                               Container(
//                                 margin: const EdgeInsets.only(left: 15),
//                                 padding: EdgeInsets.symmetric(
//                                     vertical: 8, horizontal: 8),
//                                 decoration: BoxDecoration(
//                                   color: ColorConstants.WHITE,
//                                 ),
//                                 child: ExpandablePanel(
//                                   theme: ExpandableThemeData(
//                                     hasIcon: true,
//                                   ),
//                                   controller:
//                                       _expandableController.length > index
//                                           ? _expandableController[index]
//                                           : _expandableController[0],
//                                   header: Column(
//                                     mainAxisAlignment: MainAxisAlignment.start,
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       Text(
//                                           '${trainingDetailProvider.modules!.elementAt(index).name}',
//                                           maxLines: 1,
//                                           overflow: TextOverflow.ellipsis,
//                                           softWrap: false,
//                                           style: Styles.semibold(size: 16)),
//                                       Text(
//                                         '${trainingDetailProvider.modules!.elementAt(index).description}',
//                                         maxLines: 1,
//                                         overflow: TextOverflow.ellipsis,
//                                         softWrap: false,
//                                         style: Styles.regular(size: 14),
//                                       )
//                                     ],
//                                   ),
//                                   collapsed: SizedBox(
//                                     height: 0,
//                                   ),
//                                   expanded: ChangeNotifierProvider<
//                                           TrainingContentProvier>(
//                                       create: (context) =>
//                                           TrainingContentProvier(
//                                             TrainingService(ApiService()),
//                                             trainingDetailProvider.modules!
//                                                 .elementAt(index),
//                                           ),
//                                       child: Consumer<TrainingContentProvier?>(
//                                           builder: (context,
//                                               trainingContentProvier, child) {
//                                         try {
//                                           if (trainingContentProvier
//                                                   ?.trainingModuleResponse
//                                                   .data ==
//                                               null) return SizedBox();
//                                         } catch (e) {
//                                           return SizedBox();
//                                         }
//                                         if (trainingContentProvier
//                                                 ?.trainingModuleResponse.data ==
//                                             null) return SizedBox();
//                                         // open first module on default
//                                         if (!moduleOnceOpened) {
//                                           _expandableController[0] =
//                                               ExpandableController(
//                                                   initialExpanded: true);
//                                           moduleOnceOpened = true;
//                                           WidgetsBinding.instance
//                                               .addPostFrameCallback((_) {
//                                             setState(() {});
//                                           });
//                                         }
//                                         if (trainingContentProvier
//                                                 ?.trainingModuleResponse ==
//                                             null) return SizedBox();

//                                         WidgetsBinding.instance
//                                             .addPostFrameCallback((_) {
//                                           trainingContentProvier
//                                               ?.getSelectedTypeContent(
//                                                   selectedType: selectedType,
//                                                   isModulesSelected:
//                                                       isAllSelected);
//                                         });
//                                         // if (trainingContentProvier
//                                         //         ?.containsClasses ==
//                                         //     false) {
//                                         //   return SizedBox();
//                                         // }

//                                         return ModuleCourseCard(
//                                           trainingContentProvider:
//                                               trainingContentProvier!,
//                                           onValueUpdate: () {
//                                             // _controller.dispose();
//                                             // _ytController.dispose();
//                                             print("set state is called");
//                                             _controller.dispose();
//                                             _controller = VideoPlayerController
//                                                 .networkUrl(Uri.parse(''));

//                                             setState(() {});
//                                           },
//                                           onVideoPause: () {
//                                             _controller.pause();
//                                           },
//                                           sendValue: (dynamic controller,
//                                               String title,
//                                               bool isNote,
//                                               String NoteUrl,
//                                               String noteImageUrl,
//                                               dynamic data,
//                                               {isYoutubeController =
//                                                   false}) async {
//                                             await Future.delayed(
//                                                 Duration(seconds: 1));
//                                             currentTrainingProvider =
//                                                 trainingContentProvier;

//                                             isYoutubeView = isYoutubeController;
//                                             selectedItemName = title;
//                                             isNoteView = isNote;
//                                             selectedData = data;
//                                             if (isNote) {
//                                               setState(() {
//                                                 noteUrl = NoteUrl;
//                                                 noteImgUrl = noteImageUrl;
//                                                 popupHeight =
//                                                     MediaQuery.of(context)
//                                                         .size
//                                                         .height;
//                                               });
//                                             } else {
//                                               setState(() {
//                                                 isYoutubeController == true
//                                                     ? _ytController = controller
//                                                     : _controller = controller;
//                                                 popupHeight = 300;

//                                                 _controller.initialize().then(
//                                                     (value) => setState(() {
//                                                           showLoading = false;
//                                                           _controller.play();
//                                                         }));
//                                               });
//                                             }
//                                           },
//                                           now: now,
//                                         );
//                                       })),
//                                 ),
//                               ),
//                             ],
//                           ),
//                         );
//                       });
//                 }),
//           ),
//       ],
//     );
//   }

//   playVideo(BuildContext context) {
//     Navigator.push(
//         context,
//         PageTransition(
//             type: PageTransitionType.fade,
//             child: Scaffold(
//               body: SafeArea(
//                 child: Container(
//                   height: MediaQuery.of(context).size.height,
//                   width: MediaQuery.of(context).size.width,
//                   color: Colors.black,
//                   child: RotatedBox(
//                     quarterTurns: isNoteView ? 0 : 1,
//                     child: Stack(
//                       children: [
//                         Center(
//                           child: SizedBox(
//                             height: MediaQuery.of(context).size.height,
//                             width: double.infinity,
//                             child: ClipRRect(
//                               borderRadius: BorderRadius.circular(0),
//                               child: Stack(
//                                 children: [
//                                   Hero(
//                                     tag: 'videoPlayer',
//                                     child: isNoteView
//                                         ? CompetitionNotes(
//                                             notesUrl: noteUrl,
//                                             id: selectedContentId)
//                                         : VisibilityDetector(
//                                             key: Key("popUpUrlVideoPlayer"),
//                                             onVisibilityChanged:
//                                                 (VisibilityInfo info) {
//                                               if (info.visibleFraction == 1.0) {
//                                                 currentMin = 0;
//                                                 prevMin = 0;
//                                                 listenVideoChanges(_controller);
//                                               }
//                                             },
//                                             child: VideoPlayer(_controller)),
//                                   ),
//                                   if (!isNoteView)
//                                     Positioned.fill(
//                                       child: ValueListenableBuilder(
//                                         valueListenable: _controller,
//                                         builder: (context,
//                                             VideoPlayerValue value, child) {
//                                           //Do Something with the value.
//                                           return Row(
//                                             mainAxisAlignment:
//                                                 MainAxisAlignment.spaceEvenly,
//                                             children: [
//                                               GestureDetector(
//                                                   onTap: () {
//                                                     _controller.seekTo(Duration(
//                                                         seconds: _controller
//                                                                 .value
//                                                                 .position
//                                                                 .inSeconds -
//                                                             10));
//                                                   },
//                                                   child: SvgPicture.asset(
//                                                     'assets/images/rewind.svg',
//                                                     color: ColorConstants.WHITE,
//                                                     height: 30,
//                                                     width: 30,
//                                                     allowDrawingOutsideViewBox:
//                                                         true,
//                                                   )),
//                                               GestureDetector(
//                                                 onTap: () {
//                                                   setState(() {
//                                                     _controller.value.isPlaying
//                                                         ? _controller.pause()
//                                                         : _controller.play();
//                                                   });
//                                                 },
//                                                 child: !value.isPlaying
//                                                     ? SvgPicture.asset(
//                                                         'assets/images/play.svg',
//                                                         color: ColorConstants
//                                                             .WHITE,
//                                                         height: 30,
//                                                         width: 30,
//                                                         allowDrawingOutsideViewBox:
//                                                             true,
//                                                       )
//                                                     : Icon(Icons.pause,
//                                                         color: ColorConstants
//                                                             .WHITE,
//                                                         size: 30),
//                                               ),
//                                               GestureDetector(
//                                                   onTap: () {
//                                                     _controller.seekTo(Duration(
//                                                         seconds: _controller
//                                                                 .value
//                                                                 .position
//                                                                 .inSeconds +
//                                                             10));
//                                                   },
//                                                   child: SvgPicture.asset(
//                                                     'assets/images/forward.svg',
//                                                     color: ColorConstants.WHITE,
//                                                     height: 30,
//                                                     width: 30,
//                                                     allowDrawingOutsideViewBox:
//                                                         true,
//                                                   )),
//                                             ],
//                                           );
//                                         },
//                                       ),
//                                     ),
//                                   if (!isNoteView)
//                                     Positioned(
//                                         bottom: 0,
//                                         left: 0,
//                                         right: 0,
//                                         child: Transform.rotate(
//                                           angle: Utility().isRTL(context)
//                                               ? -math.pi
//                                               : 0,
//                                           child: Container(
//                                             width: width(context),
//                                             child: SmoothVideoProgress(
//                                               controller: _controller,
//                                               builder: (context, position,
//                                                       duration, child) =>
//                                                   SliderTheme(
//                                                 data: SliderThemeData(
//                                                   trackShape:
//                                                       CustomTrackShape(11),
//                                                 ),
//                                                 child: Slider(
//                                                   onChangeStart: (_) =>
//                                                       _controller.pause(),
//                                                   onChangeEnd: (_) =>
//                                                       _controller.play(),
//                                                   onChanged: (value) =>
//                                                       _controller.seekTo(
//                                                           Duration(
//                                                               milliseconds: value
//                                                                   .toInt())),
//                                                   value: position.inMilliseconds
//                                                       .toDouble(),
//                                                   min: 0,
//                                                   max: duration.inMilliseconds
//                                                       .toDouble(),
//                                                 ),
//                                               ),
//                                             ),
//                                           ),
//                                         )),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                         if (!isNoteView)
//                           Padding(
//                             padding: const EdgeInsets.all(10.0),
//                             child: GestureDetector(
//                               onTap: () {
//                                 Navigator.of(context).pop();
//                               },
//                               child: Icon(
//                                 Icons.cancel,
//                                 color: Colors.white,
//                               ),
//                             ),
//                           ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ))).then((value) {
//       currentTrainingProvider?.getTraningDetail();
//     });

//     //
//   }
// }

// class CustomTrackShape extends RoundedRectSliderTrackShape {
//   final movefromleft;

//   CustomTrackShape(this.movefromleft);

//   @override
//   Rect getPreferredRect({
//     required RenderBox parentBox,
//     Offset offset = Offset.zero,
//     required SliderThemeData sliderTheme,
//     bool isEnabled = false,
//     bool isDiscrete = false,
//   }) {
//     final trackHeight = sliderTheme.trackHeight;
//     final trackLeft = offset.dx;
//     final trackTop = offset.dy + (parentBox.size.height - trackHeight!) / 2;
//     final trackWidth = parentBox.size.width * 0.95;
//     return Rect.fromLTWH(
//         trackLeft + movefromleft, trackTop, trackWidth, trackHeight);
//   }
// }
