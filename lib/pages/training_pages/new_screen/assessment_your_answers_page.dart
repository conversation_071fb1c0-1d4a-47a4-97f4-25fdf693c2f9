import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/utils/constant.dart';
import 'package:shimmer/shimmer.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/response/home_response/my_assessment_response.dart';
import '../../../data/models/response/home_response/test_review_response.dart';
import '../../../utils/Styles.dart';
import '../../../utils/resource/colors.dart';
import '../../custom_pages/alert_widgets/alerts_widget.dart';
import '../../custom_pages/custom_widgets/NextPageRouting.dart';
import '../../ghome/home_page.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';

import '../../singularis/competition/competition_detail.dart';

class AssessmentYourAnswersPage extends StatefulWidget {
  final int? contentId;
  final bool isReview;
  final bool isOptionSelected;
  final Function? sendValue;
  final Map<int, bool> attemptList;
  final int? programId;
  final int?  attemptAllowed;
  final bool? isEvent;
  final int? disableBackTracking;

  const AssessmentYourAnswersPage(
      {Key? key,
      this.contentId,
      this.programId,
      required this.isReview,
      required this.isOptionSelected,
      required this.sendValue,
      required this.attemptList,
      this.attemptAllowed,
      this.isEvent,
      this.disableBackTracking})
      : super(key: key);

  @override
  State<AssessmentYourAnswersPage> createState() =>
      _AssessmentYourAnswersPageState();
}

class _AssessmentYourAnswersPageState extends State<AssessmentYourAnswersPage> {
  List<AssessmentList>? assessmentList = [];
  var _isLoading = true;
  late HomeBloc _authBloc;
  List<TestReviewBean> _list = [];
  int? _currentQuestionId;
  bool _showSubmitDialog = false;
  String selectedOption = '';
  List selectedOptionList = [];

  @override
  void initState() {
    super.initState();
  }

  void _handleAttemptTestResponse(ReviewTestState state) {
    try {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          this.setState(() {
            _isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          if (state.response!.data != null) {
            _list.clear();
            selectedOptionList.clear();
            for (int i = 0;
                i < state.response!.data!.assessmentReview!.questions!.length;
                i++) {
              _list.add(
                TestReviewBean(
                    question:
                        state.response!.data!.assessmentReview!.questions![i],
                    id: state.response!.data!.assessmentReview!.questions![i]
                        .questionId,
                    title: state.response!.data!.assessmentReview!.questions![i]
                        .question),
              );
            }

            if (_list.length > 0) {
              _currentQuestionId = _list.first.question!.questionId;
            }
          }
          this.setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.ERROR:
          this.setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    } catch (e) {}
  }

  void _submitAnswers() {
    _authBloc.add(SubmitAnswerEvent(request: widget.contentId.toString()));
  }

  void _handleSubmitAnswerResponse(SubmitAnswerState state) {
    try {
      setState(() {
        switch (state.apiState) {
          case ApiStatus.LOADING:
            _isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            _isLoading = false;
            setState(() {});

            AlertsWidget.alertWithOkBtn(
              context: context,
              onOkClick: () {
                if(widget.attemptAllowed == 1){
                  Navigator.pop(context);
                  Navigator.pop(context);
                  Navigator.pop(context, 1);
                  /*Navigator.pushReplacement(context,
                      MaterialPageRoute(builder: (context) => CompetitionDetail(
                        competitionId: widget.programId,
                        isEvent: widget.isEvent,
                      )));*/

                }else{
                  Navigator.pop(context);
                  Navigator.pop(context);
                }

              },
              text: "${tr('app_assessment_submit_one')}",
            );
            break;
          case ApiStatus.ERROR:
            _isLoading = false;
            setState(() {});
            widget.sendValue!(false);
            Navigator.pop(context);
            _isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return new WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        backgroundColor: ColorConstants.WHITE,
        appBar: AppBar(
          title: Text('your_answer', style: Styles.bold(size: 18),).tr(),
          centerTitle: true,
          backgroundColor: ColorConstants.WHITE,
          elevation: 0.0,
          leading: widget.disableBackTracking == 1 ? SizedBox() : IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () {
              //Navigator.pop(context);
            },
          ),
        ),
        bottomNavigationBar: BottomAppBar(
          height: 140,
          elevation: 0,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 30.0, top: 10.0, right: 30.0, bottom: 10.0),
            child: Container(
              height: 100,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    child: Row(
                      children: [
                        Container(
                          height: 20,
                          width: 20,
                          decoration: BoxDecoration(
                            color: ColorConstants().primaryColorAlways(),
                            borderRadius: BorderRadius.all(Radius.circular(100)),
                          ),
                        ),
                        Text(' ${tr('answers')}', style: Styles.regular(size: 13),),
                        SizedBox(
                          width: 20,
                        ),
                        Container(
                          height: 20,
                          width: 20,
                          decoration: BoxDecoration(
                            color: ColorConstants.GREY_3,
                            borderRadius: BorderRadius.all(Radius.circular(100)),
                          ),
                        ),
                        Text(' ${tr('skipped')}', style: Styles.regular(size: 13),),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  InkWell(
                    onTap: () {
                      if (widget.isReview) {
                        Navigator.pushAndRemoveUntil(
                            context, NextPageRoute(homePage()), (route) => false);
                      } else {
                        _submitAnswers();
                      }
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(colors: [
                          ColorConstants().gradientLeft(),
                          ColorConstants().gradientRight(),
                        ]),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      child: Center(
                          child: Text(
                            'submit_test',
                            style: Styles.textRegular(
                                size: 16,
                                color: ColorConstants().primaryForgroundColor()),
                          ).tr()),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        body: ScreenWithLoader(
          isLoading: _isLoading,
          body: _mainBody(),
        ),
      ),
    );
  }

  _mainBody() {
    _authBloc = BlocProvider.of<HomeBloc>(context);
    return BlocManager(
        initState: (context) async{
          await Future.delayed(Duration(seconds: 5));
          if (_list.length == 0)
            _authBloc.add(ReviewTestEvent(request: '${widget.contentId}?program_id=${widget.programId}'),
            );
        },
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is ReviewTestState) _handleAttemptTestResponse(state);
            if (state is SubmitAnswerState) _handleSubmitAnswerResponse(state);
          },
          child: _list.isNotEmpty
              ? GridView.builder(
            gridDelegate:
            SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4),
            itemCount: widget.attemptList.entries.length,
            itemBuilder: (context, index) {
              var element =
              [...widget.attemptList.entries.toList()][index];

              return Container(
                width: height(context) * 0.1,
                height: height(context) * 0.1,
                margin: EdgeInsets.all(4),
                decoration: BoxDecoration(
                    color: element.value == false
                        ? ColorConstants.GREY_3
                        : ColorConstants().primaryColorAlways(),
                    borderRadius: BorderRadius.circular(8)),
                child: Padding(
                  padding: EdgeInsets.all(10),
                  child: Center(
                    child: Text(
                      '${widget.attemptList.keys.toList().indexOf(element.key) + 1}',
                      style: Styles.textRegular(
                          size: 16, color: Colors.white),
                    ),
                  ),
                ),
              );
            },
          )

              : _emptyBody(),
        ));
  }

  _emptyBody() {
    return Container(
      child: GridView.builder(
        padding: EdgeInsets.only(left: 10.0, right: 10.0, top: 30.0),
        gridDelegate:
            const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 5),
        itemCount: 10,
        itemBuilder: (BuildContext context, int index) {
          return Container(
            child: Shimmer.fromColors(
              baseColor: Colors.grey,
              highlightColor: ColorConstants.GREY_4,
              child: Card(
                color: ColorConstants.GREY_3,
              ),
            ),
          );
        },
      ),
    );
  }
}
