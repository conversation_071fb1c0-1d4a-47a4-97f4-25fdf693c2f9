import 'dart:io';
import 'dart:isolate';
import 'dart:ui';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/assignment_submissions_response.dart';
import 'package:masterg/pages/announecment_pages/full_video_page.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/video_resume/preview_sample_resume_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/custom_progress_indicator.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../ghome/widget/read_more.dart';

class ReviewSubmissions extends StatefulWidget {
  final int? maxMarks;
  final int? contentId;
  const ReviewSubmissions({Key? key, this.contentId, this.maxMarks})
      : super(key: key);

  @override
  _ReviewSubmissionsState createState() => _ReviewSubmissionsState();
}

class _ReviewSubmissionsState extends State<ReviewSubmissions> {
  late AssessmentDetails data;
  List<SubmissionDetails>? _attempts = [];
  // var _swiperController = SwiperController();
  bool _isLoading = true;

  @override
  void initState() {
    _getData();
    // _downloadListener();
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      // String id = data[0];
      // DownloadTaskStatus status = data[1];
      // int progress = data[2];
      setState(() {});
    });

    //FlutterDownloader.registerCallback(downloadCallback as DownloadCallback);
    FlutterDownloader.registerCallback(downloadCallback);

    super.initState();
  }

  static void downloadCallback(String id, int status, int progress) {
    final SendPort send = IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, status, progress]);
  }

  void _getData() {
    BlocProvider.of<HomeBloc>(context)
        .add(AssignmentSubmissionsEvent(request: widget.contentId));
  }

  void _handleResponse(AssignmentSubmissionsState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");
          data = state.response!.data!.assessmentDetails!.first;
          _attempts =
              state.response!.data!.assessmentDetails!.first.submissionDetails;

          _isLoading = false;
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'assignment_submission', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: ColorConstants.BLACK),
        elevation: 0,
        title: Text(
          'review_submission',
          style: Styles.bold(size: 20),
        ).tr(),
      ),
      body: BlocManager(
        initState: (c) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is AssignmentSubmissionsState) _handleResponse(state);
          },
          child: _buildBody(),
        ),
      ),
    );
  }

  _buildBody() {
    return Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: _isLoading
            ? Center(
                child: CustomProgressIndicator(true, ColorConstants.WHITE),
              )
            : _attempts!.isNotEmpty
                ? SingleChildScrollView(
                    child: Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    child: ListView.builder(
                        itemCount: _attempts?.length,
                        itemBuilder: (BuildContext context, int currentIndex) =>
                            Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 8),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Container(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.6,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                                '${_attempts![currentIndex].file!.split('/').last}',
                                                overflow: TextOverflow.fade,
                                                maxLines: 2,
                                                softWrap: true,
                                                style:
                                                    Styles.regular(size: 14)),
                                            Text(
                                                '${_attempts![currentIndex].createdAt != null ? Utility.convertDateFromMillis(
                                                    _attempts![currentIndex]
                                                        .createdAt!,
                                                    Strings
                                                        .REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS,
                                                  ) : ''}',
                                                style: Styles.regular(
                                                    size: 10,
                                                    color:
                                                        ColorConstants.GREY_3))
                                          ],
                                        ),
                                      ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              InkWell(
                                                onTap: () async {
                                                  downloadAssignment(
                                                      fileUrl: _attempts![
                                                              currentIndex]
                                                          .file);
                                                  // download(
                                                  //     _attempts![currentIndex]
                                                  //         .file);
                                                },
                                                child: ShaderMask(
                                                  blendMode: BlendMode.srcIn,
                                                  shaderCallback:
                                                      (Rect bounds) {
                                                    return LinearGradient(
                                                        begin: Alignment
                                                            .centerLeft,
                                                        end: Alignment
                                                            .centerRight,
                                                        colors: <Color>[
                                                          ColorConstants()
                                                              .gradientLeft(),
                                                          ColorConstants()
                                                              .gradientRight()
                                                        ]).createShader(bounds);
                                                  },
                                                  child: SvgPicture.asset(
                                                    'assets/images/download_icon.svg',
                                                    height: 25,
                                                    width: 25,
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 20),
                                              InkWell(
                                                onTap: () {
                                                  Navigator.push(
                                                      context,
                                                      NextPageRoute(
                                                          FullContentPage(
                                                        contentType: _attempts![
                                                                currentIndex]
                                                            .extension,
                                                        resourcePath: _attempts![
                                                                currentIndex]
                                                            .file,
                                                        title: _attempts![
                                                                currentIndex]
                                                            .title,
                                                      )
                                                          //     PreviewSampleResume(
                                                          //   previewUrl: _attempts![
                                                          //           currentIndex]
                                                          //       .file,
                                                          //   title:
                                                          //       '${_attempts![currentIndex].title ?? ''}',
                                                          //       msg: tr('file_type_msg'),
                                                          // )
                                                          ));
                                                },
                                                child: ShaderMask(
                                                  blendMode: BlendMode.srcIn,
                                                  shaderCallback:
                                                      (Rect bounds) {
                                                    return LinearGradient(
                                                        begin: Alignment
                                                            .centerLeft,
                                                        end: Alignment
                                                            .centerRight,
                                                        colors: <Color>[
                                                          ColorConstants()
                                                              .gradientLeft(),
                                                          ColorConstants()
                                                              .gradientRight()
                                                        ]).createShader(bounds);
                                                  },
                                                  child: SvgPicture.asset(
                                                    'assets/images/view_icon.svg',
                                                    height: 25,
                                                    width: 25,
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  ),
                                                ),
                                              )
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Row(
                                            children: [
                                              Container(
                                                child: data
                                                            .submissionDetails![
                                                                currentIndex]
                                                            .reviewStatus ==
                                                        0
                                                    ? Text(
                                                        'under_review',
                                                      ).tr()
                                                    : Text(
                                                        data.isGraded == 0
                                                            ? "${tr('non_graded')} "
                                                            : "${data.submissionDetails![currentIndex].marksObtained ?? 0}/${widget.maxMarks}",
                                                        maxLines: 2,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        softWrap: true,
                                                        style: Styles.bold(
                                                            size: 12,
                                                            color: data.isGraded ==
                                                                    0
                                                                ? ColorConstants
                                                                    .BLACK
                                                                : ColorConstants
                                                                    .GREEN),
                                                      ),
                                              ),
                                              SizedBox(width: 6),
                                              SvgPicture.asset(
                                                'assets/images/info.svg',
                                                height: 14,
                                                width: 14,
                                                allowDrawingOutsideViewBox:
                                                    true,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                if (_attempts![currentIndex].teacherNotes != null)
                                  SizedBox(
                                    height: 4,
                                  ),
                                if (_attempts![currentIndex].teacherNotes != null)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 18),
                                    child: ReadMoreText(
                                      text: '${_attempts![currentIndex].teacherNotes}',
                                      color: ColorConstants.PRIMARY_COLOR,
                                      viewMore: tr('view_more'),
                                    ),
                                    /*child: Text(
                                      '${_attempts![currentIndex].teacherNotes}',
                                      style: Styles.textItalic(
                                          size: 14, color: Color(0xff5A5F73)),
                                    ),*/
                                  ),
                              ],
                            )),
                  ))
                : Center(
                    child: Text(
                      'no_assignment_submitted',
                      style: Styles.textBold(),
                    ).tr(),
                  ));
  }

  Widget kPadding({required Widget child}) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
        child: child);
  }

  ReceivePort _port = ReceivePort();

  // _downloadListener() {
  //   IsolateNameServer.registerPortWithName(
  //       _port.sendPort, 'downloader_send_port');
  //   _port.listen((dynamic data) async {
  //     String? id = data[0];
  //     DownloadTaskStatus? status = data[1];
  //     int? progress = data[2];
  //     if (status == DownloadTaskStatus.complete &&
  //         progress == 100 &&
  //         id != null) {
  //       String query = "SELECT * FROM task WHERE task_id='" + id + "'";
  //       var tasks = await FlutterDownloader.loadTasksWithRawQuery(query: query);
  //       //if the task exists, open it
  //       if (tasks != null) {
  //         Future.delayed(Duration(seconds: 2), () {
  //           FlutterDownloader.open(taskId: tasks.first.taskId);
  //         });
  //       }
  //     }
  //   });
  //   // FlutterDownloader.registerCallback(downloadCallback);
  // }

  Future<void> downloadAssignment({String? fileUrl}) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }

    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS ||
        status.isGranted ||
        android.version.sdkInt >= 33 ||
        await Permission.storage.request().isGranted) {
      final status = await Permission.storage.status;

      if (Platform.isAndroid) {
        localPath = "/sdcard/download/";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }

      final file = File("$localPath/${fileUrl!.split('/').last}");
      // final fileName = fileUrl.split('/').last;
      // final file = File("$localPath/$fileName");
      bool downloadSuccess = false;

      if (!file.existsSync()) {
        Log.v('file url is2 $file');

        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('Downloading Start'),
        ));

        final id = await FlutterDownloader.enqueue(
          url: fileUrl,
          savedDir: localPath,
          showNotification: true,
          openFileFromNotification: true,
          saveInPublicStorage: true,
        ).then((value) async {
          downloadSuccess = true;
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Successfully Downloaded'),
          ));

          OpenFilex.open("$localPath/${fileUrl.split('/').last}");
        });
      } else if (file.existsSync()) {
        Utility.showSnackBar(
            scaffoldContext: context, message: 'file already exists');
        OpenFilex.open("$localPath/${fileUrl.split('/').last}");
      }
      if (file.existsSync()) {
        Utility.showSnackBar(
            scaffoldContext: context, message: 'file already exists');
      }
    } else {
      launchUrl(Uri.parse(fileUrl!), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
    return;
  }
}
