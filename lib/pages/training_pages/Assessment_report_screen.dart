import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/assessmentReportResp.dart';

import 'package:masterg/data/providers/mg_assessment_detail_provioder.dart';
import 'package:masterg/local/pref/Preference.dart';

import 'package:masterg/main.dart';

import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';

import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';

import 'package:masterg/pages/onboarding_pages/onboarding_select_intreset.dart';
import 'package:masterg/pages/training_pages/new_screen/assessment_attempt_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class AssessmentReportScreen extends StatefulWidget {
  final String? score;
  final String? diffLevel;
  final String? attemptsAllow;
  final String? dateTime;
  final String? fieldName;
  final String? attemptCount;
  final String? maxMarks;
  final int? assessmentId;
  final int? displayScorecard;
  final MgAssessmentDetailProvider assessmentDetailProvider;

  const AssessmentReportScreen(
      {super.key,
      required this.score,
      required this.diffLevel,
      required this.attemptsAllow,
      required this.dateTime,
      required this.fieldName,
      this.attemptCount,
      this.maxMarks,
      required this.assessmentDetailProvider,
        this.displayScorecard,
      required this.assessmentId,});

  @override
  State<AssessmentReportScreen> createState() => _AssessmentReportScreenState();
}

class _AssessmentReportScreenState extends State<AssessmentReportScreen> {
  bool? isLoading = false;
  bool? isLearningPath = true;
  bool? isInterestArea = false;

  AssessmentReportResp? assessmentReport;
  List<SkillCategory>? skillCategories;
  String? fitmentScore;
  List<String> splitText = [];
  List<String> splitTextAreas = [];
  GlobalKey key = GlobalKey();

  @override
  void initState() {
    super.initState();
    getAssessmentReport(assessmentId: widget.assessmentId);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) {
        if (state is AssessmentReportState) {
          _handleAssessmentReportState(state);
        }
      },
      child: Scaffold(
          appBar: AppBar(
              elevation: 0,
              backgroundColor: ColorConstants.WHITE,
              leading: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(Icons.arrow_back, color: ColorConstants.BLACK)),
              title: Text('assessment_report', style: Styles.bold()).tr()),
          body: ScreenWithLoader(
            isLoading: isLoading,
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    logisticProfession(),
                    SizedBox(height: 20),
                    fitmentAssessment(context, widget.assessmentDetailProvider,
                        currentIndiaTime!),
                    if (assessmentReport?.data?.skillCategories?.isNotEmpty ==
                        true) ...[
                      SizedBox(height: 30),
                      Padding(
                        padding: const EdgeInsets.only(left: 2.0),
                        child: Row(
                          children: [
                            Text('assessment_analysis', style: Styles.bold())
                                .tr(),
                            InkWell(
                              onTap: () {
                                openPopupFor();
                              },
                              child: Icon(Icons.info),
                            ),
                          ],
                        ),
                      ),
                      assessmentAnalysis(),
                      
                    ],
                    SizedBox(height: 20),

                    summaryCard(),
                    //main()
                  ],
                ),
              ),
            ),
          )),
    );
  }

  openPopupFor() {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('assessment_analysis').tr(),
          content: barColorDefine(),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('close').tr(),
            ),
          ],
        );
      },
    );
  }

  barColorDefine() {
    return Container(
      width: double.infinity,
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: const Radius.circular(
            20,
          ),
          topRight: const Radius.circular(20),
          bottomRight: Radius.circular(20),
          topLeft: Radius.circular(20),
        ),
        //color: Color(0xffD9F6FF)
      ),
      child: Column(
        children: [
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(6)),
                          color: Colors.red),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text('0% - 25%', style: Styles.regular(size: 12)),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Row(
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: 23.0),
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(6)),
                          color: Colors.yellow),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child:
                          Text('25.01% - 50%', style: Styles.regular(size: 12)),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(6)),
                          color: Colors.orange),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child:
                          Text('50.01% - 75%', style: Styles.regular(size: 12)),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(6)),
                          color: ColorConstants.PRIMARY_COLOR),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text('75.01% - 100%',
                          style: Styles.regular(size: 12)),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  fitmentAssessment(context,
      MgAssessmentDetailProvider assessmentDetailProvider, DateTime value) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            bottomLeft: const Radius.circular(
              20,
            ),
            topRight: const Radius.circular(20),
            bottomRight: Radius.circular(20),
            topLeft: Radius.circular(20),
          ),
          color: ColorConstants.GREY),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text('${widget.fieldName ?? ''}', style: Styles.bold()),
          SizedBox(height: 5),
        widget.displayScorecard == 1 ? RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: tr('your_score'),
                  style: Styles.regular(size: 12),
                ),
                TextSpan(
                    text: '${widget.score ?? ''}',
                    style: Styles.bold(size: 12)),
                TextSpan(
                    text: '/${widget.maxMarks ?? ''}',
                    style: Styles.regular(size: 12)),
              ],
            ),
          ):SizedBox(),
          SizedBox(
            height: 30,
            child: Row(children: [
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: tr('difficulty_level') + ': ',
                      style: Styles.regular(size: 12),
                    ),
                    TextSpan(
                      text:
                          widget.diffLevel != 'null' ? widget.diffLevel : 'N/A',
                      style: Styles.bold(color: Colors.green, size: 12),
                    )
                  ],
                ),
              ),
              Spacer(),
              Expanded(
                child: TapWidget(
                  onTap: () async {
                    switch (Utility.classStatus(
                        assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.startDate!,
                        assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.endDate!,
                        value)) {
                      case 1:
                        AlertsWidget.showCustomDialog(
                            context: context,
                            title: tr('assessment_not_ready_submission'),
                            text: "",
                            icon: 'assets/images/circle_alert_fill.svg',
                            showCancel: false,
                            oKText: '${tr('ok')}',
                            onOkClick: () async {});
                        return;

                      case 2:
                        AlertsWidget.showCustomDialog(
                            context: context,
                            title: tr('due_data_passed'),
                            text: "",
                            icon: 'assets/images/circle_alert_fill.svg',
                            showCancel: false,
                            oKText: '${tr('ok')}',
                            onOkClick: () async {});
                        return;
                    }

                    if (assessmentDetailProvider.assessmentResponse!.data!
                                .instruction!.details!.attemptAllowed! !=
                            0 &&
                        assessmentDetailProvider.assessmentResponse!.data!
                                .instruction!.details!.attemptCount! >=
                            assessmentDetailProvider.assessmentResponse!.data!
                                .instruction!.details!.attemptAllowed!) {
                      Utility.showSnackBar(
                          scaffoldContext: context,
                          message: tr('maximum_attempts_reached'));
                    } else {
                      if (assessmentDetailProvider.assessmentResponse!.data!
                                  .instruction!.details!.isPassed ==
                              1 &&
                          assessmentDetailProvider.assessmentResponse!.data!
                                  .instruction!.details!.allowAfterPassing ==
                              0) {
                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text('assessment_passed').tr(),
                        ));

                        return;
                      }
                      AlertsWidget.showCustomDialog(
                          context: context!,
                          title: tr('confirm'),
                          text: tr('assessment_attempt_confirm'),
                          icon: 'assets/images/circle_alert_fill.svg',
                          onCancelClick: () {},
                          onOkClick: () async {
                            await Navigator.push(
                                context!,
                                NextPageRoute(
                                  AssessmentAttemptPage(
                                      contentId: assessmentDetailProvider
                                          .assessments.contentId,
                                      isVideoTypeQuiz: assessmentDetailProvider
                                              .assessmentResponse!
                                              .data!
                                              .instruction!
                                              .details!
                                              .quizType !=
                                          'text',
                                      programId: assessmentDetailProvider
                                          .assessments.program),
                                ));
                            assessmentDetailProvider.getDetails();
                          });
                    }
                  },
                  child: Container(
                    height: 30,
                    width: 80,
                    decoration: BoxDecoration(
                      gradient: assessmentDetailProvider.assessmentResponse!
                                      .data!.instruction!.details!.isPassed ==
                                  1 &&
                              assessmentDetailProvider
                                      .assessmentResponse!
                                      .data!
                                      .instruction!
                                      .details!
                                      .allowAfterPassing ==
                                  0
                          ? LinearGradient(colors: [
                              ColorConstants.GREY_3,
                              ColorConstants.GREY_3,
                            ])
                          : LinearGradient(colors: [
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight(),
                            ]),
                      color: (assessmentDetailProvider.assessmentResponse!.data!
                                  .instruction!.details!.attemptCount! >=
                              assessmentDetailProvider.assessmentResponse!.data!
                                  .instruction!.details!.attemptAllowed!)
                          ? ColorConstants.GREY_3
                          : ColorConstants().gradientRight(),
                      borderRadius: BorderRadius.all(Radius.circular(5)),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: 8, right: 8, top: 4, bottom: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            assessmentDetailProvider.assessmentResponse!.data!
                                        .instruction!.details!.attemptCount! >
                                    0
                                ? 'reattempt'
                                : 'attempt',
                            style: Styles.textExtraBold(
                                size: 12,
                                color:
                                    ColorConstants().primaryForgroundColor()),
                          ).tr(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ]),
          ),
          SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              //${tr('marks')} ${assessmentDetailProvider.assessments.maximumMarks}  • '
              Text(
                tr('attempts') + ': ',
                style: Styles.regular(size: 12, color: ColorConstants.BLACK),
              ),
              Text(
                assessmentDetailProvider.assessmentResponse!.data!.instruction!
                            .details!.attemptAllowed ==
                        0
                    ? tr('unlimited_attempt')
                    : ' ${widget.attemptCount} ' + tr('attempt_available'),
                style: Styles.semibold(size: 12, color: ColorConstants.BLACK),
              )
            ],
          ),
          SizedBox(height: 5),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: tr('date_time_attempt'),
                  style: Styles.regular(size: 12),
                ),
                TextSpan(
                    text: '${widget.dateTime ?? ''}',
                    style: Styles.bold(size: 12)),
              ],
            ),
          ),
          SizedBox(height: 5),
          Preference.getString(Preference.SAVE_INTEREST) != null
              ? RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Industry/Field : ',
                        style: Styles.regular(size: 12),
                      ),
                      TextSpan(
                          text:
                              '${Preference.getString(Preference.SAVE_INTEREST) ?? ''}',
                          style: Styles.bold(size: 12)),
                    ],
                  ),
                )
              : SizedBox(),
        ]),
      ),
    );
  }

  logisticProfession() {
    // MenuListProvider menuProvider = Provider.of(context);
    return Container(
      //height: 270,
      width: double.infinity,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            bottomLeft: const Radius.circular(
              20,
            ),
            topRight: const Radius.circular(20),
            bottomRight: Radius.circular(20),
            topLeft: Radius.circular(20),
          ),
          color: Color(0xffD9F6FF)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            children: [
              //Text('${Preference.getString(Preference.SAVE_INTEREST)}'),
              SizedBox(
                width: 200,
                child: Text(
                    '${Preference.getString(Preference.SETUP_GOAL) ?? ''} Profession',
                    style: Styles.bold(lineHeight: 1)),
              ),
              Spacer(),
              Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: <Widget>[
                    SizedBox(
                      height: 60,
                      width: 60,
                      child: CircularProgressIndicator(
                        strokeWidth: 8.0,
                        value: (assessmentReport
                                    ?.data?.allignmentPer?.percentage !=
                                null)
                            ? (assessmentReport
                                        ?.data?.allignmentPer?.percentage ??
                                    0) /
                                100
                            : 0,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                            ColorConstants.PRIMARY_COLOR),
                      ),
                    ),
                    Column(
                      children: [
                        Text(
                          assessmentReport?.data?.allignmentPer?.percentage !=
                                  null
                              ? '${assessmentReport?.data?.allignmentPer?.percentage?.toDouble().toStringAsFixed(1) ?? ''}%'
                              : '',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w700,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          //SizedBox(height: 8),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  //text: 'Based on your assessment scores your overall Fitment\n towards ${Preference.getString(Preference.SAVE_INTEREST)?? ''} is ',
                  text: tr('based_your_scores') +
                      '${Preference.getString(Preference.SETUP_GOAL) ?? ''} is ',
                  style: Styles.regular(size: 11),
                ),
                assessmentReport?.data?.allignmentPer?.percentage != null
                    ? TextSpan(
                        text:
                            '${assessmentReport?.data?.allignmentPer?.percentage?.toDouble().toStringAsFixed(1) ?? ''}%',
                        style: Styles.bold(size: 11))
                    : TextSpan(text: ''),
              ],
            ),
          ),
        ]),
      ),
    );
  }

  assessmentAnalysis() {
    Color? determineColor(double? fitmentScore) {
      if (fitmentScore == null) {
        return ColorConstants.WHITE;
      }
      if (fitmentScore <= 25) {
        return Color.fromARGB(255, 231, 20, 5);
      }
      if (fitmentScore >= 25 && fitmentScore <= 50) {
        return Color(0xffEAB600);
      }
      if (fitmentScore <= 50 || fitmentScore <= 75) {
        return Color.fromARGB(255, 233, 103, 4);
      }
      if (fitmentScore >= 75 || fitmentScore <= 100) {
        return ColorConstants.PRIMARY_COLOR;
      }
    }

    return assessmentReport?.data?.skillCategories?.length != 0 &&
            assessmentReport?.data?.skillCategories?.isNotEmpty == true
        ? ListView.builder(
            itemCount: assessmentReport?.data?.skillCategories?.length ?? 0,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: ((context, categoryIndex) {
              // Extracting skills data
              var skills = assessmentReport
                  ?.data?.skillCategories?[categoryIndex].skills;

              // Initialize variables for sum of fitment scores and total possible score
              var totalFitmentScore = 0.0;
              var totalPossibleScore = 0;

              // Calculating total fitment score and total possible score
              skills?.forEach((skill) {
                totalFitmentScore += skill.fitmentScore ?? 0;
                // totalFitmentScore = totalFitmentScore/ int.parse('${assessmentReport?.data?.skillCategories?[index].skills?.length}');
                //totalPossibleScore += int.parse(skill.totScore ?? 0);
                totalPossibleScore += skill.totScore ?? 0;
              });

              // Calculating the percentage
              var percentage = totalFitmentScore / totalPossibleScore * 100;
              return Padding(
                padding: const EdgeInsets.all(0.0),
                child: Container(
                    margin: EdgeInsets.symmetric(vertical: 8),
                    //  height: MediaQuery.of(context).size.height * 0.6,
                    width: double.infinity,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          bottomLeft: const Radius.circular(
                            10,
                          ),
                          topRight: const Radius.circular(10),
                          bottomRight: Radius.circular(10),
                          topLeft: Radius.circular(10),
                        ),
                        color: ColorConstants.GREY),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                                '${assessmentReport?.data?.skillCategories?[categoryIndex].scName}',
                                style: Styles.regular(size: 14)),
                          ),
                          SizedBox(height: 5),
                          Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8.0),
                              child: Text(
                                  '${assessmentReport?.data?.skillCategories?[categoryIndex].scFitmentScore}%',
                                  style: Styles.bold(size: 24))),
                          SizedBox(
                            // height: MediaQuery.of(context).size.height,
                            child: ListView.builder(
                                itemCount: assessmentReport
                                        ?.data
                                        ?.skillCategories?[categoryIndex]
                                        .skills
                                        ?.length ??
                                    0,
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemBuilder: ((context, index) {
                                  return Column(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0),
                                        child: assessmentReport
                                                        ?.data
                                                        ?.skillCategories
                                                        ?.length !=
                                                    0 &&
                                                assessmentReport
                                                        ?.data
                                                        ?.skillCategories
                                                        ?.isNotEmpty ==
                                                    true
                                            ? Container(
                                                width: MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                margin:
                                                    EdgeInsets.only(top: 8.0),
                                                decoration: BoxDecoration(
                                                    // color: ColorConstants.BLACK.withOpacity(0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10)),
                                                child: Stack(
                                                  children: [
                                                    Column(
                                                      children: [
                                                        Row(
                                                          children: [
                                                            SizedBox(
                                                              width: 250,
                                                              child: Text(
                                                                  '${assessmentReport?.data?.skillCategories?[categoryIndex].skills?[index].subSkill ?? ''}',
                                                                  style: Styles.regular(
                                                                      size: 14,
                                                                      color: ColorConstants
                                                                          .BLACK),
                                                                  maxLines: 1,
                                                                  softWrap:
                                                                      true,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis),
                                                            ),
                                                            Spacer(),
                                                            Text(
                                                                '${assessmentReport?.data?.skillCategories?[categoryIndex].skills?[index].fitmentScore?.toStringAsFixed(0)}%',
                                                                style:
                                                                    Styles.bold(
                                                                        size:
                                                                            14))
                                                          ],
                                                        ),
                                                        SizedBox(height: 10),
                                                        ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .horizontal(
                                                            left:
                                                                Radius.circular(
                                                                    10),
                                                            right:
                                                                Radius.circular(
                                                                    10),
                                                          ),
                                                          child:
                                                              LinearProgressIndicator(
                                                            minHeight: 6,
                                                            value: (assessmentReport
                                                                        ?.data
                                                                        ?.skillCategories?[
                                                                            categoryIndex]
                                                                        .skills?[
                                                                            index]
                                                                        .fitmentScore ??
                                                                    0) /
                                                                100,
                                                            backgroundColor:
                                                                Colors
                                                                    .grey[300],
                                                            valueColor:
                                                                AlwaysStoppedAnimation<
                                                                    Color>(
                                                              determineColor(assessmentReport
                                                                      ?.data
                                                                      ?.skillCategories?[
                                                                          categoryIndex]
                                                                      .skills?[
                                                                          index]
                                                                      .fitmentScore) ??
                                                                  ColorConstants
                                                                      .WHITE,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              )
                                            : Container(
                                                width: width(context),
                                                height: width(context) *
                                                    0.9251870324189526,
                                                child: Shimmer.fromColors(
                                                  baseColor: Colors.grey[300]!,
                                                  highlightColor:
                                                      Colors.grey[100]!,
                                                  enabled: true,
                                                  child: Container(
                                                    margin:
                                                        const EdgeInsets.all(
                                                            10),
                                                    decoration: BoxDecoration(
                                                        color: ColorConstants
                                                            .WHITE,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(20)),
                                                    width: 100,
                                                    height: 13,
                                                    child: ClipRRect(
                                                      borderRadius: BorderRadius
                                                          .horizontal(
                                                        left:
                                                            Radius.circular(10),
                                                        right:
                                                            Radius.circular(10),
                                                      ),
                                                      child:
                                                          LinearProgressIndicator(
                                                        minHeight: 10,
                                                        backgroundColor:
                                                            Colors.grey[300],
                                                        valueColor:
                                                            AlwaysStoppedAnimation<
                                                                    Color>(
                                                                ColorConstants
                                                                    .GREY),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                      ),
                                    ],
                                  );
                                })),
                          )
                        ],
                      ),
                    )),
              );
            }),
          )
        : Container(
            width: width(context),
            height: width(context) * 0.9251870324189526,
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              enabled: true,
              child: Container(
                margin: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: ColorConstants.WHITE,
                    borderRadius: BorderRadius.circular(20)),
                width: 100,
                height: 13,
                child: ClipRRect(
                  borderRadius: BorderRadius.horizontal(
                    left: Radius.circular(10),
                    right: Radius.circular(10),
                  ),
                  child: LinearProgressIndicator(
                    minHeight: 10,
                    backgroundColor: Colors.grey[300],
                    valueColor:
                        AlwaysStoppedAnimation<Color>(ColorConstants.GREY),
                  ),
                ),
              ),
            ),
          );
  }

  summaryCard() {
    if (assessmentReport?.data?.summary?.strengths != null &&
        assessmentReport?.data?.summary?.strengths != '')
      splitText = '${assessmentReport?.data?.summary?.strengths}'.split(',');

    if (assessmentReport?.data?.summary?.areaOfImprovement != '' &&
        assessmentReport?.data?.summary?.areaOfImprovement != null)
      splitTextAreas =
          '${assessmentReport?.data?.summary?.areaOfImprovement}'.split(',');

    return Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: const Radius.circular(
                10,
              ),
              topRight: const Radius.circular(10),
              bottomRight: Radius.circular(10),
              topLeft: Radius.circular(10),
            ),
            color: ColorConstants.GREY),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('summary', style: Styles.regular(size: 14)).tr(),
              SizedBox(height: 5),
              if (assessmentReport?.data?.summary?.strengths != null &&
                  assessmentReport?.data?.summary?.strengths != "") ...[
                Text('strengths', style: Styles.regular(size: 12)).tr(),
                SizedBox(height: 5),
                for (String part in splitText)
                  Text(part, style: Styles.bold(size: 12)),
              ],
              SizedBox(height: 15),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (assessmentReport?.data?.summary?.areaOfImprovement !=
                          '' &&
                      assessmentReport?.data?.summary?.areaOfImprovement !=
                          null)
                    Text('areas_for_improvement',
                            style: Styles.regular(size: 12))
                        .tr(),
                  SizedBox(height: 5),
                  // Text('${assessmentReport?.data?.summary?.areaOfImprovement ?? ''}',
                  //     style: Styles.semibold(size: 12)),

                  for (String partAreas in splitTextAreas)
                    Text(partAreas, style: Styles.semibold(size: 12)),
                ],
              ),
              SizedBox(height: 15),
              Text('remarks', style: Styles.regular(size: 12)).tr(),
              SizedBox(height: 5),
              SizedBox(
                child: ListView.builder(
                    itemCount:
                        assessmentReport?.data?.summary?.remarks?.length ?? 0,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: ((context, index) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(
                                '• ${assessmentReport?.data?.summary?.remarks?[index]}',
                                style: Styles.semibold(
                                  size: 12,
                                )),
                          ),
                        ],
                      );
                    })),
              )
            ],
          ),
        ));
  }

  void getAssessmentReport({int? assessmentId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(AssessmentReportEvent(assessmentId: assessmentId));
  }

  void _handleAssessmentReportState(AssessmentReportState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Assessment Report Loading.................");
          isLoading = true;
          print('Assessment Report event 2');
          break;
        case ApiStatus.SUCCESS:
          print('Assessment Report');
          Log.v(
              "Assessment Report Success ................... ${state.response}");
          try {
            if (state.response != null && state.response != null) {
              assessmentReport = state.response;
              skillCategories = state.response?.data?.skillCategories;
            } else {}
          } catch (e, stackTrace) {
            debugPrint('$stackTrace');
            setState(() {
              isLoading = false;
            });
          }
          isLoading = false;
          break;

        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("Assessment Report state Error..........................");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
