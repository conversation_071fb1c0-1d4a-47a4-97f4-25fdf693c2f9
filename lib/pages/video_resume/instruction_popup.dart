import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

enum ResumeKeyStatus { loading, complete, error }

class InstructionPopup extends StatelessWidget {
  final Function onContinue;
  const InstructionPopup({Key? key, required this.onContinue})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          // color: ColorConstants.WHITE,
          body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            SizedBox(
                width: 330,
                child: Text(
                  'resume_key_elements',
                  style: TextStyle(
                    color: Color(0xFF222631),
                    fontSize: 17,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ).tr()),
            _size(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: SizedBox(
                  width: 330,
                  child: Text(
                    'resume_element_desc',
                    style: TextStyle(
                      color: Color(0xFF222631),
                      fontSize: 14,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ).tr()),
            ),
            _size(height: 20),
            Text(
              'mandatory',
              style: TextStyle(
                color: Color(0xFF727C95),
                fontSize: 14,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
              ),
            ).tr(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                children: [
                  _size(height: 4),
                  _rowItem('name'),
                  _size(height: 4),
                  _rowItem('email_address'),
                  //_size(height: 4),
                  //_rowItem('professional'),
                  _size(height: 4),
                  _rowItem('phone_number'),
                  _size(height: 4),
                  _rowItem('address'),
                  _size(height: 4),
                  _rowItem('resume_objective'),
                  _size(height: 4),
                  _rowItem('education_qualifications'),
                  _size(height: 4),
                  _rowItem('professional_experience'),
                  _size(height: 4),
                  _rowItem('skills_abilities'),
                  _size(height: 20),
                ],
              ),
            ),
            Text(
              'optional',
              style: TextStyle(
                color: Color(0xFF727C95),
                fontSize: 14,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
              ),
            ).tr(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                children: [
                  _size(height: 10),
                  _rowItem('achievements_awards'),
                  _size(height: 10),
                  _rowItem('certifications'),
                  _size(height: 10),
                  _rowItem('publications'),
                  _size(height: 10),
                  _rowItem('languages'),
                  _size(height: 40),
                ],
              ),
            ),
            Text(
              'ensure_english_video',
              style: Styles.bold(),
              textAlign: TextAlign.center,
            ).tr(),
            _size(height: 10),
            InkWell(
              onTap: () {
                onContinue();
              },
              child: Container(
                width: double.infinity,
                height: 50,
                padding: const EdgeInsets.all(16),
                decoration: ShapeDecoration(
                  color: ColorConstants().primaryColorbtnAlways(),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'continue',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                            ),
                          ).tr()
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          ]),
        ),
      )),
    );
  }

  _rowItem(String title) {
    return Row(children: [
      Text('•',
          style: Styles.semibold(
              lineHeight: 1.4,
              color: Color.fromARGB(255, 79, 79, 81),
              size: 15)),
      _size(width: 10),
      Text(title,
              style: Styles.semibold(
                  lineHeight: 1.4,
                  color: Color.fromARGB(255, 79, 79, 81),
                  size: 15))
          .tr()
    ]);
  }

  _size({double height = 20, double width = 0}) {
    return SizedBox(
      height: height,
      width: width,
    );
  }
}
