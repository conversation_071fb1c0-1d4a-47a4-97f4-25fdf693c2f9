import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/pages/video_resume/refresh_notifier.dart';
import 'package:masterg/utils/infinite_rotate.dart';
import 'package:provider/provider.dart';

class RefreshButton extends StatefulWidget {
  final Function() onRefresh;
  final int index;
  const RefreshButton({Key? key, required this.onRefresh, required this.index})
      : super(key: key);

  @override
  _RefreshButtonState createState() => _RefreshButtonState();
}

class _RefreshButtonState extends State<RefreshButton> {
  late Timer _timer;
  int _start = 10;
  bool showFakeLoader = false;

  void startTimer() {
    const oneSec = const Duration(seconds: 1);
    _timer = new Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_start == 0) {
          setState(() {
            timer.cancel();
          });
        } else {
          setState(() {
            _start--;
          });
        }
      },
    );
  }

  @override
  void initState() {
    startTimer();
    super.initState();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RefreshNotifier>(
      builder: ((context, value, child) => InkWell(
          onTap: () async {
            print("timer is ${_timer.isActive}");
            if (_timer.isActive == false) {
              widget.onRefresh();
              _start = 10;
              startTimer();
            } else {
              showFakeLoader = true;
              setState(() {});
              await Future.delayed(Duration(seconds: 2));
              showFakeLoader = false;
              setState(() {});
            }
          },
          child: Padding(
            // color: Colors.red,
            padding:
                const EdgeInsets.only(left: 6, right: 18, top: 10, bottom: 10),
            child: showFakeLoader == true ||
                    value.isRefreshingList[widget.index] == true
                ? InfiniteRotate(child: Icon(Icons.autorenew))
                : Icon(Icons.autorenew),
          ))),
    );
  }
}
