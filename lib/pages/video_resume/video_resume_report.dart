import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/models/response/home_response/analyse_video_resume_resp.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

import '../../blocs/home_bloc.dart';
import '../../data/api/api_service.dart';
import '../../utils/Log.dart';
import '../../utils/Styles.dart';
import '../explore_job/explore_job_list_page.dart';
import 'instruction_custom_widget.dart';
import 'instruction_popup.dart';

class VideoResumeReport extends StatefulWidget {
  final String videoText;
  final int videoIndex;
  final Function(String) onCaption;
  const VideoResumeReport(
      {Key? key,
      required this.videoText,
      required this.videoIndex,
      required this.onCaption})
      : super(key: key);

  @override
  _VideoResumeReportState createState() => _VideoResumeReportState();
}

class _VideoResumeReportState extends State<VideoResumeReport> {
  String dotString = '.';
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
    if (widget.videoText != '')
      BlocProvider.of<HomeBloc>(context).add(AnalyseVideoResumeEvent(
          videoText: widget.videoText, index: widget.videoIndex));
    else {
      BlocProvider.of<HomeBloc>(context)
          .add(ListVideoResumeEvent(widget.videoIndex));
    }
    updateDot();
  }

  void updateDot() async {
    while (true) {
      await Future.delayed(Duration(milliseconds: 300));
      setState(() {
        if (dotString == '...') {
          dotString = '';
        } else
          dotString = dotString + '.';
      });
    }
  }

  List<String> mandatoryList = [
    'Name',
    'Email Address',
    'Phone number',
    'Address',
    'Resume Objective',
    'Education & Qualifications',
    'Professional Experience',
    'Skills & Abilities'
  ];

  List<String> optionalList = [
    'Achievements or Rewards',
    'Certifications',
    'Publications',
    'Languages',
  ];

  AnalyseVideoResumeResponse? analyseResponse;
  List<Widget> mandatory = [];
  List<Widget> optional = [];
  bool isMandatoryOk = true;

  @override
  Widget build(BuildContext context) {
    mandatory = [];
    optional = [];
    analyseResponse?.data?.resumeDt?.toJson().entries.forEach((v) {
      if (v.value != null) {
        if (isMandatoryOk) {
          isMandatoryOk = (int.tryParse('${v.value}') ?? 0) > 0;
        }
        mandatory.add(InstructionCustomWidget(
          status:
              v.value == 0 ? ResumeKeyStatus.error : ResumeKeyStatus.complete,
          title: Utility.pascalCase('${v.key}'),
        ));
      }
    });
    analyseResponse?.data?.resumeDtOptional?.toJson().entries.forEach((v) {
      if (v.value != null) {
        optional.add(InstructionCustomWidget(
            status:
                v.value == 0 ? ResumeKeyStatus.error : ResumeKeyStatus.complete,
            title: Utility.pascalCase('${v.key}')));
      }
    });
    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) async {
        if (state is AnalyseVideoResumeState) {
          setState(() {
            switch (state.apiState) {
              case ApiStatus.LOADING:
                Log.v("Loading....................ListVideoResumeState");
                isLoading = true;
                break;
              case ApiStatus.SUCCESS:
                Log.v("Success....................ListVideoResumeState");
                isLoading = false;
                analyseResponse = state.response;

                setState(() {});
                break;
              case ApiStatus.ERROR:
                isLoading = false;
                Log.v("Error..........................ListVideoResumeState");
                Log.v("Error..........................${state.error}");
                break;
              case ApiStatus.INITIAL:
                break;
            }
          });
        } else if (state is ListVideoResumeState) {
          setState(() async {
            switch (state.apiState) {
              case ApiStatus.LOADING:
                Log.v("Loading....................ListVideoResumeState");
                isLoading = false;
                break;
              case ApiStatus.SUCCESS:
                Log.v("Success....................ListVideoResumeState");

                if (state.response?.data?.first.videoText == '') {
                  await Future.delayed(Duration(seconds: 10));

                  BlocProvider.of<HomeBloc>(context)
                      .add(ListVideoResumeEvent(widget.videoIndex));
                } else {
                  widget.onCaption('${state.response?.data?.first.videoText}');
                  BlocProvider.of<HomeBloc>(context).add(
                      AnalyseVideoResumeEvent(
                          videoText: '${state.response?.data?.first.videoText}',
                          index: 0));
                }

                break;
              case ApiStatus.ERROR:
                isLoading = false;

                Log.v("Error..........................${state.error}");
                break;
              case ApiStatus.INITIAL:
                break;
            }
          });
        }
      },
      child: Scaffold(
        body: isLoading || (analyseResponse == null && widget.videoText == '')
            ? Container(
                padding: const EdgeInsets.all(20),
                child: ListView(children: [
                  Row(
                    children: [
                      Text(
                        'analysing_video',
                        style: Styles.bold(size: 17),
                      ).tr(),
                      Text(
                        '$dotString',
                        style: Styles.bold(),
                      ).tr(),
                    ],
                  ),
                  SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 0),
                    child: Text(
                      'explore_jobs_analysing',
                      style: Styles.regular(size: 13),
                    ).tr(),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'mandatory',
                    style: TextStyle(
                      color: Color(0xFF727C95),
                      fontSize: 14,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
                  ).tr(),
                  SizedBox(height: 4),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: mandatoryList.length,
                    itemBuilder: (context, index) => Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 8),
                      child: InstructionCustomWidget(
                        status: ResumeKeyStatus.loading,
                        title: '${mandatoryList[index]}',
                      ),
                    ),
                  ),
                  SizedBox(height: 10),
                  Text(
                    'optional',
                    style: TextStyle(
                      color: Color(0xFF727C95),
                      fontSize: 14,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
                  ).tr(),
                  SizedBox(height: 4),
                  ListView.builder(
                    physics: NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: optionalList.length,
                    itemBuilder: (context, index) => Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 8),
                      child: InstructionCustomWidget(
                        status: ResumeKeyStatus.loading,
                        title: '${optionalList[index]}',
                      ),
                    ),
                  ),
                  SizedBox(height: 30),
                  InkWell(
                    onTap: () {
                      // Navigator.push(
                      //     context,
                      //     MaterialPageRoute(
                      //         builder: (context) => TermsAndCondition(
                      //               url:
                      //                   '${APK_DETAILS['explore-jobs-webview_url']}' +
                      //                       Preference.getInt(
                      //                               Preference.USER_ID)
                      //                           .toString(),
                      //               title: tr('explore_jobs'),
                      //             ),
                      //         maintainState: false));
                      Navigator.push(
                          context,
                          NextPageRoute(
                              ExploreJobListPage(
                                indexNo: widget.videoIndex - 1,
                                fromVideoResume: true,
                              ),
                              isMaintainState: true));
                    },
                    child: Container(
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all()),
                      child: Center(
                          child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('explore_jobs').tr(),
                          Icon(
                            Icons.arrow_forward_ios_outlined,
                            size: 10,
                            color: ColorConstants.BLACK,
                          )
                        ],
                      )),
                    ),
                  )
                ]),
              )
            : Container(
                padding: const EdgeInsets.all(10),
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    Text(
                      'analysis_report',
                      style: Styles.bold(),
                    ).tr(),
                    SizedBox(height: 10),
                    isMandatoryOk
                        ? Container(
                            padding: EdgeInsets.all(10),
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            decoration: BoxDecoration(
                                color: Color(0xff00BC4B).withOpacity(0.07),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Color(0xff00BC4B))),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SvgPicture.asset(
                                    'assets/images/video_resume_key_complete.svg',
                                    color: Color(0xff00BC4B)),
                                SizedBox(width: 4),
                                SizedBox(
                                  width: width(context) * 0.8,
                                  child: Text(
                                    'resume_congrats',
                                    style: Styles.regular(
                                        color: ColorConstants.BLACK),
                                  ).tr(),
                                ),
                              ],
                            ),
                          )
                        : Container(
                            padding: EdgeInsets.all(10),
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            decoration: BoxDecoration(
                                color: Color(0xffFF7B17).withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Color(0xffFF7B17))),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SvgPicture.asset(
                                    'assets/images/video_resume_key_error.svg'),
                                SizedBox(width: 4),
                                SizedBox(
                                    width: width(context) * 0.8,
                                    child: Text(
                                      'resume_component_re-upload',
                                      style: Styles.regular(
                                          color: ColorConstants.BLACK),
                                    ).tr()),
                              ],
                            ),
                          ),
                    SizedBox(height: 10),
                    Text(
                      'mandatory',
                      style: TextStyle(
                        color: Color(0xFF727C95),
                        fontSize: 12,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ).tr(),
                    SizedBox(height: 4),
                    ListView(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        children: mandatory),
                    SizedBox(height: 10),
                    Text(
                      'optional',
                      style: TextStyle(
                        color: Color(0xFF727C95),
                        fontSize: 12,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ).tr(),
                    SizedBox(height: 4),
                    ListView(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        children: optional),
                    SizedBox(height: 30),
                    InkWell(
                      onTap: () {
                        // Navigator.push(
                        //     context,
                        //     MaterialPageRoute(
                        //         builder: (context) => TermsAndCondition(
                        //               url:
                        //                   '${APK_DETAILS['explore-jobs-webview_url']}' +
                        //                       Preference.getInt(
                        //                               Preference.USER_ID)
                        //                           .toString(),
                        //               title: tr('explore_jobs'),
                        //             ),
                        //         maintainState: false));
                        Navigator.push(
                                context,
                                NextPageRoute(
                                    ExploreJobListPage(
                                      indexNo: widget.videoIndex - 1,
                                      fromVideoResume: true,
                                    ),
                                    isMaintainState: true))
                            .then((value) => Navigator.pop(context));
                      },
                      child: Container(
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all()),
                        child: Center(child: Text('explore_jobs').tr()),
                      ),
                    )
                  ],
                ),
              ),
      ),
    );
  }
}
