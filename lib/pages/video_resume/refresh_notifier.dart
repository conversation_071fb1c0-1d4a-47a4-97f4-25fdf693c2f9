import 'package:flutter/material.dart';

class RefreshNotifier extends ChangeNotifier {
  List<bool> isRefreshingList = [];

  void updateList(int len) {
    print("update length is $len");
    isRefreshingList = List.generate(len, (index) => false);
    notifyListeners();
  }

  void setRefreshing(int index) {
    this.isRefreshingList[index] = true;
    notifyListeners();
  }

  void setRefreshingStop(int index) {
    print("refresh_notifier.dart::: setRefreshingStop $index");

    this.isRefreshingList[index] = false;
    notifyListeners();
  }
}
