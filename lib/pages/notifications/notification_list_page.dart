import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/notification_list_resp.dart';
import 'package:masterg/data/models/response/home_response/notification_read_resp.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/notifications/notification_view.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:shimmer/shimmer.dart';
import 'package:badges/badges.dart' as badges;

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});
  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  bool? isLoading = false;
  NotificationsListResp? notificationList;
  NotificationReadResp? notificationRead;
  int currentPage = 1;
  bool loadData = true;
  List<Data>? allNotifications = [];
  int readCount = 0;


  @override
  void initState() {
    getNotificationList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is NotificationsListState) {
            _handleNotificationListState(state);
          }

          if (state is NotificationsReadState) {
            _handleNotificationReadState(state);
          }
        },
        child: Scaffold(
            backgroundColor: ColorConstants.GREY,
            appBar: AppBar(
              elevation: 0,
              backgroundColor: ColorConstants.WHITE,
              title: Text('notification', style: Styles.bold()).tr(),
              leading: BackButton(color: Colors.black),
              actions: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20.0, vertical: 16),
                  child: badges.Badge(
                    badgeContent: allNotifications!.isNotEmpty
                        ? Text('${_getCount(false)}',
                        style: Styles.bold(
                            color: ColorConstants.WHITE, size: 10))
                        : Text(
                        '${Preference.getString(Preference.GET_COUNT) ?? ''}',
                        style: Styles.bold(
                            color: ColorConstants.WHITE, size: 10)),
                    child: Icon(Icons.notification_add_outlined,
                        color: ColorConstants.BLACK),
                  ),
                )
              ],
            ),
            body: ScreenWithLoader(
                isLoading: isLoading,
                body: allNotifications!.isNotEmpty
                    ? NotificationsListItem(
                    allNotifications: allNotifications,
                    onTapCallback: (notificationId, notiId, type,
                        isRead, index) {
                      getReadNotification(
                          allNotifications?[index].notifiableId,
                          '${allNotifications?[index].id}',
                          '${allNotifications?[index].type}',
                          'is_read');
                      setState(() {
                        isRead =
                            allNotifications?[index].readContent;
                      });
                    }): isLoading == true ? ListView.builder(
                    shrinkWrap: true,
                    itemCount: 10,
                    itemBuilder: (BuildContext context, int index) =>
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                            height:
                            MediaQuery.of(context).size.height * 0.1,
                            margin: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 20),
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(6)),
                          ),
                        )) : Center(child: Text('no_notification_fount').tr())))
    );
  }

  //allNotifications!.isNotEmpty
  int _getCount(bool isRead) {
    return allNotifications!
        .where((element) => element.readContent == (isRead ? "is_read" : null))
        .toList()
        .length;
  }

  void getNotificationList() {
    BlocProvider.of<HomeBloc>(context).add(NotificationsListEvent(fromValue: 1, toValue: 10));
  }

  void _handleNotificationListState(NotificationsListState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("bbbSubscriptionCoursesState Loading.................");
          isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          try {
            if (state.response != null && state.response!.data != null) {
              // notificationList = state.response;
              allNotifications!.addAll(state.response!.data!);

              //Log.v('ALLNOTIFICATIONS:--${allNotifications?.length}');
              Preference.setString(Preference.GET_COUNT, _getCount(false).toString());
              setState(() {
                isLoading = false;
              });
            } else {}
          } catch (e, stackTrace) {
            debugPrint('$stackTrace');
            setState(() {
              isLoading = false;
            });
          }
          break;

        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("SubscriptionCoursesState Error..........................");
          print('call notifications event 4');
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void getReadNotification(int? notiId, String? id, String? type, String? isRead) {
    BlocProvider.of<HomeBloc>(context).add(NotificationReadEvent(
        notiId: notiId, id: id, type: type, isRead: isRead));
  }

  void _handleNotificationReadState(NotificationsReadState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Notification Read Loading.................");
          isLoading = true;
          print('call notifications event 2');
          break;
        case ApiStatus.SUCCESS:
          print('call notifications event 3');
          Log.v(
              "Notification Read Success ................... ${state.response}");
          try {
            if (state.response != null && state.response!.data != null) {
              notificationRead = state.response;
              // setState(() {
              //   isLoading = false;
              // });
            } else {}
          } catch (e, stackTrace) {
            debugPrint('$stackTrace');
            setState(() {
              isLoading = false;
            });
          }
          break;

        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("NotificationRead State Error..........................");
          print('call notifications event 4');
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}


//TODO: LIST VIEW
class NotificationsListItem extends StatefulWidget {
  final List<Data>? allNotifications;
  final Function(String?, int?, String?, String?, int) onTapCallback;

  const NotificationsListItem(
      {Key? key, required this.allNotifications, required this.onTapCallback})
      : super(key: key);

  @override
  State<NotificationsListItem> createState() => _NotificationsListItemState();
}

class _NotificationsListItemState extends State<NotificationsListItem> {
  late ScrollController _scrollController;
  late List<Data>? allNotificationsLazy;
  bool _isLoadingLazy = false;
  int from = 10;
  int to = 10;

  @override
  void initState() {
    _scrollController = ScrollController()..addListener(_scrollListener);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    super.dispose();
  }

  void _scrollListener() {
    if (_isLoadingLazy) return;
    if (_scrollController.position.extentAfter == 0.0) {
      _isLoadingLazy = true;
      getNotificationList();
    }
  }


  @override
  Widget build(BuildContext context) {
    String extractDateDetails(String dateString) {
      DateTime date = DateTime.parse(dateString);
      String dayName = DateFormat('EE').format(date);
      String monthName = DateFormat('MMM').format(date);
      String day = DateFormat('d').format(date);
      // String year = DateFormat('y').format(date);
      String time = DateFormat('h:mm a').format(date);
      return '$day $monthName $dayName $time';
    }

    // Sort the notifications based on createdAt field
    List<Data>? sortedNotifications = List.from(widget.allNotifications!)
      ..sort((a, b) {
        DateTime aDate = DateTime.parse(a.createdAt!);
        DateTime bDate = DateTime.parse(b.createdAt!);
        return bDate.compareTo(aDate);
      });

    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) {
        if (state is NotificationsListState) {
          _handleNotificationListState(state);
        }
      },
      child: Container(
        height: MediaQuery.of(context).size.height,
        padding: EdgeInsets.all(8.0),
        child: ListView.builder(
          controller: _scrollController,
          shrinkWrap: true,
          physics: BouncingScrollPhysics(),
          itemCount: widget.allNotifications?.length,
          itemBuilder: (context, index) {
            Data notification = sortedNotifications[index];
            // ignore: unrelated_type_equality_checks
            String? readContent =
            widget.allNotifications?[index].readContent?.toLowerCase();
            bool isRead = readContent == 'is_read';

            return InkWell(
              onTap: () {
                setState(() {
                  widget.allNotifications?[index].readContent = 'is_read';
                });
                if (!isRead) {
                  widget.onTapCallback(
                      '${widget.allNotifications?[index].id}',
                      widget.allNotifications?[index].notifiableId,
                      '${widget.allNotifications?[index].type}',
                      '${widget.allNotifications?[index].readContent}',
                      index);
                }
                Navigator.push(
                  context,
                  NextPageRoute(
                      NotificationViewPage(
                        notificationId:
                        '${widget.allNotifications?[index].id ?? ''}',
                        notiTitle:
                        '${widget.allNotifications?[index].title ?? ''}',
                        notiDesc:
                        '${widget.allNotifications?[index].description ?? ''}',
                        dateTime:
                        '${extractDateDetails('${widget.allNotifications?[index].createdAt}')}',
                        type: '${widget.allNotifications?[index].type ?? ''}',
                        route: '${widget.allNotifications?[index].dataDetails?.route?? ''}',
                        id: widget.allNotifications?[index].dataDetails?.id?? 0,
                      ),
                      isMaintainState: true),
                );
              },
              child: Container(
                //height: 72,
                  margin: EdgeInsets.symmetric(vertical: 5.0),
                  decoration: BoxDecoration(
                    border: Border.all(color: ColorConstants.GREY),
                    borderRadius: BorderRadius.circular(4.0),
                    color: isRead
                        ? ColorConstants.WHITE
                        : ColorConstants().gradientRight().withOpacity(0.08),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          _buildColumn(
                              color: isRead
                                  ? Color(0xffEEF5FF)
                                  : ColorConstants()
                                  .gradientRight()
                                  .withOpacity(0.08),
                              status: widget.allNotifications?[index].type),
                          SizedBox(width: 5),
                          Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: SizedBox(
                              //height: 100,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: MediaQuery.of(context).size.width -120,
                                    child: Text(
                                      '${widget.allNotifications?[index].title ?? ''}',
                                      style: Styles.bold(
                                          color: isRead
                                              ? ColorConstants.BLACK
                                              : Color(0xff0E1638),
                                          size: 14),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: SizedBox(
                                        width:
                                        MediaQuery.of(context).size.width - 120,
                                        child: widget.allNotifications?[index].description != null ? (widget.allNotifications?[index].description.contains('<') && widget.allNotifications?[index].description?.contains('>')) ? Html(
                                          data: """${widget.allNotifications?[index].description}""",
                                        ):Text(
                                          '${widget.allNotifications?[index].description ?? ''}',
                                          softWrap: true,
                                          maxLines: 3,
                                          style: Styles.DMSansregular(
                                              size: 12,
                                              color: isRead
                                                  ? ColorConstants.BLACK
                                                  : Color(0xff0E1638)),
                                        ):SizedBox()/*

                                    : ,*/
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 10.0),
                                    child: Text(
                                        '${extractDateDetails('${widget.allNotifications?[index].createdAt ?? ''}')}',
                                        style: Styles.regular(
                                            color: isRead
                                                ? ColorConstants.BLACK
                                                : Color(0xff0E1638),
                                            size: 10)),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 5.0),
                                    child: Text(
                                        '${widget.allNotifications?[index].type ?? ''}',
                                        style: Styles.regular(
                                            color: isRead
                                                ? ColorConstants.BLACK
                                                : Color(0xff0E1638),
                                            size: 10)),
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      )
                    ],
                  )),
            );
          },
        ),
      ),
    );

  }

  void getNotificationList() {
    BlocProvider.of<HomeBloc>(context).add(NotificationsListEvent(fromValue: from, toValue: to));
  }

  void _handleNotificationListState(NotificationsListState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("bbbSubscriptionCoursesState Loading.................");
          break;
        case ApiStatus.SUCCESS:
          try {
            if (state.response != null && state.response!.data != null) {
              // notificationList = state.response;
              from += 10;
              _isLoadingLazy = false;
              allNotificationsLazy!.addAll(state.response!.data!);
              widget.allNotifications?.addAll(allNotificationsLazy!);

            } else {}
          } catch (e, stackTrace) {
            debugPrint('$stackTrace');
          }
          break;

        case ApiStatus.ERROR:
          Log.v("SubscriptionCoursesState Error..........................");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Widget _buildColumn({Color? color, String? status}) {
    return Container(
      height: 100,
      width: 70,
      decoration: BoxDecoration(color: color),
      child: status?.toLowerCase() == 'assessment'
          ? Icon(Icons.quiz_outlined, size: 30)
          : status?.toLowerCase() == 'video_yts'
          ? Icon(Icons.live_tv, size: 30)
          : status?.toLowerCase() == 'video'
          ? Icon(Icons.video_call, size: 30)
          : Icon(Icons.message, size: 30),
    );
  }
}