import 'dart:async';
import 'dart:developer';

import 'package:audioplayers/audioplayers.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:flutter/material.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

import '../../utils/Styles.dart';

class AudioUrlPlayer extends StatefulWidget {
  /// Path from where to play recorded audio
  final String source;
  final bool isLocalFile;
  const AudioUrlPlayer({
    Key? key,
    required this.source,
    required this.isLocalFile,
  }) : super(key: key);

  @override
  AudioUrlPlayerState createState() => AudioUrlPlayerState();
}

class AudioUrlPlayerState extends State<AudioUrlPlayer> {
  final _audioPlayer = AudioPlayer();
  late StreamSubscription<void> _playerStateChangedSubscription;
  late StreamSubscription<Duration?> _durationChangedSubscription;
  late StreamSubscription<Duration> _positionChangedSubscription;
  Duration? _position = Duration(seconds: 0);
  Duration? _duration = Duration(seconds: 0);

  @override
  void initState() {
    _playerStateChangedSubscription =
        _audioPlayer.onPlayerComplete.listen((state) async {
      setState(() {});
    });
    _positionChangedSubscription = _audioPlayer.onPositionChanged.listen(
      (position) => setState(() {
        _position = position;
      }),
    );
    _durationChangedSubscription = _audioPlayer.onDurationChanged.listen(
      (duration) => setState(() {
        _duration = duration;
      }),
    );
    play();

    super.initState();
  }

  @override
  void dispose() {
    _playerStateChangedSubscription.cancel();
    _positionChangedSubscription.cancel();
    _durationChangedSubscription.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return IgnorePointer(
          ignoring: _duration?.inMilliseconds == 0,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  '${widget.source.split('/').last}',
                  style: Styles.semibold(),
                ),
              ),
              _buildSlider(constraints.maxWidth),
              _buildControl(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildControl() {
    return Column(
      children: [
        _duration?.inMilliseconds == 0
            ? Text(
                'loading',
                style: Styles.regular(size: 16),
              ).tr()
            : Transform.translate(
                offset: Offset(0, -10),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${Utility.showDuration(_position!.inSeconds)}'),
                      Text('${Utility.showDuration(_duration!.inSeconds)}')
                    ],
                  ),
                )),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
                onPressed: () {
                  _audioPlayer
                      .seek(Duration(seconds: _position!.inSeconds - 10));
                },
                icon: Icon(
                  Icons.replay_10,
                  size: 24,
                  color: ColorConstants.GREY_6,
                )),
            _duration?.inMilliseconds == 0
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                    ),
                  )
                : IconButton(
                    onPressed: () async {
                      log("staus is ${_audioPlayer.state} ");
                      try {
                        if (_audioPlayer.state == PlayerState.playing) {
                          await pause();
                        } else {
                          await play();
                        }
                        log("staus is ce ${_audioPlayer.state} ");
                      } catch (e) {
                        log("error while controlling audio file $e");
                      }
                      setState(() {});
                    },
                    icon: Icon(
                      _audioPlayer.state == PlayerState.playing
                          ? Icons.pause_circle_outline_rounded
                          : Icons.play_circle_outline_rounded,
                      size: 32,
                    )),
            IconButton(
                onPressed: () {
                  _audioPlayer
                      .seek(Duration(seconds: _position!.inSeconds + 10));
                },
                icon: Icon(
                  Icons.forward_10,
                  size: 24,
                  color: ColorConstants.GREY_6,
                )),
          ],
        )
      ],
    );
  }

  Widget _buildSlider(double widgetWidth) {
    bool canSetValue = false;
    final duration = _duration;
    final position = _position;

    if (duration != null && position != null) {
      canSetValue = position.inMilliseconds > 0;
      canSetValue &= position.inMilliseconds < duration.inMilliseconds;
    }

    double width = widgetWidth;
    return SizedBox(
      width: width,
      child: SliderTheme(
        data: SliderTheme.of(context).copyWith(
            trackHeight: 4,
            activeTrackColor: ColorConstants().primaryColorbtnAlways(),
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: 0.0)),
        child: Slider(
          onChanged: (v) {
            if (duration != null) {
              final position = v * duration.inMilliseconds;
              _audioPlayer.seek(Duration(milliseconds: position.round()));
            }
          },
          value: canSetValue && duration != null && position != null
              ? position.inMilliseconds / duration.inMilliseconds
              : 0.0,
        ),
      ),
    );
  }

  Future<void> play() async {
    return await _audioPlayer.play(UrlSource(widget.source));
  }

  Future<void> pause() => _audioPlayer.pause();

  Future<void> stop() => _audioPlayer.stop();
}
