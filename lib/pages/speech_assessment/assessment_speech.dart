import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:math';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/utility.dart';
import 'package:path_provider/path_provider.dart';

import '../../utils/Log.dart';
import '../../utils/Styles.dart';
import '../../utils/resource/colors.dart';

void ls(String s) {
  print('audio:: $s');
}

class WaveRecord extends StatefulWidget {
  final Function() onDelete;
  final Function(String) onSave;
  // final bool? isRTL;
  const WaveRecord({
    Key? key,
    required this.onDelete,
    required this.onSave,
    // this.isRTL
  }) : super(key: key);

  @override
  State<WaveRecord> createState() => _WaveRecordState();
}

class _WaveRecordState extends State<WaveRecord> {
  BuildContext? mContext;
  late final RecorderController recorderController;

  String? path;
  bool isRecording = false;
  bool isRecordingCompleted = false;
  bool isLoading = true;
  late Directory appDirectory;

  @override
  void initState() {
    super.initState();
    _getDir();
    _initialiseControllers();
  }

  void _getDir() async {
    // appDirectory = await getTemporaryDirectory();
    // path = "${appDirectory.path}/audio.mp3";
    appDirectory = await getApplicationDocumentsDirectory();
    path =
        "${appDirectory.path}/recording_${DateTime.now().millisecondsSinceEpoch}.m4a";
    isLoading = false;
    setState(() {});
  }

  void _initialiseControllers() {
    recorderController = RecorderController()
      ..androidEncoder = AndroidEncoder.aac
      ..androidOutputFormat = AndroidOutputFormat.mpeg4
      ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
      ..sampleRate = 44100;
    // recorderController = RecorderController()
    //   ..androidEncoder = AndroidEncoder.aac
    //   ..androidOutputFormat = AndroidOutputFormat.mpeg4
    //   ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
    //   ..sampleRate = 44100
    //   ..bitRate = 320000;

    recorderController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    recorderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    mContext = context;
    return WillPopScope(
      onWillPop: () async =>
          !isRecording && !(isRecordingCompleted && path != null),
      child: Scaffold(
        body: isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : Container(
                width: width(context),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    Text(
                      'record_your_audio',
                      style: Styles.bold(size: 14, color: ColorConstants.BLACK),
                    ).tr(),
                    if (isRecordingCompleted && path != null)
                      WaveBubble(
                        path: path,
                        isRTL: false,
                        onDelete: () {
                          isRecordingCompleted = false;
                          path = path;
                          // path = "${appDirectory.path}/audio.mp3";
                          setState(() {});
                          widget.onDelete();
                        },
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: isRecording
                            ? Column(
                                children: [
                                  Text(
                                    '0${recorderController.elapsedDuration}'
                                        .split('.')
                                        .first,
                                    style: Styles.regular(),
                                  ),
                                  SizedBox(height: 10),
                                  AudioWaveforms(
                                    enableGesture: true,
                                    size: Size(
                                        MediaQuery.of(context).size.width / 2,
                                        50),
                                    recorderController: recorderController,
                                    waveStyle: const WaveStyle(
                                      waveColor: Colors.white,
                                      extendWaveform: true,
                                      showMiddleLine: false,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12.0),
                                      color: const Color(0xFF1E1B26),
                                    ),
                                    padding: const EdgeInsets.only(left: 18),
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 15),
                                  ),
                                ],
                              )
                            : SizedBox(),
                      ),
                    ),
                    if (isRecordingCompleted && path != null)
                      InkWell(
                        onTap: () {
                          if (path != null) widget.onSave(path!);
                          Navigator.pop(context);
                        },
                        child: Container(
                            width: 100,
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 18),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color:
                                    ColorConstants().primaryColorbtnAlways()),
                            child: Center(
                              child: Text(
                                'done',
                                style:
                                    Styles.regular(color: ColorConstants.WHITE),
                              ).tr(),
                            )),
                      ),
                    Spacer(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      child: Row(
                        children: [
                          isRecording || isRecordingCompleted
                              ? IconButton(onPressed: () {}, icon: SizedBox())
                              : IconButton(
                                  onPressed: () async {
                                    FilePickerResult? result;

                                    List<String> allowedExtensions = [
                                      'mp3',
                                      'm4a'
                                    ];
                                    try {
                                      if (Platform.isIOS) {
                                        result = await FilePicker.platform
                                            .pickFiles(
                                                allowMultiple: false,
                                                type: FileType.custom,
                                                allowedExtensions:
                                                    allowedExtensions);
                                      } else {
                                        result =
                                            await FilePicker.platform.pickFiles(
                                          allowMultiple: false,
                                          type: FileType.custom,
                                          allowedExtensions: allowedExtensions,
                                          onFileLoading: (path) {},
                                        );
                                      }
                                      if (result != null) {
                                        if (!allowedExtensions.contains(
                                            Utility.fileExtension(
                                                filePath:
                                                    result.paths.first!))) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(SnackBar(
                                                  content: Text(
                                                      '${tr('upload_only')}' +
                                                          ' ${allowedExtensions.toList().join(', ')} ')));
                                          return;
                                        }
                                        path = result.paths.first;
                                        isRecordingCompleted = true;
                                        debugPrint(path);
                                        debugPrint(
                                            "Recorded file size: ${File(path!).lengthSync()}");
                                      }
                                    } on Exception catch (e) {
                                      Log.v("ehce $e");
                                    }
                                    setState(() {});
                                  },
                                  icon: Icon(
                                    Icons.folder_copy,
                                    color: Color(0xff2F374E),
                                  ),
                                ),
                          Spacer(),
                          if (!(isRecordingCompleted && path != null))
                            IconButton(
                              onPressed: _startOrStopRecording,
                              icon: SvgPicture.asset(isRecording
                                  ? 'assets/images/stop_audio.svg'
                                  : 'assets/images/record_audio.svg'),
                              color: Colors.white,
                              iconSize: 50,
                            ),
                          !isRecording
                              ? IconButton(onPressed: () {}, icon: SizedBox())
                              : IconButton(
                                  onPressed: () async {
                                    if (recorderController.isRecording) {
                                      await recorderController.pause();
                                    } else {
                                      await recorderController.record();
                                    }
                                    setState(() {});
                                  },
                                  icon: Icon(
                                    recorderController.isRecording
                                        ? Icons.pause
                                        : Icons.play_arrow,
                                    color: Color(0xff2F374E),
                                  ),
                                ),
                          Spacer(),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
      ),
    );
  }

  void _startOrStopRecording() async {
    try {
      if (isRecording) {
        recorderController.reset();

        final path = await recorderController.stop(false);

        if (path != null) {
          isRecordingCompleted = true;
          debugPrint(path);
          debugPrint("Recorded file size: ${File(path).lengthSync()}");
        }
      } else {
        await recorderController.record(path: path!);
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      setState(() {
        isRecording = !isRecording;
      });
    }
  }

  // void _startOrStopRecording() async {
  //   try {
  //     if (isRecording) {
  //       recorderController.reset();

  //       final path = await recorderController.stop(false);
  //       if (path != null) {
  //         isRecordingCompleted = true;
  //         debugPrint(path);
  //         debugPrint("Recorded file size: ${File(path).lengthSync()}");
  //       }
  //     } else {
  //       final hasPermission = await recorderController
  //           .checkPermission(); // Use stereo for better quality.
  //       if (hasPermission) {
  //         // path = "${appDirectory.path}/audio.mp3";
  //         //  path = path;
  //         print("record path siu $path");
  //         isRecordingCompleted = false;
  //         await recorderController.record(path: path!);
  //       } else {
  //         Log.v("allow record permission");
  //       }
  //     }
  //   } catch (e, stacktrace) {
  //     debugPrint('$stacktrace');
  //   } finally {
  //     setState(() {
  //       isRecording = !isRecording;
  //     });
  //   }
  //   await recorderController.record();
  // }

  void _refreshWave() {
    if (isRecording) recorderController.refresh();
  }
}

class WaveBubble extends StatefulWidget {
  final String? path;
  final Function() onDelete;
  final bool hideDelete;
  final bool isRTL;

  const WaveBubble(
      {Key? key,
      this.path,
      required this.onDelete,
      this.hideDelete = false,
      required this.isRTL})
      : super(key: key);

  @override
  State<WaveBubble> createState() => _WaveBubbleState();
}

class _WaveBubbleState extends State<WaveBubble> {
  BuildContext? mContext;
  late PlayerController controller;
  late StreamSubscription<PlayerState> playerStateSubscription;

  final playerWaveStyle = PlayerWaveStyle(
    fixedWaveColor: Color(0xffD9D9D9),
    liveWaveColor: ColorConstants().primaryColorbtnAlways(),
    spacing: 6,
  );

  @override
  void initState() {
    super.initState();
    print("the path is ${widget.isRTL}");
    controller = PlayerController();
    _preparePlayer();

    playerStateSubscription = controller.onPlayerStateChanged.listen((_) {
      setState(() {});
    });
  }

  void _preparePlayer() async {
    print("the path is start");
    try {
      controller.preparePlayer(
        path: '${widget.path}',
        shouldExtractWaveform: true,
      );
    } catch (e) {
      print("the path is exception $e");
    }
    print("the path is ${widget.path}");

    setState(() {});
  }

  @override
  void dispose() {
    playerStateSubscription.cancel();
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    mContext = context;
    return widget.path != null
        ? Directionality(
            textDirection: Utility.setDirection(false),
            child: Container(
              width: width(context),
              child: StreamBuilder<int?>(
                stream: controller.onCurrentDurationChanged,
                builder: ((context, snapshot) => SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                          trackHeight: 4,
                          activeTrackColor:
                              ColorConstants().primaryColorbtnAlways(),
                          thumbShape:
                              RoundSliderThumbShape(enabledThumbRadius: 0.0)),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 10,
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 22.0),
                            child: Text(
                              '${widget.path}'.split('/').last,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Styles.semibold(),
                            ),
                          ),
                          snapshot.hasData
                              ? Slider(
                                  min: 0.0,
                                  max: controller.maxDuration.toDouble(),
                                  value: snapshot.data!.toDouble(),
                                  onChangeEnd: (b) {
                                    print("change starte");
                                  },
                                  onChanged: (value) {
                                    print("value is");
                                    controller.seekTo(value.toInt());
                                  },
                                )
                              : Slider(
                                  min: 0.0,
                                  max: 1.0,
                                  value: 0.0,
                                  onChanged: (b) {}),
                          Transform.translate(
                            offset: Offset(0, -10),
                            child: snapshot.data == null
                                ? Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 24.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text('00:00'),
                                        // Text(':${controller.maxDuration ~/ 1000}'.padLeft(2,'0'))
                                        Text(
                                            '${Utility.showDuration(controller.maxDuration ~/ 1000)}')
                                      ],
                                    ),
                                  )
                                : Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 22.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                            '${Utility.showDuration(snapshot.data! ~/ 1000)}'),
                                        // Text(
                                        //     '${snapshot.data! ~/ (1000 * 60)}:${snapshot.data! ~/ 1000}'),
                                        Text(
                                            '${Utility.showDuration(controller.maxDuration ~/ 1000)}')
                                      ],
                                    ),
                                  ),
                          ),
                          Stack(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  IconButton(
                                      onPressed: () {
                                        int seekMs =
                                            max(0, snapshot.data! - 10000);
                                        controller.seekTo(seekMs);
                                      },
                                      icon: Icon(
                                        Icons.replay_10,
                                        size: 24,
                                        color: ColorConstants.GREY_6,
                                      )),
                                  IconButton(
                                      // onPressed: () async {
                                      //   controller.playerState.isPlaying
                                      //       ? await controller.pausePlayer()
                                      //       : await controller.startPlayer(
                                      //           finishMode: FinishMode.pause);
                                      //
                                      //   setState(() {});
                                      // },
                                      onPressed: () async {
                                        controller.playerState.isPlaying
                                            ? await controller.pausePlayer()
                                            : await controller.startPlayer();

                                        setState(() {});
                                      },
                                      icon: Icon(
                                        controller.playerState.isPlaying
                                            ? Icons.pause_circle_outline_rounded
                                            : Icons.play_circle_outline_rounded,
                                        size: 32,
                                      )),
                                  IconButton(
                                      onPressed: () {
                                        int seekMs = min(controller.maxDuration,
                                            snapshot.data! + 10000);

                                        controller.seekTo(seekMs);
                                      },
                                      icon: Icon(
                                        Icons.forward_10,
                                        size: 24,
                                        color: ColorConstants.GREY_6,
                                      )),
                                ],
                              ),
                              Positioned(
                                right: 10,
                                bottom: 16,
                                child: InkWell(
                                    onTap: () {
                                      bool isRTL = (widget.isRTL);
                                      print("data context is $isRTL");

                                      AlertsWidget.showCustomDialog(
                                          isRTL: isRTL,
                                          context: mContext!,
                                          title: tr('confirm'),
                                          text: tr('delete_confirmation_msg'),
                                          icon:
                                              'assets/images/circle_alert_fill.svg',
                                          onCancelClick: () {},
                                          onOkClick: () async {
                                            widget.onDelete();
                                          });
                                    },
                                    child: Padding(
                                      padding:
                                          const EdgeInsets.only(right: 10.0),
                                      child: SvgPicture.asset(
                                        'assets/images/delete.svg',
                                        height: 20,
                                      ),
                                    )),
                              )
                            ],
                          )
                        ],
                      ),
                    )),
              ),
            ),
          )
        : const SizedBox.shrink();
  }
}
