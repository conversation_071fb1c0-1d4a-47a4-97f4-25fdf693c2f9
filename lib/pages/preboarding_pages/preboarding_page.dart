import 'dart:math';

import 'package:dots_indicator/dots_indicator.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/auth_pages/new_language_screen.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/preboarding_pages/full_screen_preboarding.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import '../ebook/pages/preboarding/preboarding.dart';

class SingularisWowPreBoarding extends StatefulWidget {
  @override
  _SingularisWowPreBoardingState createState() => _SingularisWowPreBoardingState();
}

class _SingularisWowPreBoardingState extends State<SingularisWowPreBoarding> {
  List<Widget> _pages = [];
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    if (SchedulerBinding.instance.schedulerPhase ==
        SchedulerPhase.persistentCallbacks) {
      // SchedulerBinding.instance.addPostFrameCallback((duration) {
      //   _pages.add(_pageItem(0));
      //   _pages.add(_pageItem(1));
      //   _pages.add(_pageItem(2));
      //   setState(() {});
      // });
    }
  }

  @override
  Widget build(BuildContext context) {
    Application(context);
    _pages = [_pageItem(0), _pageItem(1), _pageItem(2)];
    return Scaffold(
      backgroundColor: Colors.white,
      body: APK_DETAILS['package_name'] == 'com.singularis.mescdigilibrary'
          ? PreboardingPage()
          : APK_DETAILS['full_screen_preboarding'] == '1'
              ? FullScreenPreboarding()
              : Column(
                  children: [_pageView(), _loginRegisterWidget(_currentIndex)],
                ),
    );
  }

  Widget _loginRegisterWidget(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 25,
      ),
      child: Container(
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _dots(index),
            SizedBox(
              height: 10,
            ),
            TapWidget(
              onTap: () async {
                Navigator.pushAndRemoveUntil(
                    context,
                    NextPageRoute(SelectLanguage(
                      showEdulystLogo: true,
                    )),
                    (route) => false);
              },
              child: Container(
                height: height(context) * 0.06,
                width: min(400, width(context)),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  gradient: LinearGradient(colors: [
                    ColorConstants().gradientLeft(),
                    ColorConstants().gradientRight(),
                  ]),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'get_started',
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold),
                    ).tr(),
                    Padding(
                      padding: const EdgeInsets.only(left: 10.0),
                      child: Icon(
                        Icons.arrow_forward_ios_rounded,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _pageView() {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.8,
      child: PageView.builder(
        itemBuilder: (context, index) {
          return Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.7,
            child: _pages[index],
          );
        },
        allowImplicitScrolling: false,
        itemCount: _pages.length,
        onPageChanged: (value) {
          setState(() {
            _currentIndex = value;
          });
        },
        controller: PageController(
          viewportFraction: 1,
          initialPage: 0,
        ),
      ),
    );
  }

  Widget _pageItem(int index) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      // alignment: Alignment.topCenter,
      // color: Colors.red,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 20,
          ),
          Container(
            width: double.infinity,
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.all(30.0),
              child: Center(
                child: Image.asset('${APK_DETAILS['preboarding${index + 1}']}',
                    // PreBoardingData.getDat()[index]['image'],
                    fit: BoxFit.contain,
                    height: height(context) * 0.4,
                    width: width(context)),
              ),
            ),
          ),
          SizedBox(
            height: 20,
          ),
          _buildContentTitle(index),
          _buildContent(index)
        ],
      ),
    );
  }

  _dots(int index) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DotsIndicator(
          dotsCount: 3,
          position: index.toDouble(),
          decorator: DotsDecorator(
            size: const Size.square(8.0),
            color: Color(0xffCCCACA),
            activeColor: ColorConstants().gradientLeft(),
            activeSize: const Size(30.0, 8.0),
            activeShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0)),
          ),
        ),
      ],
    );
  }

  _dot(bool index) {
    return Container(
      width: 10,
      height: 10,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black),
        color: index ? ColorConstants().primaryColor() : ColorConstants.WHITE,
        borderRadius: BorderRadius.all(
          Radius.circular(5),
        ),
      ),
    );
  }

  _buildContent(int index) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      // color: Colors.blue,
      width: width(context) * 0.8,
      child: Text(
        '${APK_DETAILS['preboarding_desc${index + 1}']}',
        // PreBoardingData.getDat()[index]['text2'],

        style: Styles.textRegular(
          size: 16,
        ),
        maxLines: 4,
        textAlign: TextAlign.center,
      ).tr(),
    );
  }

  _buildContentTitle(int index) {
    return Container(
      width: width(context) * 0.8,
      child: GradientText(
        // PreBoardingData.getDat()[index]['text1'],
        tr('${APK_DETAILS['preboarding_title${index + 1}']}'),
        style:
            Styles.textRegular(size: 16).copyWith(fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
        maxLines: 2,
        colors: [
          ColorConstants().gradientLeft(),
          ColorConstants().gradientRight(),
        ],
      ),
    );
  }
}

class PreBoardingData {
  static Map<int, dynamic> getDat() {
    return {
      0: {
        'image': APK_DETAILS['preboarding1'],
        'text1': tr('landing_scrn1'),
        'text2': tr('landing_scrn2')
      },
      1: {
        'image': APK_DETAILS['preboarding2'],
        'text1': tr('landing_scrn3'),
        'text2': tr('landing_scrn4')
      },
      2: {
        'image': APK_DETAILS['preboarding3'],
        'text1': tr('landing_scrn5'),
        'text2': tr('landing_scrn6')
      },
    };
  }
}
