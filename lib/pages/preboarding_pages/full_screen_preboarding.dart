import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/get_core.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/auth_pages/bottomSheet_login_flow/select_language_bottomsheet.dart.dart';
import 'package:masterg/pages/preboarding_pages/participation_register.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../../local/pref/Preference.dart';
import '../auth_pages/new_language_screen.dart';

class FullScreenPreboarding extends StatefulWidget {
  const FullScreenPreboarding({Key? key}) : super(key: key);

  @override
  State<FullScreenPreboarding> createState() => _FullScreenPreboardingState();
}

class _FullScreenPreboardingState extends State<FullScreenPreboarding>
    with SingleTickerProviderStateMixin {
  late final PageController _pageController = PageController();
  late final AnimationController _controller = AnimationController(
    duration: const Duration(seconds: 2),
    vsync: this,
  )..repeat(reverse: true);
  late final Animation<Offset> _offsetAnimation = Tween<Offset>(
    begin: Offset.zero,
    end: const Offset(1.5, 0.0),
  ).animate(CurvedAnimation(
    parent: _controller,
    curve: Curves.elasticIn,
  ));

  int preboardingLen = 3;
  int dotIndex = 0;
  late Timer _timer;

  @override
  void dispose() {
    _pageController.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(Duration(seconds: 5), (Timer timer) {
      setState(() {
        _currentIndex = (_currentIndex + 1) % preboardingLen;
      });
      if (_pageController.page! < preboardingLen - 1) {
        _pageController.nextPage(
            duration: Duration(milliseconds: 500), curve: Curves.ease);
      } else {
        _pageController.animateToPage(0,
            duration: Duration(milliseconds: 500), curve: Curves.ease);
      }
    });
  }

  int _currentIndex = 0;


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            left: 0,
            right: 0,
            child: Container(
              child: AnimatedSwitcher(
                duration: Duration(seconds: 1),
                child: Container(
                  width: width(context),
                  key: ValueKey<String>(
                      kIsWeb ? '${APK_DETAILS['preboarding_web${_currentIndex + 1}']}' : '${APK_DETAILS['preboarding${_currentIndex + 1}']}'),
                  child: Stack(children: [
                    if(kIsWeb) ...[
                      Positioned.fill(
                        child: '${APK_DETAILS['preboarding_web${_currentIndex + 1}']}'
                            .split('.')
                            .last ==
                            'svg'
                            ? SvgPicture.asset(
                          '${APK_DETAILS['preboarding_web${_currentIndex + 1}']}',
                          fit: BoxFit.cover,
                          width: width(context),
                        )
                            : Image.asset(
                          '${APK_DETAILS['preboarding_web${_currentIndex + 1}']}',
                          fit: BoxFit.cover,
                        ),
                      ),
                    ] else ...[
                      Positioned.fill(
                        child: '${APK_DETAILS['preboarding${_currentIndex + 1}']}'
                            .split('.')
                            .last ==
                            'svg'
                            ? SvgPicture.asset(
                          '${APK_DETAILS['preboarding${_currentIndex + 1}']}',
                          fit: BoxFit.cover,
                          width: width(context),
                        )
                            : Image.asset(
                          '${APK_DETAILS['preboarding${_currentIndex + 1}']}',
                          fit: BoxFit.cover,
                        ),
                      ),

                      Positioned(
                          top:
                          '${tr('${APK_DETAILS['preboarding_desc${_currentIndex + 1}']}')}' !=
                              ''
                              ? height(context) * 0.6
                              : height(context) * 0.7,
                          left: Utility().isRTL(context) ? 0 : 15,
                          right: Utility().isRTL(context) ? 15 : null,
                          child: SizedBox(
                            width: width(context) * 0.9,
                            child: APK_DETAILS['package_name'] != 'com.singularis.jumeira' ? Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  '${tr('${APK_DETAILS['preboarding_title${_currentIndex + 1}']}')}',
                                  maxLines: 4,
                                  textAlign: TextAlign.center,
                                  style: Styles.textBold(
                                      lineHeight: 1.3,
                                      color: ColorConstants.WHITE,
                                      size: APK_DETAILS['package_name'] !=
                                          'com.singularis.mesc'
                                          ? 33.37
                                          : 33.37),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: Text(
                                    '${tr('${APK_DETAILS['preboarding_desc${_currentIndex + 1}']}')}',
                                    textAlign: TextAlign.center,
                                    maxLines: 4,
                                    style: Styles.regular(
                                        lineHeight: 1.4,
                                        color: ColorConstants.WHITE,
                                        size: 16),
                                  ),
                                )
                              ],
                            ) : SizedBox(),
                          )),
                    ],
                  ]),
                ),
                transitionBuilder: (child, animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: child,
                  );
                },
              ),
            ),
          ),
          Positioned(
            bottom: 60,
            left: 15,
            right: 15,
            child: Column(
              children: [
                // _dots(dotIndex),
                SizedBox(height: 10),

                TapWidget(
                  onTap: () {
                    if (APK_DETAILS["bottom_sheet_login"] == "true") {
                      getLanguageBottomSheet();
                    } else
                      Navigator.pushAndRemoveUntil(
                          context,
                          NextPageRoute(SelectLanguage(
                            showEdulystLogo: true,
                          )),
                          (route) => false);

                    // Navigator.push(
                    //     context, NextPageRoute(ParticiaptionRegister()));
                  },
                  child: Container(
                    height: height(context) * 0.06,
                    width: width(context),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: ColorConstants.DARK_BUTTON
                        // gradient: LinearGradient(colors: [
                        //   ColorConstants().gradientLeft(),
                        //   ColorConstants().gradientRight(),
                        // ]),
                        ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'get_started',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold),
                        ).tr(),
                        SizedBox(width: 5),
                        Icon(
                          Icons.arrow_forward_ios_rounded,
                          color: ColorConstants.ACCENT_COLOR,
                          size: 18,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  getLanguageBottomSheet() {
    return showModalBottomSheet(
        enableDrag: false,
        isDismissible: false,
        isScrollControlled: false,
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.4,
              decoration: BoxDecoration(
                color: ColorConstants.DARK_BACKGROUND,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.all(20),
                child: SingleChildScrollView(
                  child: Column(
                    children: <Widget>[
                      Text('app_language',
                              style: Styles.DMSansregular(
                                  color: ColorConstants.ACCENT_COLOR, size: 16))
                          .tr(),
                      SizedBox(height: 20),
                      Container(
                          // height: MediaQuery.of(context).size.height * 0.4,
                          child: SelectLanguageBottomSheet(
                        showEdulystLogo: true,
                      ))
                    ],
                  ),
                ),
              ),
            );
          });
        });
  }

  _dots(int index) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DotsIndicator(
          dotsCount: 3,
          position: index.toDouble(),
          decorator: DotsDecorator(
            size: const Size.square(8.0),
            color: Color(0xffCCCACA),
            activeColor: ColorConstants.WHITE,
            activeSize: const Size(30.0, 8.0),
            activeShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0)),
          ),
        ),
      ],
    );
  }
}
