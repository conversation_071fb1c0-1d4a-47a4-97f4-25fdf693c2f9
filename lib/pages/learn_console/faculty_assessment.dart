import 'dart:convert';
import 'dart:developer';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/my_assessment_response.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/providers/mg_assessment_detail_provioder.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/training_pages/mg_assessment_detail.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';

import '../../data/models/response/home_response/faculty_response/faculty_batch_assessment_resp.dart';

class FacultyAssessmentPage extends StatefulWidget {
  final String? courseId;
  const FacultyAssessmentPage({
    super.key,
    this.courseId,
  });

  @override
  State<FacultyAssessmentPage> createState() => _FacultyAssessmentPageState();
}

class _FacultyAssessmentPageState extends State<FacultyAssessmentPage> {
  MenuListProvider? menuProvider;
  FacultyBatchAssessmentResponse? facultyAssessment;
  bool isLoading = false;
  Map<int, bool> batchSelected = Map<int, bool>();
  int? selectedDate = DateTime.now().day;
  int? selectedMonth = DateTime.now().month;
  int? selectedYear = DateTime.now().year;
  Map<String, bool> isSelected = new Map<String, bool>();
  bool isAllChecked = false;
  List<Assessment> filteredAssessments = [];
  @override
  void initState() {
    getFacultyAssessment();

    super.initState();
  }

  void getFacultyAssessment({int? courseId, String? selectedDate}) {
    BlocProvider.of<HomeBloc>(context).add(FacultyBatchAssessmentEvent(
        courseId: courseId, selectedDate: selectedDate));
  }

  @override
  Widget build(BuildContext context) {
    void _showMonthPicker(BuildContext context) async {
      try {
        DateTime? picked = await showDatePicker(
          context: context,
          firstDate: DateTime(2010),
          lastDate: DateTime(2050),
          initialDate: DateTime(
              selectedYear ?? DateTime.now().year,
              selectedMonth ?? DateTime.now().month,
              selectedDate ?? DateTime.now().day),
          builder: (BuildContext context, Widget? child) {
            // return Theme(
            //   data: ThemeData.light().copyWith(
            //     primaryColor: ColorConstants.PRIMARY_BLUE,
            //     accentColor: ColorConstants.PRIMARY_BLUE,
            //     colorScheme: ColorScheme.light(primary: ColorConstants.PRIMARY_BLUE),
            //     buttonTheme: ButtonThemeData(textTheme: ButtonTextTheme.primary),
            //   ),
            //   child: child!,
            // );
            return Theme(
              data: ThemeData.light().copyWith(
                primaryColor: ColorConstants.PRIMARY_BLUE,
                colorScheme: ColorScheme.light(
                  primary: ColorConstants.PRIMARY_BLUE,
                  secondary: ColorConstants.PRIMARY_BLUE, // replaces accentColor
                ),
                buttonTheme: ButtonThemeData(textTheme: ButtonTextTheme.primary),
              ),
              child: child!,
            );
          },
        );

        if (picked != null &&
            picked != DateTime(selectedYear!, selectedMonth!, selectedDate!)) {
          setState(() {
            selectedDate = picked.day;
            selectedMonth = picked.month;
            selectedYear = picked.year;
          });

          String formattedDate = DateFormat('yyyy-MM-dd').format(picked);
          print('Selected date is: $formattedDate');
          // Uncomment and update this line with your logic

          getFacultyAssessment(courseId: 0, selectedDate: formattedDate);

          filterAssessmentsFromCalender();

          // log('selected location is $selectClassLocation');
        }
      } catch (e, stackTrace) {
        print('Error: $e');
        print('Stack trace: $stackTrace');
      }
    }

    return BlocManager(
        initState: (context) {},
        child: Consumer<MenuListProvider>(
          builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is FacultyBatchAssessmentState) {
                _handleFacultyBatchAssessmentState(state);
              }
            },
            child: Scaffold(
                appBar: AppBar(
                  elevation: 0,
                  backgroundColor: ColorConstants.WHITE,
                  leading: BackButton(color: ColorConstants.BLACK),
                  title: Text('assessment', style: Styles.bold(size: 16)).tr(),
                  actions: [
                    InkWell(
                      onTap: () {
                        _showMonthPicker(context);
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: SvgPicture.asset(
                          'assets/images/selected_calender.svg',
                          height: 20,
                          width: 20,
                          allowDrawingOutsideViewBox: true,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: GestureDetector(
                        onTap: () {
                          List<String?> selectedUserId = [];
                          if (isAllChecked == true) {
                            facultyAssessment?.data?.assessment
                                ?.forEach((value) {
                              selectedUserId.add(value.batch);
                            });
                          } else {
                            facultyAssessment?.data?.assessment
                                ?.forEach((value) {
                              if (isSelected[value.batch] == true) {
                                selectedUserId.add(value.batch);
                              }
                            });
                          }
                          showBottomSheet(context);
                        },
                        child: SvgPicture.asset(
                          'assets/images/filter.svg',
                          height: 15,
                          width: 15,
                          allowDrawingOutsideViewBox: true,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: ColorConstants.GREY,
                body: ScreenWithLoader(
                  isLoading: isLoading,
                  body: SingleChildScrollView(
                    child: Column(children: [
                      facultyAssessment?.data?.assessment == null &&
                              facultyAssessment?.data?.assessment?.length == 0
                          ? SizedBox()
                          : assessmentList()
                    ]),
                  ),
                )),
          ),
        ));
  }

  void filterAssessmentsFromCalender() {
    filteredAssessments =
        facultyAssessment!.data!.assessment!.where((assessment) {
      var assessmentDate =
          DateTime.fromMillisecondsSinceEpoch((assessment.endDate ?? 0) * 1000);
      return assessmentDate.year == selectedYear &&
          assessmentDate.month == selectedMonth;
    }).toList();

    setState(() {});
    Log.v('Filtered assessments: $filteredAssessments');
  }

  void _handleFacultyBatchAssessmentState(FacultyBatchAssessmentState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyBatchDetailsState.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................FacultyBatchDetailsState");

            facultyAssessment = state.response;
            filteredAssessments = state.response!.data!.assessment!;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyBatchDetailsState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  assessmentList() {
    return Column(
      children: [
        filteredAssessments.length != 0 && filteredAssessments.isNotEmpty
            ? ListView.builder(
                shrinkWrap: true,
                physics: BouncingScrollPhysics(),
                itemCount: filteredAssessments.length,
                itemBuilder: (BuildContext context, int index) {
                  if (facultyAssessment?.data?.batches != null &&
                      facultyAssessment?.data?.batches?.length != 0) {
                    var assessment = filteredAssessments[index];
                    var batches = jsonDecode(assessment.batch ?? '[]');

                    bool isVisible = batchSelected.containsValue(true)
                        ? batches
                            .any((batch) => batchSelected[batch['id']] == true)
                        : true;

                    // jsonDecode(
                    //         '${facultyAssessment?.data?.assessment?[index].batch}')[index]
                    // ['batch_id'];

                    return Visibility(
                      visible: isVisible,
                      child: Container(
                        // height: MediaQuery.of(context).size.height * 0.18,
                        margin:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                        width: MediaQuery.of(context).size.width * 0.9,
                        decoration: BoxDecoration(
                            color: ColorConstants.ACCENT_COLOR,
                            borderRadius: BorderRadius.circular(6)),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                    '${filteredAssessments[index].programName}',
                                    style: Styles.regular(size: 14)),
                              ),
                              Row(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(left: 9, top: 3),
                                    child: Text(
                                        '${filteredAssessments[index].title ?? ''}',
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        softWrap: false,
                                        style: Styles.bold(size: 16)),
                                  ),
                                  Spacer(),
                                  Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Container(
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                              color: Color(0xffF0F1FA),
                                              width: 2)),
                                      margin: EdgeInsets.only(left: 9, top: 3),
                                      child: Padding(
                                          padding: const EdgeInsets.all(4.0),
                                          child: SizedBox(
                                            height: 20,
                                            // width: 100,
                                            child: ListView.builder(
                                                scrollDirection:
                                                    Axis.horizontal,
                                                shrinkWrap: true,
                                                itemCount: batches.length ?? 0,
                                                //  jsonDecode(
                                                //         '${filteredAssessments[index].batch}')
                                                //     .length,
                                                itemBuilder: (context,
                                                        batchIndex) =>
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              right: 4.0),
                                                      child: ShaderMask(
                                                        blendMode:
                                                            BlendMode.srcIn,
                                                        shaderCallback:
                                                            (Rect bounds) {
                                                          return LinearGradient(
                                                              begin: Alignment
                                                                  .centerLeft,
                                                              end: Alignment
                                                                  .centerRight,
                                                              colors: <Color>[
                                                                ColorConstants()
                                                                    .gradientLeft(),
                                                                ColorConstants()
                                                                    .gradientRight()
                                                              ]).createShader(
                                                              bounds);
                                                        },
                                                        child: Text(
                                                            batches[batchIndex][
                                                                    'batch_name']
                                                                .toString(),
                                                            // jsonDecode('${filteredAssessments[index].batch}')[
                                                            //            index]
                                                            //         ['batch_name']
                                                            //     .toString(),
                                                            maxLines: 2,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            softWrap: false,
                                                            style:
                                                                Styles.semibold(
                                                              size: 12,
                                                            )),
                                                      ),
                                                    )),
                                          )),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 10),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      child: Row(
                                        children: [
                                          Icon(Icons.alarm, size: 20),
                                          SizedBox(width: 4),
                                          // Text(
                                          //     '${Utility.convertCourseTime(int.parse('${filteredAssessments[index].startDate}'), 'hh:mm aa', isUTC: true)} To ',
                                          //     style: Styles.regular(size: 12)),
                                          // Text(
                                          //     '${Utility.getTime(timestamp: filteredAssessments[index].endDate, dateFormat: 'hh:mm aa')}',
                                          //     style: Styles.regular(size: 12)),

                                          StrToTime(
                                            //time: '${Utility.convertCourseTime(int.parse('${filteredAssessments[index].startDate}'), 'hh:mm aa', isUTC: true)}',
                                            time: '${Utility.convertGMTTime(filteredAssessments[index].startDate)}',
                                            dateFormat: ' hh:mm a ',
                                            appendString: Utility().isRTL(context) ? '' : tr('to'),
                                            textStyle: Styles.regular(
                                                size: 12, color: ColorConstants.BODY_TEXT),
                                          ),

                                          StrToTime(
                                            //time: '${Utility.convertCourseTime(int.parse('${filteredAssessments[index].startDate}'), 'hh:mm aa', isUTC: true)}',
                                            time: '${Utility.convertGMTTime(filteredAssessments[index].endDate)}',
                                            dateFormat: ' hh:mm a ',
                                            appendString: Utility().isRTL(context) ? '' : tr('to'),
                                            textStyle: Styles.regular(
                                                size: 12, color: ColorConstants.BODY_TEXT),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Spacer(),
                                    SizedBox(
                                      child: Row(
                                        children: [
                                          Icon(Icons.calendar_month_outlined,
                                              size: 20),
                                          SizedBox(width: 4),
                                          Text(
                                            '${Utility.convertDateFromMillis(int.parse('${facultyAssessment?.data?.assessment?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ',
                                            style: Styles.regular(size: 12),
                                            // textDirection: ui.TextDirection.ltr,
                                          ),
                                          // StrToTime(
                                          //   //time: '${Utility.convertCourseTime(filteredAssessments[index].endDate!, "dd MMM")}',
                                          //   time: '${Utility.convertDateFromMillis(int.parse('${facultyAssessment?.data?.assessment?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ',
                                          //   dateFormat: ' dd-MMM-yy ',
                                          //   appendString: '',
                                          //   textStyle: Styles.regular(
                                          //       size: 12,
                                          //       lineHeight: 1,
                                          //       color:
                                          //           ColorConstants.BODY_TEXT),
                                          // ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 10),
                              Divider(),
                              Center(
                                child: InkWell(
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        NextPageRoute(
                                            ChangeNotifierProvider<
                                                    MgAssessmentDetailProvider>(
                                                create: (context) =>
                                                    MgAssessmentDetailProvider(
                                                        TrainingService(
                                                            ApiService()),
                                                        AssessmentList(
                                                            contentId:
                                                                filteredAssessments[
                                                                        index]
                                                                    .id)),
                                                child: MgAssessmentDetailPage(
                                                  programName:
                                                      '${filteredAssessments[index].title}',
                                                )),
                                            isMaintainState: true));
                                  },
                                  child: Container(
                                    height: height(context) * 0.05,
                                    width: width(context) * 0.6,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(colors: [
                                        ColorConstants().gradientLeft(),
                                        ColorConstants().gradientRight()
                                      ]),

                                      borderRadius: BorderRadius.circular(10),
                                      // border: Border.all(color: Color(0xffF0F1FA), width: 2)
                                    ),
                                    margin: EdgeInsets.only(left: 9, top: 3),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Center(
                                          child: Text('view_assessment',
                                                  style: Styles.textBold(
                                                      size: 12,
                                                      color: ColorConstants
                                                          .ACCENT_COLOR))
                                              .tr()),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    );
                  }
                  return SizedBox();
                })
            : isLoading
                ? SizedBox()
                : Center(
                    child: Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text('no_assessment_found',
                            style: Styles.bold(size: 16))
                        .tr(),
                  )),
      ],
    );
  }

  void showBottomSheet(context) {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        backgroundColor: ColorConstants.WHITE,
        builder: (context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                // mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Center(
                    child: Container(
                      padding: EdgeInsets.all(10),
                      margin: EdgeInsets.only(top: 10),
                      height: 4,
                      width: 70,
                      decoration: BoxDecoration(
                          color: Colors.grey,
                          borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                  Container(
                    height: 0.5,
                    color: Colors.grey[100],
                  ),
                  Container(
                      color: ColorConstants.WHITE,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(
                          children: [
                            Text('filters', style: Styles.bold()).tr(),
                            Spacer(),
                            GestureDetector(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: Icon(Icons.close))
                          ],
                        ),
                      )),
                  Container(
                      //  height: 400,
                      color: ColorConstants.WHITE,
                      child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('select_batch', style: Styles.bold()).tr(),
                                SizedBox(height: 5),
                                SizedBox(
                                  // height: 400,
                                  child: ListView.builder(
                                      scrollDirection: Axis.vertical,
                                      shrinkWrap: true,
                                      physics: BouncingScrollPhysics(),
                                      itemCount: facultyAssessment
                                              ?.data?.batches?.length ??
                                          0,
                                      itemBuilder: ((context, index) {
                                        return Row(children: [
                                          Checkbox(
                                              activeColor: ColorConstants
                                                  .ACTIVE_TAB_UNDERLINE,
                                              value: batchSelected[
                                                      facultyAssessment
                                                          ?.data
                                                          ?.batches?[index]
                                                          .id] ??
                                                  false,
                                              onChanged: ((value) {
                                                setState(() {
                                                  batchSelected[int.parse(
                                                          '${facultyAssessment?.data?.batches?[index].id}')] =
                                                      value ?? false;
                                                });
                                              })),
                                          Text(
                                              '${facultyAssessment?.data?.batches?[index].title ?? ''}',
                                              style: Styles.regular()),
                                        ]);
                                      })),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0, vertical: 16),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          getFacultyAssessment();
                                          Navigator.pop(context);
                                        },
                                        child: Container(
                                          height: height(context) * 0.05,
                                          width: width(context) * 0.4,
                                          decoration: BoxDecoration(
                                            color: Color(0xff3CA4D2),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            // border: Border.all(color: Color(0xffF0F1FA), width: 2)
                                          ),
                                          margin:
                                              EdgeInsets.only(left: 9, top: 3),
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Center(
                                                child: Text('apply',
                                                        style: Styles.textBold(
                                                            size: 12,
                                                            color: ColorConstants
                                                                .ACCENT_COLOR))
                                                    .tr()),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ]))),
                  Container(
                    height: 0.5,
                    color: Colors.grey[100],
                  ),
                ],
              ),
            );
          });
        });
  }
}
