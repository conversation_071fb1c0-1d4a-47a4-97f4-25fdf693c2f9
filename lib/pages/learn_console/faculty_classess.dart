import 'dart:convert';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_class_resp.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/learn_console/attendance_view.dart';
import 'package:masterg/pages/learn_console/view_recording.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import '../custom_pages/custom_widgets/NextPageRouting.dart';
import 'dart:ui' as ui;

class FacultyClassPage extends StatefulWidget {
  const FacultyClassPage({
    super.key,
  });

  @override
  State<FacultyClassPage> createState() => _FacultyClassPageState();
}

class _FacultyClassPageState extends State<FacultyClassPage> {
  MenuListProvider? menuProvider;
  Map<String, bool> isSelected = new Map<String, bool>();
  bool isAllChecked = false;

  FacultyBatchClassResponse? facultyClass;
  bool isLoading = false;
  bool? selectedAttendance = true;
  bool? selectedRecording = false;
  bool isSelectLive = false;
  bool isSelectUpcomimg = false;
  bool isSelectConcluded = false;
  bool isSelectBatch = false;
  Map<int, bool> batchSelected = Map<int, bool>();
  int? selectedDate = DateTime.now().day;
  int? selectedMonth = DateTime.now().month;
  int? selectedYear = DateTime.now().year;
  int? selctedClassStatus;
  int? selctedBatchStatus;
  bool? isShowMessage = false;
  List<LiveClass>? filteredLiveClassFromCalender = [];

  String formattedMonth = DateFormat('MMM').format(DateTime.now());
  @override
  void initState() {
    getFacultyClass();

    super.initState();
  }

  void getFacultyClass({String? selectedDate}) {
    BlocProvider.of<HomeBloc>(context)
        .add(FacultyBatchClassEvent(selectedDate: selectedDate));
  }

  @override
  Widget build(BuildContext context) {
    void _showMonthPicker(BuildContext context) async {
      try {
        DateTime? picked = await showDatePicker(
          context: context,
          firstDate: DateTime(2010),
          lastDate: DateTime(2050),
          initialDate: DateTime(
              selectedYear ?? DateTime.now().year,
              selectedMonth ?? DateTime.now().month,
              selectedDate ?? DateTime.now().day),
          builder: (BuildContext context, Widget? child) {
            return 
            Theme(
              data: 
              ThemeData.light().copyWith(
                primaryColor:
                 ColorConstants().gradientRight(),
                buttonTheme:
                    ButtonThemeData(textTheme: ButtonTextTheme.primary),
                colorScheme:
                    ColorScheme.light(primary: ColorConstants().gradientRight())
                        .copyWith(secondary: ColorConstants().gradientRight()),
              ),
              child: child!,
            );
          },
        );

        if (picked != null &&
            picked != DateTime(selectedYear!, selectedMonth!, selectedDate!)) {
          setState(() {
            selectedDate = picked.day;
            selectedMonth = picked.month;
            selectedYear = picked.year;
          });

          String formattedDate = DateFormat('yyyy-MM-dd').format(picked);
          print('Selected date is: $formattedDate');

          getFacultyClass(selectedDate: formattedDate);
          filterAssessmentsFromCalender();
        }
      } catch (e, stackTrace) {
        print('Error: $e');
        print('Stack trace: $stackTrace');
      }
    }

    return BlocManager(
        initState: (context) {},
        child: Consumer<MenuListProvider>(
            builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
                  listener: (context, state) async {
                    if (state is FacultyBatchClassState) {
                      _handleFacultyBatchClassState(state);
                    }
                  },
                  child: Scaffold(
                    appBar: AppBar(
                      elevation: 0,
                      backgroundColor: ColorConstants.WHITE,
                      leading: BackButton(color: ColorConstants.BLACK),
                      title:
                          Text('my_classes', style: Styles.bold(size: 16)).tr(),
                      actions: [
                        InkWell(
                          onTap: () {
                            _showMonthPicker(context);
                          },
                          child: SvgPicture.asset(
                            'assets/images/selected_calender.svg',
                            height: 20,
                            width: 20,
                            allowDrawingOutsideViewBox: true,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: GestureDetector(
                            onTap: () {
                              List<String?> selectedUserId = [];
                              if (isAllChecked == true) {
                                facultyClass?.data?.liveClass?.forEach((value) {
                                  selectedUserId.add(value.classStatus);
                                });
                              } else {
                                facultyClass?.data?.liveClass?.forEach((value) {
                                  if (isSelected[value.classStatus] == true) {
                                    selectedUserId.add(value.classStatus);
                                  }
                                });
                              }
                              showBottomSheet(context);
                            },
                            child: SvgPicture.asset(
                              'assets/images/filter.svg',
                              height: 15,
                              width: 15,
                              allowDrawingOutsideViewBox: true,
                            ),
                          ),
                        ),
                      ],
                    ),
                    backgroundColor: ColorConstants.GREY,
                    body: ScreenWithLoader(
                      isLoading: isLoading,
                      body: SingleChildScrollView(
                        child: todayClasses(),
                        //     facultyClass?.data?.liveClass?.length != 0
                        // ? todayClasses()
                        // : isLoading == true
                        //     ? SizedBox()
                        //     : Padding(
                        //         padding: const EdgeInsets.only(top: 300.0),
                        //         child: SizedBox(
                        //             child: Center(
                        //                 child: Text('classes_not_found',
                        //                         style: Styles.bold())
                        //                     .tr())),
                        // ),
                      ),
                    ),
                  ),
                )));
  }

  void _handleFacultyBatchClassState(FacultyBatchClassState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyBatchDetailsState.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v(
                "Success....................FacultyBatchDetailsState ${state.response?.toJson()}");

            facultyClass = state.response;
            filteredLiveClassFromCalender = state.response?.data?.liveClass;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyBatchDetailsState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void showBottomSheet(context) {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        backgroundColor: ColorConstants.WHITE,
        builder: (context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Center(
                    child: Container(
                      padding: EdgeInsets.all(10),
                      margin: EdgeInsets.only(top: 10),
                      height: 4,
                      width: 70,
                      decoration: BoxDecoration(
                          color: Colors.grey,
                          borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                  Container(
                    height: 0.5,
                    color: Colors.grey[100],
                  ),
                  Container(
                      color: ColorConstants.WHITE,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(
                          children: [
                            Text('Filter', style: Styles.bold()),
                            Spacer(),
                            GestureDetector(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: Icon(Icons.close))
                          ],
                        ),
                      )),
                  Container(
                      color: ColorConstants.WHITE,
                      child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Select Class Status',
                                    style: Styles.bold()),
                                Row(children: [
                                  Checkbox(
                                      activeColor:
                                          ColorConstants.ACTIVE_TAB_UNDERLINE,
                                      value: isSelectLive,
                                      onChanged: ((value) {
                                        setState(() {
                                          isSelectLive = true;
                                          isSelectLive = value ?? false;
                                          // getFacultyClass();
                                          // Navigator.pop(context);
                                        });
                                      })),
                                  Text('Live Classes', style: Styles.regular()),
                                ]),
                                Row(children: [
                                  Checkbox(
                                      activeColor:
                                          ColorConstants.ACTIVE_TAB_UNDERLINE,
                                      value: isSelectUpcomimg,
                                      onChanged: ((value) {
                                        setState(() {
                                          isSelectUpcomimg = true;
                                          isSelectUpcomimg = value ?? false;
                                        });
                                      })),
                                  Text('Upcoming Classes',
                                      style: Styles.regular()),
                                ]),
                                Row(children: [
                                  Checkbox(
                                      activeColor:
                                          ColorConstants.ACTIVE_TAB_UNDERLINE,
                                      value: isSelectConcluded,
                                      onChanged: ((value) {
                                        setState(() {
                                          isSelectConcluded = true;
                                          isSelectConcluded = value ?? false;
                                        });
                                      })),
                                  Text('Concluded Classes',
                                      style: Styles.regular()),
                                ]),
                                SizedBox(height: 10),
                                Text('Select Batch', style: Styles.bold()),
                                SizedBox(height: 5),
                                SizedBox(
                                  child: ListView.builder(
                                      scrollDirection: Axis.vertical,
                                      shrinkWrap: true,
                                      physics: BouncingScrollPhysics(),
                                      itemCount:
                                          facultyClass?.data?.batches?.length ??
                                              0,
                                      itemBuilder: ((context, index) {
                                        return Row(children: [
                                          Checkbox(
                                              activeColor: ColorConstants
                                                  .ACTIVE_TAB_UNDERLINE,
                                              value: batchSelected[facultyClass
                                                      ?.data
                                                      ?.batches?[index]
                                                      .id] ??
                                                  false,
                                              onChanged: ((value) {
                                                setState(() {
                                                  batchSelected[int.parse(
                                                          '${facultyClass?.data?.batches?[index].id}')] =
                                                      value ?? false;
                                                });
                                              })),
                                          Text(
                                              '${facultyClass?.data?.batches?[index].title ?? ''}',
                                              style: Styles.regular()),
                                        ]);
                                      })),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0, vertical: 16),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          getFacultyClass();
                                          Navigator.pop(context);
                                        },
                                        child: Container(
                                          height: height(context) * 0.05,
                                          width: width(context) * 0.4,
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(colors: [
                                              ColorConstants().gradientLeft(),
                                              ColorConstants().gradientRight(),
                                            ]),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          margin:
                                              EdgeInsets.only(left: 9, top: 3),
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Center(
                                                child: Text('apply',
                                                        style: Styles.textBold(
                                                            size: 12,
                                                            color: ColorConstants
                                                                .ACCENT_COLOR))
                                                    .tr()),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ]))),
                  Container(
                    height: 0.5,
                    color: Colors.grey[100],
                  ),
                ],
              ),
            );
          });
        });
  }

  void filterAssessmentsFromCalender() {
    filteredLiveClassFromCalender =
        facultyClass?.data?.liveClass?.where((assessment) {
      var assessmentDate =
          DateTime.fromMillisecondsSinceEpoch((assessment.endDate ?? 0) * 1000);
      return assessmentDate.year == selectedYear &&
          assessmentDate.month == selectedMonth;
    }).toList();

    setState(() {});
    Log.v('Filtered assessments: $filteredLiveClassFromCalender');
  }

  todayClasses() {
    List<LiveClass>? filteredLiveClass = facultyClass?.data?.liveClass;

    bool filterApplied = isSelectConcluded || isSelectUpcomimg || isSelectLive;
    List<String> appliedFilterlist = [];
    if (isSelectConcluded) {
      appliedFilterlist.add('concluded');
    }
    if (isSelectUpcomimg) {
      appliedFilterlist.add('scheduled');
    }
    if (isSelectLive) {
      appliedFilterlist.add('live');
    }

    if (filterApplied) {
      filteredLiveClass = filteredLiveClass?.where((element) {
        return appliedFilterlist.contains(element.classStatus?.toLowerCase());
      }).toList();
    } else {
      filteredLiveClass = filteredLiveClassFromCalender;
    }

    filteredLiveClass = filteredLiveClass?.where((elem) {
      return batchSelected.containsValue(true)
          ? (batchSelected[elem.batchId] == true)
          : true;
    }).toList();

    if (filteredLiveClass?.length == 0 && isLoading == false) {
      return Padding(
          padding: const EdgeInsets.only(top: 300.0),
          child: SizedBox(
              child: Center(
                  child:
                      Text('classes_not_found', style: Styles.bold()).tr())));
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: BouncingScrollPhysics(),
      itemCount: filteredLiveClass?.length,
      itemBuilder: (BuildContext context, int index) {
        return Container(
          // height: MediaQuery.of(context).size.height * 0.22,
          margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          width: MediaQuery.of(context).size.width * 0.9,
          decoration: BoxDecoration(
              color: ColorConstants.ACCENT_COLOR,
              borderRadius: BorderRadius.circular(6)),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border:
                              Border.all(color: Color(0xffF0F1FA), width: 1)),
                      margin: EdgeInsets.only(left: 9, top: 3),
                      child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: SizedBox(
                            height: 20,
                            child: filteredLiveClass != null &&
                                    filteredLiveClass.length != 0
                                ? ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    shrinkWrap: true,
                                    itemCount: jsonDecode(
                                                '${filteredLiveClass[index].batch}')
                                            .length ??
                                        0,
                                    itemBuilder: (context, index) {
                                      final decodedBatch = jsonDecode(
                                          '${filteredLiveClass?[0].batch}');
                                      if (decodedBatch.length == 0) {
                                        return SizedBox();
                                      }

                                      final batchName = decodedBatch[0]
                                              ['batch_name']
                                          .toString();
                                      return Padding(
                                          padding:
                                              const EdgeInsets.only(right: 4.0),
                                          child: ShaderMask(
                                            blendMode: BlendMode.srcIn,
                                            shaderCallback: (Rect bounds) {
                                              return LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: <Color>[
                                                    ColorConstants()
                                                        .gradientLeft(),
                                                    ColorConstants()
                                                        .gradientRight()
                                                  ]).createShader(bounds);
                                            },
                                            child: Text(batchName,
                                                // jsonDecode('${filteredLiveClass[index].batch}')[
                                                //             index]
                                                //         [
                                                //         'batch_name']
                                                //     .toString(),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                softWrap: false,
                                                style: Styles.semibold(
                                                  size: 12,
                                                )),
                                          ));
                                    })
                                : SizedBox(),
                          )),
                    ),
                    Spacer(),
                    Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border:
                              Border.all(color: Color(0xffF0F1FA), width: 1)),
                      margin: EdgeInsets.only(left: 9, top: 3),
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/images/live_icon.svg',
                              height: 20.0,
                              width: 20.0,
                              allowDrawingOutsideViewBox: true,
                            ),
                            SizedBox(width: 4),
                            Text(
                              '${filteredLiveClass?[index].classStatus ?? ''}',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              softWrap: false,
                              style: Styles.semibold(
                                  size: 12, color: Color(0xffEA575E)),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(left: 9, top: 3),
                  child: Text('${filteredLiveClass?[index].title ?? ''}',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      softWrap: false,
                      style: Styles.bold(size: 16)),
                ),
                SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Row(
                    children: [
                      SizedBox(
                        child: Row(
                          children: [
                            Icon(Icons.alarm, size: 20),
                            SizedBox(width: 4),
                            Text(
                              '${Utility.convertDateFromMillis(int.parse('${filteredLiveClass?[index].startDate ?? ''}'), '' + ' hh:mm aa')} To ${Utility.convertDateFromMillis(int.parse('${facultyClass?.data?.liveClass?[index].endDate ?? ''}'), '' + ' hh:mm a')}',
                              style: Styles.regular(size: 12),
                              textDirection: ui.TextDirection.ltr,
                            ),
                          ],
                        ),
                      ),
                      Spacer(),
                      SizedBox(
                        child: Row(
                          children: [
                            Icon(Icons.calendar_month_outlined, size: 20),
                            SizedBox(width: 4),
                            Text(
                              '${Utility.convertDateFromMillis(int.parse('${facultyClass?.data?.liveClass?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ',
                              style: Styles.regular(size: 12),
                              textDirection: ui.TextDirection.ltr,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    if (facultyClass?.data?.liveClass?[index].recordUrl != null)
                      InkWell(
                        onTap: () {
                          setState(() {
                            selectedRecording = true;
                            selectedAttendance = false;
                          });
                          NextPageRoute(
                            VideoPlayerScreen(
                              recordUrl:
                                  '${facultyClass?.data?.liveClass?[index].recordUrl ?? ''}',
                            ),
                          );
                        },
                        child: Container(
                          height: height(context) * 0.05,
                          width: width(context) * 0.4,
                          decoration: BoxDecoration(
                              color: selectedRecording == true
                                  ? Color(0xff3CA4D2)
                                  : ColorConstants.WHITE,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: Color(0xffF0F1FA), width: 1)),
                          margin: EdgeInsets.only(left: 9, top: 3),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Center(
                                child: Text('view_recording',
                                        style: Styles.textBold(
                                            size: 12,
                                            color: selectedRecording == true
                                                ? ColorConstants.WHITE
                                                : Color(0xff3CA4D2)))
                                    .tr()),
                          ),
                        ),
                      ),
                    Spacer(),
                    Center(
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            selectedAttendance = true;
                            selectedRecording = false;
                          });
                          Navigator.push(
                              context,
                              NextPageRoute(AttendanceViewScreen(
                                  recordUrl: facultyClass
                                      ?.data?.liveClass?[index].recordUrl,
                                  classId: facultyClass
                                          ?.data?.liveClass?[index].id ??
                                      0,
                                  title: jsonDecode(
                                              '${facultyClass?.data?.liveClass?[index].batch}')[
                                          index]['batch_name']
                                      .toString(),
                                  batch:
                                      '${facultyClass?.data?.liveClass?[index].batch}',
                                  startDate:
                                      '${Utility.convertDateFromMillis(int.parse('${facultyClass?.data?.liveClass?[index].startDate ?? ''}'), '' + ' hh:mm aa')} To ${Utility.convertDateFromMillis(int.parse('${facultyClass?.data?.liveClass?[index].endDate ?? ''}'), '' + ' hh:mm a')}',
                                  classStatus:
                                      '${facultyClass?.data?.liveClass?[index].classStatus}',
                                  endDate:
                                      '${Utility.convertDateFromMillis(int.parse('${facultyClass?.data?.liveClass?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ')));
                        },
                        child: Container(
                          height: height(context) * 0.05,
                          width: width(context) * 0.4,
                          decoration: BoxDecoration(
                            gradient: selectedAttendance == true
                                ? LinearGradient(colors: [
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight(),
                                  ])
                                : LinearGradient(colors: [
                                    ColorConstants.WHITE,
                                    ColorConstants.WHITE,
                                  ]),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          margin: EdgeInsets.only(left: 9, top: 3),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Center(
                                child: Text('View Attendance',
                                    style: Styles.textBold(
                                        size: 12,
                                        color: ColorConstants.ACCENT_COLOR))),
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
