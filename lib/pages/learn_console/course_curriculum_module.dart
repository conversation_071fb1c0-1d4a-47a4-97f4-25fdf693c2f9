import 'dart:math';
import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
//import 'package:flutter_html/style.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/assign_learner_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_details_resp.dart';
import 'package:masterg/data/models/response/home_response/training_detail_response.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/providers/training_detail_provider.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/certificate_container.dart';
import 'package:masterg/pages/training_pages/program_content/program_content_provider.dart';

import 'package:masterg/pages/training_pages/program_content/widgets/filter_card.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';

import '../../../utils/config.dart';
import '../custom_pages/custom_widgets/CommonHTMLWebview.dart';
import '../custom_pages/custom_widgets/NextPageRouting.dart';

class CourseCurriculumModules extends StatefulWidget {
  final List<Modules>? module;
  final int? programId;

  const CourseCurriculumModules({
    super.key,
    required this.module,
    this.programId,
  });

  @override
  State<CourseCurriculumModules> createState() =>
      _CourseCurriculumModulesState();
}

class _CourseCurriculumModulesState extends State<CourseCurriculumModules> {
  TabController? tabController;
  int? selectedIndex = 0;
  final TextEditingController _searchController = TextEditingController();
  bool? isLoading = false;
  AssignLearnerResponse? assignLearner;

  @override
  void initState() {
    //getAssignLearner(isFaculty: 1, programId: widget.programId);
    callState();
    super.initState();
  }

  @override
  void dispose() {
    tabController?.dispose();
    super.dispose();
  }

  void callState() {
    Future.delayed(const Duration(seconds: 4), () {
      print('Task 2: Executed after 4 seconds');
      setState(() {
        getAssignLearner(isFaculty: 1, programId: widget.programId);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    List<ExpandableController> _expandableController = [];
    TrainingDetailProvider trainingDetailProvider =
        Provider.of<TrainingDetailProvider>(context, listen: false);

    return ChangeNotifierProvider<ProgramContentProvider>(
      create: (context) => ProgramContentProvider(
        widget.module ?? [],
      ),
      child:
          Consumer<ProgramContentProvider>(builder: ((context, value, child) {

        if (value.loading ||
            (value.shortContentList.isEmpty && value.selectedContent == null)) {
          return Center(
            child: CircularProgressIndicator(),
          );
        }
        return OrientationBuilder(
            builder: (BuildContext context, Orientation orientation) {
          if (kIsWeb) orientation = Orientation.portrait;

          _expandableController.addAll(List.generate(
              max(1, value.modules?.length ?? 0),
              (index) => new ExpandableController(initialExpanded: true)));


          return BlocListener<HomeBloc, HomeState>(
              listener: (context, state) async {
                if (state is AssignLearnerState) {
                  handleAssignLearnerState(state);
                }
              },
              child: DefaultTabController(
                length: 2,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                        height: 40,
                        child: TabBar(
                          indicatorColor: Colors.red,
                          isScrollable: true, // Required
                          labelColor: Colors.red,
                          unselectedLabelColor:
                              Colors.black, // Other tabs color
                          labelPadding: EdgeInsets.symmetric(
                              horizontal: 50), // Space between tabs
                          indicator: UnderlineTabIndicator(
                            borderSide: BorderSide(
                                color: Colors.red,
                                width: 2), // Indicator height
                          ),
                          tabs: [
                            Tab(text: tr('module_curriculum')),
                            Tab(text: tr('learner')),
                          ],
                        ),
                      ),
                      SizedBox(height: 20),
                      SizedBox(
                        height: height(context) * 0.7,
                        child: TabBarView(
                            children: [
                              //TODO: Module Curriculum ------ assignLearner?.data?.data?.length != 0
                          value.shortContentListFiltered.isNotEmpty == true &&
                              value.filtermodules?.length != 0
                              ? Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 5, vertical: 16),
                            child: Container(
                              height: 300,
                              decoration: BoxDecoration(
                                  color: ColorConstants.WHITE,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                      color: Color(0xffF0F1FA),
                                      width: 2)),
                              child: Padding(
                                padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0, bottom: 40.0),
                                child: SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      /*Row(
                                        children: [
                                          Container(
                                            height: 60,
                                            width: 5,
                                            margin: EdgeInsets.only(
                                                left: 9, top: 3),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                              BorderRadius.circular(
                                                  10),
                                              color: Color(0xff3CA4D2),
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          Text('module_curriculum',
                                              style:
                                              Styles.bold(size: 18)).tr(),
                                        ],
                                      ),*/

                                      ListView.builder(
                                          itemCount: value.filtermodules?.length ?? 0,
                                          shrinkWrap: true,
                                          physics:
                                          BouncingScrollPhysics(),
                                          itemBuilder: (context, modIndex) {
                                            String? subjectName = '';
                                            try {
                                              if(modIndex >= 1){
                                                subjectName = '${trainingDetailProvider.skills['${value.modules?[modIndex-1].skillId ?? 0}']['name']}';
                                              }

                                            } catch (e) {}

                                            return Column(
                                              children: [
                                                //Divider(),
                                                subjectName != trainingDetailProvider.skills['${value.modules?[modIndex].skillId ?? 0}']['name'] ?
                                                Row(
                                                  children: [
                                                    Container(
                                                      height: 50,
                                                      width: 5,
                                                      margin: EdgeInsets.only(
                                                          left: 0, top: 0, bottom: 0),
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                        color: Color(0xff3CA4D2),
                                                      ),
                                                    ),
                                                    Padding(
                                                      padding: const EdgeInsets.only(left: 8.0),
                                                      child: Column(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: [
                                                          Text('${trainingDetailProvider.skills['${value.modules?[modIndex].skillId ?? 0}']['name']}',
                                                              style:
                                                              Styles.bold(size: 16)).tr(),

                                                          GestureDetector(
                                                            onTap: () {
                                                              Navigator.push(
                                                                context,
                                                                MaterialPageRoute(
                                                                  builder: (context) {
                                                                    return CommonHTMLWebView(
                                                                      title:
                                                                      '${trainingDetailProvider.skills['${value.modules?[modIndex].skillId}']['name']}',
                                                                      url:
                                                                      '${trainingDetailProvider.skills['${value.modules?[modIndex].skillId}']['description']}',
                                                                      addHtmlTag: true,
                                                                    );
                                                                  },
                                                                ),
                                                              );
                                                            },
                                                            child: Padding(
                                                              padding: const EdgeInsets.only(top: 5.0),
                                                              child: Row(
                                                                children: [
                                                                  Text('description_view', style: TextStyle(fontSize: 12),).tr(),
                                                                  Padding(
                                                                    padding: const EdgeInsets.only(left: 8.0),
                                                                    child: Icon(Icons.remove_red_eye, size: 15, color: Colors.grey),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ):SizedBox(),

                                                SizedBox(height: 20,),
                                                Container(
                                                  // margin: const EdgeInsets.only(
                                                  //     left: 15, right: 15),
                                                  padding: EdgeInsets
                                                      .symmetric(
                                                      vertical: 4,
                                                      horizontal: 0),
                                                  decoration:
                                                  BoxDecoration(
                                                    color: ColorConstants
                                                        .WHITE,
                                                  ),
                                                  child: ExpandablePanel(
                                                      controller: _expandableController
                                                          .length >
                                                          modIndex
                                                          ? _expandableController[
                                                      modIndex]
                                                          : ExpandableController(),
                                                      theme:
                                                      ExpandableThemeData(
                                                        hasIcon: true,
                                                      ),
                                                      header: Column(
                                                        mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .start,
                                                        crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                        children: [
                                                          Text(
                                                              '${value.filtermodules?[modIndex].name ?? ''}',
                                                              // '${trainingDetailProvider.modules?[modIndex].name}',
                                                              maxLines: 1,
                                                              overflow:
                                                              TextOverflow
                                                                  .ellipsis,
                                                              softWrap:
                                                              false,
                                                              style: Styles
                                                                  .semibold(
                                                                  size:
                                                                  16)),
                                                          Utility().isHtml('${value.filtermodules?[modIndex].description ?? ''}') ? GestureDetector(
                                                            onTap: () {
                                                              Navigator.push(
                                                                context,
                                                                MaterialPageRoute(
                                                                  builder: (context) {
                                                                    return CommonHTMLWebView(
                                                                      title:
                                                                      '${value.filtermodules?[modIndex].name}',
                                                                      url:
                                                                      '${value.filtermodules?[modIndex].description}',
                                                                      addHtmlTag: true,
                                                                    );
                                                                  },
                                                                ),
                                                              );
                                                            },
                                                            child: Html(
                                                              data: '${value.filtermodules?[modIndex].description ?? ''}',
                                                            ),
                                                          ) : Text(
                                                            '${value.filtermodules?[modIndex].description ?? ''}',
                                                            // '${trainingDetailProvider.modules?[modIndex].description}',
                                                            maxLines: 1,
                                                            overflow:
                                                            TextOverflow
                                                                .ellipsis,
                                                            softWrap:
                                                            false,
                                                            style: Styles
                                                                .regular(
                                                                size:
                                                                14),
                                                          )
                                                        ],
                                                      ),
                                                      collapsed: SizedBox(
                                                        height: 0,
                                                      ),
                                                      expanded: ListView.builder(
                                                        shrinkWrap: true,
                                                        physics:
                                                        BouncingScrollPhysics(),
                                                        itemCount: value.shortContentListFiltered[modIndex].length?? 0,
                                                        itemBuilder:
                                                            (context,
                                                            index) {
                                                          if (value.shortContentListFiltered[modIndex]
                                                          [index]
                                                          ['sorted_content'] == 'sessions') {
                                                            dynamic
                                                            sessionData =
                                                            Sessions.fromJson(
                                                                value.shortContentListFiltered[modIndex]
                                                                [
                                                                index]);
                                                            return _moduleCard(
                                                                provider:
                                                                value,
                                                                leadingid: Utility
                                                                    .classStatus(
                                                                    int.parse(
                                                                        '${sessionData.startDate}'),
                                                                    int
                                                                        .parse(
                                                                      '${sessionData.endDate}',
                                                                    ),
                                                                    currentIndiaTime!),
                                                                time:
                                                                ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(sessionData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''} ',
                                                                title:
                                                                '${sessionData.title}',
                                                                description: sessionData.contentType?.toLowerCase() ==
                                                                    'teamsclass'
                                                                    ? 'teams'
                                                                    : '${sessionData.contentType?.toLowerCase() == 'otherclass' ? 'weblink' : sessionData.contentType?.toLowerCase() == 'liveclass' || sessionData.contentType?.toLowerCase() == 'zoomclass' ? 'Live' : 'Classroom'}',
                                                                //type: 'session',
                                                                type: value.shortContentListFiltered[modIndex][index]['content_type'],
                                                                data:
                                                                sessionData,
                                                                index:
                                                                index,
                                                                context:
                                                                context,
                                                                showLiveStatus: Utility.isBetween(
                                                                    int.parse(
                                                                        '${sessionData.startDate}'),
                                                                    int.parse(
                                                                        '${sessionData.endDate}'),
                                                                    currentIndiaTime!));
                                                          } else if (value.shortContentListFiltered[modIndex]
                                                          [index]
                                                          ['sorted_content'] == 'assessments') {
                                                            dynamic
                                                            assessmentData =
                                                            Assessments.fromJson(
                                                                value.shortContentListFiltered[modIndex]
                                                                [
                                                                index]);

                                                            return _moduleCard(
                                                              provider:
                                                              value,
                                                              leadingid: Utility.classStatus(
                                                                  int.parse('${assessmentData.startDate}'),
                                                                  int.parse(
                                                                    '${assessmentData.endDate}',
                                                                  ),
                                                                  currentIndiaTime!),
                                                              time:
                                                              ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(assessmentData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
                                                              title:
                                                              '${assessmentData.title}',
                                                              description:
                                                              '${capitalize(assessmentData.contentType)}',
                                                              //type: 'assessment',
                                                              type: value.shortContentListFiltered[modIndex][index]['content_type'],
                                                              data:
                                                              assessmentData,
                                                              index:
                                                              index,
                                                              context:
                                                              context,
                                                            );
                                                          } else if (value.shortContentListFiltered[modIndex]
                                                          [index]
                                                          ['sorted_content'] == 'assignments') {
                                                            dynamic
                                                            assignmentData =
                                                            Assignments.fromJson(
                                                                value.shortContentListFiltered[modIndex]
                                                                [
                                                                index]);

                                                            return _moduleCard(
                                                              provider:
                                                              value,
                                                              leadingid: Utility.classStatus(
                                                                  int.parse(
                                                                      '${assignmentData.startDate}'),
                                                                  int.parse(
                                                                      '${assignmentData.endDate}'),
                                                                  currentIndiaTime!),
                                                              time:
                                                              ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(assignmentData.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
                                                              title:
                                                              '${assignmentData.title}',
                                                              description:
                                                              '${capitalize(assignmentData.contentType)}',
                                                              //type: 'assignment',
                                                              type: value.shortContentListFiltered[modIndex][index]['content_type'],
                                                              data:
                                                              assignmentData,
                                                              index:
                                                              index,
                                                              context:
                                                              context,
                                                            );
                                                          } else if (value.shortContentListFiltered[modIndex]
                                                          [index]
                                                          ['sorted_content'] == 'learning_shots') {
                                                            dynamic
                                                            learningShorts =
                                                            LearningShots.fromJson(
                                                                value.shortContentListFiltered[modIndex]
                                                                [
                                                                index]);

                                                            return _moduleCard(
                                                              provider:
                                                              value,
                                                              leadingid: learningShorts.completion ==
                                                                  100.0
                                                                  ? 2
                                                                  : learningShorts.completion !=
                                                                  0.0
                                                                  ? 3
                                                                  : 1,
                                                              //time: ' ${Utility().isRTL(context) ? '' : '•'} ${learningShorts.contentType == 'notes' ? learningShorts.noPages : learningShorts.durationInMinutes} ${learningShorts.contentType == 'notes' ? tr('page') : learningShorts.durationInMinutes == 0 || learningShorts.durationInMinutes == 1 ? tr('min') : tr('mins')} ${Utility().isRTL(context) ? '•' : ''}',

                                                              time: value.shortContentListFiltered[modIndex][index]['content_type'] != 'text' ?
                                                              ' ${Utility().isRTL(context) ? '' : '•'} ${learningShorts.contentType == 'notes' ? learningShorts.noPages : learningShorts.durationInMinutes} ${learningShorts.contentType == 'notes' ? tr('page') : learningShorts.durationInMinutes == 0 || learningShorts.durationInMinutes == 1 ? tr('min') : tr('mins')} ${Utility().isRTL(context) ? '•' : ''}'
                                                                  :'',

                                                              title:'${learningShorts.title}',
                                                              description:
                                                              '${capitalize(learningShorts.contentType)}',
                                                              //type: 'learningShots',
                                                              type: value.shortContentListFiltered[modIndex][index]['content_type'],
                                                              data:
                                                              learningShorts,
                                                              index:
                                                              index,
                                                              context:
                                                              context,
                                                            );
                                                          } else if (value.shortContentListFiltered[modIndex]
                                                          [index]
                                                          ['sorted_content'] == 'interactive_content') {
                                                            dynamic
                                                            learningShorts =
                                                            LearningShots.fromJson(
                                                                value.shortContentListFiltered[modIndex]
                                                                [
                                                                index]);

                                                            return _moduleCard(
                                                              provider:
                                                              value,
                                                              leadingid: learningShorts.completion ==
                                                                  100.0
                                                                  ? 2
                                                                  : learningShorts.completion !=
                                                                  0.0
                                                                  ? 3
                                                                  : 1,
                                                              time:
                                                              ' ${Utility().isRTL(context) ? '' : '•'} ${learningShorts.contentType == 'notes' ? learningShorts.noPages : learningShorts.durationInMinutes} ${learningShorts.contentType == 'notes' ? tr('page') : learningShorts.durationInMinutes == 0 || learningShorts.durationInMinutes == 1 ? tr('min') : tr('mins')} ${Utility().isRTL(context) ? '•' : ''}',
                                                              // time:
                                                              //     ' ${Utility().isRTL(context) ? '' : '•'} ${learningShorts.durationInMinutes} ${learningShorts.durationInMinutes == 1 ? tr('min') : tr('mins')} ${Utility().isRTL(context) ? '•' : ''}',
                                                              title:
                                                              '${learningShorts.title}',
                                                              description:
                                                              '${capitalize(learningShorts.contentType)}',
                                                              //type: 'learningShots',
                                                              type: value.shortContentListFiltered[modIndex][index]['content_type'],
                                                              data:
                                                              learningShorts,
                                                              index:
                                                              index,
                                                              context:
                                                              context,
                                                            );
                                                          } else if (value.shortContentListFiltered[modIndex]
                                                          [index]
                                                          ['sorted_content'] == 'scorm') {
                                                            dynamic
                                                            scormContent =
                                                            Scorm.fromJson(
                                                                value.shortContentListFiltered[modIndex]
                                                                [
                                                                index]);

                                                            return _moduleCard(
                                                              provider:
                                                              value,
                                                              leadingid: Utility.classStatus(
                                                                  int.parse(
                                                                      '${scormContent.startDate}'),
                                                                  int.parse(
                                                                      '${scormContent.endDate}'),
                                                                  currentIndiaTime!),
                                                              time:
                                                              ' ${Utility().isRTL(context) ? '' : '•'} ${Utility.convertCourseTime(scormContent.startDate, Strings.REQUIRED_DATE_HH_MM_A_DD_MMM)} ${Utility().isRTL(context) ? '•' : ''}',
                                                              title:
                                                              '${scormContent.title}',
                                                              description:
                                                              '${capitalize(scormContent.contentType)}',
                                                              //type: 'scorm',
                                                              type: value.shortContentListFiltered[modIndex][index]['content_type'],
                                                              data:
                                                              scormContent,
                                                              index:
                                                              index,
                                                              context:
                                                              context,
                                                            );
                                                          }


                                                          return SizedBox(); // New Code
                                                          //TODO: OLD Code
                                                          /*return Padding(
                                                            padding:
                                                            const EdgeInsets
                                                                .all(
                                                                20.0),
                                                            child: SizedBox(
                                                                height: 20,
                                                                child: Center(
                                                                    child: Text(
                                                                      'no_data_found',
                                                                      style: Styles
                                                                          .bold(),
                                                                    ))), //no_data_found
                                                          );*/
                                                        },
                                                      )),
                                                ),
                                              ],
                                            );
                                          }),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                              : Container(
                            width: width(context) * 0.8,
                            height: 400,
                            child: Center(
                              child: Text(
                                '${tr('lbl_no_available_module')}',
                                style: Styles.regular(),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),

                          //TODO: Module Learner
                              if(isLoading == false)...[
                                assignLearner?.data?.data?.length != null &&
                                    assignLearner?.data?.data?.length != 0
                                    ? SingleChildScrollView(
                                  child: Column(
                                    children: [
                                      Center(
                                        child: Padding(
                                          padding:
                                          const EdgeInsets.only(top: 10.0),
                                          child: Container(
                                            child: ListView.builder(
                                                shrinkWrap: true,
                                                physics: BouncingScrollPhysics(),
                                                itemCount: assignLearner
                                                    ?.data?.data?.length ??
                                                    0,
                                                itemBuilder:
                                                    (BuildContext context,
                                                    int index) {
                                                  return Container(
                                                      padding: EdgeInsets.all(8),
                                                      margin: EdgeInsets.all(8),
                                                      decoration: BoxDecoration(
                                                          color: ColorConstants
                                                              .WHITE,
                                                          borderRadius:
                                                          BorderRadius
                                                              .circular(10),
                                                          border: Border.all(
                                                              color: Color(
                                                                  0xffF0F1FA),
                                                              width: 2)),
                                                      child: Column(
                                                          crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                          children: [
                                                            Row(
                                                              mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .spaceBetween,
                                                              children: [
                                                                Text(
                                                                    '${assignLearner?.data?.data?[index].name ?? ''}',
                                                                    style: Styles
                                                                        .bold(
                                                                        size:
                                                                        14)),
                                                                Padding(
                                                                  padding:
                                                                  const EdgeInsets
                                                                      .all(
                                                                      4.0),
                                                                  child: InkWell(
                                                                    onTap: () {
                                                                      setState(
                                                                              () {
                                                                            assignLearner
                                                                                ?.data
                                                                                ?.data
                                                                                ?.removeAt(
                                                                                index);
                                                                          });
                                                                    },
                                                                    child: Icon(
                                                                        Icons
                                                                            .close,
                                                                        size: 15),
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                            Text(
                                                                '${assignLearner?.data?.data?[index].mobileNo ?? ''}',
                                                                style: Styles
                                                                    .textRegular(
                                                                    size:
                                                                    12)),
                                                            Text(
                                                                '${assignLearner?.data?.data?[index].email ?? ''}',
                                                                style: Styles
                                                                    .textRegular(
                                                                    size:
                                                                    14)),
                                                            Text(
                                                                '${assignLearner?.data?.data?[index].batchName ?? ''}',
                                                                style: Styles
                                                                    .textRegular(
                                                                    size:
                                                                    14)),
                                                          ]));

                                                }),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                )
                                    : Center(
                                    child: SizedBox(
                                        height: 0,
                                        child: Text('',
                                            style: Styles.bold()).tr())),//no_data
                              ]else...[
                                Center(
                                  child: CircularProgressIndicator()),
                              ],

                        ]),
                      ),
                    ],
                  ),
                ),
              ));
        });
      })),
    );
  }

  Widget _moduleCard(
      {required String time,
      required String title,
      required String description,
      required String type,
      required dynamic data,
      required int index,
      required context,
      required ProgramContentProvider provider,
      int leadingid = 1,
      bool showNotificationIcon = false,
      bool showLiveStatus = false}) {
    bool isSelected = false;
    try {
      isSelected =
          provider.selectedContent.programContentId == data.programContentId;
    } catch (e) {}
    return InkWell(
      onTap: () async {},
      child: Stack(
        children: [
          Container(
            width: MediaQuery
                .of(context)
                .size
                .width,
            decoration: BoxDecoration(
              color: ui.Color.fromARGB(255, 255, 255, 255),
            ),
            padding: EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (type == 'session') ...[
                  // SvgPicture.asset(
                  //   'assets/images/video_icon.svg',
                  //   allowDrawingOutsideViewBox: true,
                  // ),
                  //Icon(Icons.videocam_off),
                ] else if (type == 'offlineclass') ...[
                    Icon(Icons.videocam_off_outlined, size: 18,),
                  ] else if (type == 'video_yts') ...[
                      Icon(Icons.video_collection_outlined , size: 18,),

                    ] else if (type == 'video') ...[
                        Icon(Icons.videocam_outlined, size: 18,),
                      ] else if (type == 'notes') ...[
                      Icon(Icons.picture_as_pdf_outlined, size: 18,)
                    ]else if (type == 'zoomclass') ...[
                        Icon(Icons.video_call_outlined, size: 18,)

                      ]else if (type == 'teamsclass') ...[
                        Icon(Icons.group_add_outlined, size: 18,)

                      ]else if (type == 'otherclass') ...[
                        Icon(Icons.class_outlined, size: 18,)

                      ]else if (type == 'interactive_content') ...[
                        Icon(Icons.web_rounded, size: 18,)
                      ]
                      else if (type == 'text') ...[
                        Icon(Icons.text_snippet_outlined, size: 18,)

                      ] else if (type == 'quiz') ...[
                        SvgPicture.asset(
                          'assets/images/quizz.svg',
                          allowDrawingOutsideViewBox: true,
                        ),
                      ] else if (type == 'assignment') ...[
                          Icon(Icons.assignment_outlined, size: 18,)
                        ] else if (type == 'notes') ...[
                            Icon(Icons.notes_outlined, size: 18,)
                          ]else if (type == 'scorm') ...[
                              Icon(Icons.web_outlined, size: 18,)
                            ],
                SizedBox(width: 10),
                Container(
                  width: MediaQuery
                      .of(context)
                      .size
                      .width * (0.8 - 0.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              '$title',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              softWrap: true,
                              style: Styles.semibold(size: 16),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            '${description.toLowerCase() == 'video_yts' ? tr(
                                'video') : tr('$description'.toLowerCase())}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(
                              size: 14,
                            ),
                          ),
                          // SizedBox(width: 5),
                          Text(
                            '${time.replaceAll("null", "0")}',
                            textDirection: ui.TextDirection.ltr,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(
                              size: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String capitalize(String? letter) {
    return "${letter![0].toUpperCase()}${letter.substring(1).toLowerCase()}";
  }

  void getAssignLearner({int? isFaculty, int? programId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(AssignLearnerEvent(isFaculty: 1, programId: programId));
  }

  void handleAssignLearnerState(AssignLearnerState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyAssignLearnerState.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................FacultyAssignLearnerState");
            assignLearner = state.response;
            this.setState(() {});
            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyAssignLearnerState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }
}
