import 'dart:ui' as ui;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/Attendance_percentage_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_batch_details_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/program_completion_resp.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/learn_console/leader_console_program_details.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

class ModuleLeaderConsoleCard extends StatefulWidget {
  final FacultyBatchDetailsResponse? programModuleLead;
  final int index;
  final ProgramCompletionResponse? programCompletion;
  final AttendancePercentageResponse? attendancePercentage;
  final Function onExpension;

  const ModuleLeaderConsoleCard(
      {super.key,
      this.programModuleLead,
      required this.index,
      this.programCompletion,
      this.attendancePercentage,
      required this.onExpension});

  @override
  State<ModuleLeaderConsoleCard> createState() =>
      _ModuleLeaderConsoleCardState();
}

class _ModuleLeaderConsoleCardState extends State<ModuleLeaderConsoleCard> {
  bool isModuleExpanded = false;
  var formatter = NumberFormat("#.00");

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      InkWell(
        onTap: () {
          Navigator.push(
              context,
              NextPageRoute(LeaderProgramDetailsPage(
                image:
                    '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].image}',
                programName: widget.programModuleLead?.data
                    ?.programsModuleLead?[widget.index].name,
                description: widget.programModuleLead?.data
                    ?.programsModuleLead?[widget.index].description,
                startDate:
                    '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].startDate}',
                endDate:
                    '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].endDate}',
                facultyNames:
                    '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].facultyNames}',
                totLearners:
                    '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].totLearners}',
                programId: widget.programModuleLead?.data
                    ?.programsModuleLead?[widget.index].id,
                status:
                    '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].status}',
                category:
                    '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].createdBy}',
              ), isMaintainState: true));
        },
        child: Container(
          height: MediaQuery.of(context).size.height * 0.35,
          margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          width: widget.programModuleLead?.data?.programsModuleLead?.length == 1
              ? MediaQuery.of(context).size.width * 0.95
              : MediaQuery.of(context).size.width * 0.88,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(6)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 85,
                      height: 90,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CachedNetworkImage(
                          imageUrl:
                              '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].image ?? ''}',
                          width: 100,
                          height: 120,
                          errorWidget: (context, url, error) =>
                              SvgPicture.asset(
                            'assets/images/gscore_postnow_bg.svg',
                          ),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                                width: width(context) * 0.38,
                                margin: EdgeInsets.only(left: 9, top: 3),
                                child: Text(
                                    '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].name ?? ''}',
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    softWrap: false,
                                    style: Styles.semibold(size: 16))),
                            // SizedBox(width: 80),
                            widget
                                        .programModuleLead
                                        ?.data
                                        ?.programsModuleLead?[widget.index]
                                        .batchName ==
                                    null
                                ? SizedBox()
                                : Container(
                                    width: width(context) * 0.20,
                                    decoration: BoxDecoration(
                                        color: Color(0xffF0F1FA),
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all(
                                            color: Color(0xffF0F1FA),
                                            width: 2)),
                                    margin: EdgeInsets.only(left: 9, top: 3),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Center(
                                        child: ShaderMask(
                                           blendMode: BlendMode.srcIn,
                                      shaderCallback: (Rect bounds) {
                                        return LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: <Color>[
                                              ColorConstants().gradientLeft(),
                                              ColorConstants().gradientRight()
                                            ]).createShader(bounds);
                                      },

                                          child: Text(
                                              '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].batchName ?? ''}',
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                              softWrap: false,
                                              style: Styles.semibold(
                                                  size: 12,
                                                  )),
                                        ),
                                      ),
                                    )),
                          ],
                        ),
                        Container(
                            width: width(context) * 0.5,
                            margin: EdgeInsets.only(left: 0, top: 3),
                            child: Utility().isHtml('${widget.programModuleLead?.data?.programsModuleLead?[widget.index].description ?? ''}') ? Html(
                              data: '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].description ?? ''}',
                            ) : Text(
                                '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].description ?? ''}',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.regular(size: 12))),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                  width: width(context) * 0.6,
                  margin: EdgeInsets.only(left: 9, top: 3),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_month_outlined, size: 20),
                      SizedBox(width: 10),
                      SizedBox(
                        child: Text(
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          '${Utility.convertDateFromMillis(int.parse('${widget.programModuleLead?.data?.programsModuleLead?[widget.index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} To  ${Utility.convertDateFromMillis(int.parse('${widget.programModuleLead?.data?.programsModuleLead?[widget.index].endDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)}',
                          style: Styles.semibold(
                              size: 12, color: Color(0xff0E1638)),
                          textDirection: ui.TextDirection.ltr,
                        ),
                      ),
                    ],
                  )),
              Container(
                width: width(context),
                margin: EdgeInsets.only(left: 9, top: 9, right: 9),
                child: Row(
                  children: [
                    SvgPicture.asset(
                      'assets/images/person.svg',
                      height: 20.0,
                      width: 20.0,
                      allowDrawingOutsideViewBox: true,
                    ),
                    SizedBox(width: 10),
                    Text(
                        '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].facultyNames ?? ''}',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.regular(size: 12)),
                    Spacer(),
                    Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/local_library.svg',
                          height: 20.0,
                          width: 20.0,
                          allowDrawingOutsideViewBox: true,
                        ),
                        SizedBox(width: 5),
                        Text(
                            '${widget.programModuleLead?.data?.programsModuleLead?[widget.index].totLearners ?? ''}',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(size: 12)),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Container(
            height: isModuleExpanded == true ? 130 : 60,
            margin: EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
                color:  Color(0xffF1FBFF),
                borderRadius: BorderRadius.all(Radius.circular(10)),
                border: Border.all(color: Color(0xffF1FBFF))),
            child: SizedBox(
              child: ExpansionTile(
                key: Key(widget.index.toString()),
                collapsedTextColor: ColorConstants.BLACK,
                initiallyExpanded: false,
                title: Text(
                  'batch_completion22',
                  style: Styles.bold(),
                ).tr(),
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${tr('program_completion')} ${formatter.format(widget.programCompletion?.programCompletion ?? 0.0 ?? 0)}%',
                          style: Styles.regular(),
                        ).tr(),
                        SizedBox(height: 5),
                        ClipRRect(
                          borderRadius: BorderRadius.all(Radius.circular(30)),
                          child: LinearProgressIndicator(
                            minHeight: 6,
                            value:
                                (widget.programCompletion?.programCompletion ??
                                        0) /
                                    100,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              ColorConstants().gradientLeft(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                onExpansionChanged: ((newState) {
                  if (isModuleExpanded == false) {
                    widget.onExpension();
                    setState(() {
                      isModuleExpanded = true;
                    });
                  } else {
                    setState(() {
                      isModuleExpanded = false;
                    });
                  }
                }),
              ),
            )
            //
            ),
      ),
    ]);
  }
}
