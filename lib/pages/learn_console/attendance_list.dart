import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/mark_attendance_resp.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';

class AttendanceList extends StatefulWidget {
  List<LiveClassUser>? liveClassUser;

  final String? searchString;
  AttendanceList({super.key, this.liveClassUser, required this.searchString});

  @override
  State<AttendanceList> createState() => _AttendanceListState();
}

class _AttendanceListState extends State<AttendanceList> {
  MarkAttendanceResponse? markAttendance;
  bool? isPresent = false;
  bool? isAbsent = false;
  bool? isINV = false;
  int? selectedBatch;
  bool isChecked = false;
  List<bool> checklistSelections = [];
  @override
  Widget build(BuildContext context) {
    log("your search value is ${widget.searchString}");

    if (widget.searchString != null) {
      widget.liveClassUser = widget.liveClassUser
          ?.where((element) => element.name!
              .toLowerCase()
              .contains(widget.searchString!.toLowerCase()))
          .toList();
    }

    return Column(
      children: [
        Container(
          color: Color(0xffEAECF0),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                Checkbox(
                    activeColor: ColorConstants.ACTIVE_TAB_UNDERLINE,
                    value: isChecked,
                    onChanged: ((value) {
                      setState(() {
                        isChecked = value ?? false;
                      });
                    })),
                Text('user_id', style: Styles.regular()).tr(),
                Spacer(),
                Text('class_attendence').tr(),
                SizedBox(width: 5),
                Icon(Icons.info_outline)
              ],
            ),
          ),
        ),
        Divider(
          thickness: 1,
        ),
        ListView.builder(
            physics: BouncingScrollPhysics(),
            itemCount: widget.liveClassUser?.length ?? 0,
            shrinkWrap: true,
            itemBuilder: ((context, index) {
              return Column(
                children: [
                  Container(
                    // color: Color(0xffEAECF0),
                    height: 75,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                activeColor:
                                    ColorConstants.ACTIVE_TAB_UNDERLINE,
                                value: isChecked,
                                onChanged: (value) {
                                  setState(() {
                                    isChecked = value ?? false;
                                  });
                                },
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                      '${widget.liveClassUser?[index].name ?? ''}',
                                      style: Styles.bold(size: 12)),
                                  Text(
                                      '${widget.liveClassUser?[index].email ?? ''}',
                                      style: Styles.regular(size: 12)),
                                ],
                              ),
                              Spacer(),
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    isPresent = true;
                                    isINV = false;
                                    isAbsent = false;
                                  });
                                },
                                child: InkWell(
                                  onTap: () {},
                                  child: Container(
                                    height: MediaQuery.of(context).size.height *
                                        0.045,
                                    margin: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 10),
                                    width: MediaQuery.of(context).size.width *
                                        0.10,
                                    decoration: BoxDecoration(
                                        color: isPresent == true
                                            ? Color.fromARGB(255, 155, 189, 156)
                                            : ColorConstants.WHITE,
                                        borderRadius:
                                            BorderRadius.circular(100)),
                                    child: Center(
                                        child: Text('P',
                                            style: Styles.bold(size: 12))),
                                  ),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    isAbsent = true;
                                    isINV = false;
                                    isPresent = false;
                                  });
                                },
                                child: Container(
                                  height: MediaQuery.of(context).size.height *
                                      0.045,
                                  margin: EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  width:
                                      MediaQuery.of(context).size.width * 0.10,
                                  decoration: BoxDecoration(
                                      color: isAbsent == true
                                          ? Colors.red[300]
                                          : ColorConstants.WHITE,
                                      borderRadius: BorderRadius.circular(100)),
                                  child: Center(
                                      child: Text('A',
                                          style: Styles.bold(size: 12))),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    isINV = true;
                                    isAbsent = false;
                                    isPresent = false;
                                  });
                                },
                                child: Container(
                                  height: MediaQuery.of(context).size.height *
                                      0.045,
                                  margin: EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  width:
                                      MediaQuery.of(context).size.width * 0.10,
                                  decoration: BoxDecoration(
                                      color: isINV == true
                                          ? Colors.grey
                                          : ColorConstants.WHITE,
                                      borderRadius: BorderRadius.circular(100)),
                                  child: Center(
                                      child: Text('INV',
                                          style: Styles.bold(size: 12))),
                                ),
                              )
                            ],
                          ),
                          Divider(thickness: 1)
                        ],
                      ),
                    ),
                  ),
                ],
              );
            })),
      ],
    );
  }
}
