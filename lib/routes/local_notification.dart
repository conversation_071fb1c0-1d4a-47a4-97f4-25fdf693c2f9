import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import 'notification_route.dart';

Map<String, dynamic>? currentPayload;

class NotificationService {
  final FlutterLocalNotificationsPlugin notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initNotification() async {
    AndroidInitializationSettings initializationSettingsAndroid =
        const AndroidInitializationSettings('@mipmap/ic_launcher_mec');

    var initializationSettingsIOS = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
        //onDidReceiveLocalNotification: (int id, String? title, String? body, String? payload) async {}
    );


    var initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid, iOS: initializationSettingsIOS);

    await notificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse notificationResponse) async {
       NotificationRoute().open(currentPayload!);
      // try {
      //   Provider.of<MenuListProvider>(context, listen: false)
      //       .updateCurrentIndex(currentPayload?['route']);
      //   Navigator.of(context).popUntil((route) => route.isFirst);
      // } catch (e) {
      //   print('exception on routing: $e');
      // }
    });
  }

  notificationDetails() {
    return const NotificationDetails(
        android: AndroidNotificationDetails('channelId', 'channelName',
            importance: Importance.max),
        iOS: DarwinNotificationDetails());
  }

  Future showNotification(
      {int id = 0,
      String? title,
      String? body,
      Map<String, dynamic>? payLoad}) async {
    currentPayload = payLoad;
    print("route is $currentPayload and $payLoad");
    return notificationsPlugin.show(
        id, title, body, await notificationDetails());
  }
}
