import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';
import 'package:masterg/data/providers/training_detail_provider.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/reels/reel_screen.dart';
import 'package:masterg/pages/singularis/wow_studio.dart';
import 'package:masterg/pages/training_pages/program_content/training_detail_page.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/Log.dart';
import 'package:provider/provider.dart';

import '../main.dart';
import '../pages/ghome/home_page.dart';
import '../pages/singularis/competition/competition_detail.dart';

class NotificationRoute {
  // static BuildContext? _context;
  static bool routing = false;
  NotificationRoute() {
    // _context = context;
  }

  // Define route constants
  static const String trainingPageRoute = '/training_page';
  static const String reelRoute = '/reels';
  static const String communityRoute = '/g-carvaan';
  static const String wowStudioRoute = '/wow-studio';
  static const String event = '/event';

  // Handle route
  void open(Map<String, dynamic> payload) {
    if (routing != false) return;
    payload.forEach((key, value) {
      Log.v("payload key $key : $value");
    });

    Log.v("payload end");
    String route = "";
    try {
      route = payload['route'];
    } catch (e, stackTrace) {
      Log.v("Exception: $stackTrace");
    }

    //set default page
    Widget? routeWidget;
    switch (route) {
      case trainingPageRoute:
        // Handle trainingPageRoute
        int programId = int.parse(payload['id']);

        routeWidget = ChangeNotifierProvider<TrainingDetailProvider>(
            create: (context) => TrainingDetailProvider(
                TrainingService(ApiService()), MProgram(id: programId)),
            child: TrainingDetailPage());

        break;
      case reelRoute:
        // Handle reelRoute
        routeWidget = ReelScreen();
        break;
      case wowStudioRoute:
        // Handle wowStudioRoute
        routeWidget = WowStudio();
        break;

      case event:
        int eventId = int.parse(payload['id']);
        routeWidget = CompetitionDetail(competitionId: eventId, isEvent: true);
        break;

      default:homePage();
    }
    //Navigate or page

    if (routing == false) {
      Navigator.pushAndRemoveUntil(
          Application.getContext()!,
          NextPageRoute(EasyLocalization(
            supportedLocales: [Locale('en'), Locale('ar'), Locale('hi')],
            path: 'assets/translations',
            fallbackLocale: Locale('en'),
            child: MyApp(
              intialRoute: routeWidget != null ? null : route,
              routeWidget: routeWidget,
            ),
          )),
          (route) => false);
    }
  }
}
