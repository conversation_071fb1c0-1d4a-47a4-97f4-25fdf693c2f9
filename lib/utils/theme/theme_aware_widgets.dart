import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

/// A theme-aware text field that automatically adapts to the current theme
class ThemeAwareTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? hintText;
  final String? labelText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType? keyboardType;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final bool enabled;
  final int? maxLines;
  final int? minLines;
  final bool autofocus;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final TextCapitalization textCapitalization;
  final EdgeInsetsGeometry? contentPadding;
  final bool readOnly;

  const ThemeAwareTextField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.onChanged,
    this.onTap,
    this.enabled = true,
    this.maxLines = 1,
    this.minLines,
    this.autofocus = false,
    this.focusNode,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.contentPadding,
    this.readOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return TextFormField(
          controller: controller,
          obscureText: obscureText,
          keyboardType: keyboardType,
          validator: validator,
          onChanged: onChanged,
          onTap: onTap,
          enabled: enabled,
          maxLines: maxLines,
          minLines: minLines,
          autofocus: autofocus,
          focusNode: focusNode,
          textInputAction: textInputAction,
          textCapitalization: textCapitalization,
          readOnly: readOnly,
          style: TextStyle(color: context.bodyTextColor),
          decoration: InputDecoration(
            hintText: hintText,
            labelText: labelText,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: context.inputFieldBackgroundColor,
            hintStyle: TextStyle(color: context.hintTextColor),
            labelStyle: TextStyle(color: context.bodyTextColor),
            contentPadding: contentPadding ??
                const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(color: context.inputFieldBorderColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(color: context.inputFieldBorderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                  color: context.inputFieldFocusedBorderColor, width: 2.0),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(color: context.disabledColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(color: context.errorColor),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(color: context.errorColor, width: 2.0),
            ),
          ),
        );
      },
    );
  }
}

/// A theme-aware button that automatically adapts to the current theme
class ThemeAwareButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonType type;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final double borderRadius;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const ThemeAwareButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.type = ButtonType.elevated,
    this.width,
    this.height,
    this.padding,
    this.borderRadius = 8.0,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        Widget button;

        switch (type) {
          case ButtonType.elevated:
            button = ElevatedButton(
              onPressed: onPressed,
              style: ThemeHelper.getElevatedButtonStyle(
                context,
                backgroundColor: backgroundColor,
                foregroundColor: foregroundColor,
                borderRadius: borderRadius,
              ),
              child: child,
            );
            break;
          case ButtonType.outlined:
            button = OutlinedButton(
              onPressed: onPressed,
              style: ThemeHelper.getOutlinedButtonStyle(
                context,
                borderColor: backgroundColor ?? context.primaryDark,
                foregroundColor: foregroundColor,
                borderRadius: borderRadius,
              ),
              child: child,
            );
            break;
          case ButtonType.text:
            button = TextButton(
              onPressed: onPressed,
              style: ThemeHelper.getTextButtonStyle(
                context,
                foregroundColor: foregroundColor,
              ),
              child: child,
            );
            break;
        }

        if (width != null || height != null || padding != null) {
          return Container(
            width: width,
            height: height,
            padding: padding,
            child: button,
          );
        }

        return button;
      },
    );
  }
}

/// Button types for ThemeAwareButton
enum ButtonType {
  elevated,
  outlined,
  text,
}

/// A theme-aware badge that automatically adapts to the current theme
class ThemeAwareBadge extends StatelessWidget {
  final Widget child;
  final String? label;
  final Color? backgroundColor;
  final Color? textColor;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final BadgeType type;

  const ThemeAwareBadge({
    super.key,
    required this.child,
    this.label,
    this.backgroundColor,
    this.textColor,
    this.borderRadius = 12.0,
    this.padding,
    this.type = BadgeType.primary,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        Color badgeColor;
        Color badgeTextColor;

        switch (type) {
          case BadgeType.primary:
            badgeColor = backgroundColor ?? context.primaryDark;
            badgeTextColor = textColor ?? context.primaryForegroundColor;
            break;
          case BadgeType.success:
            badgeColor = backgroundColor ?? context.successColor;
            badgeTextColor = textColor ?? Colors.white;
            break;
          case BadgeType.error:
            badgeColor = backgroundColor ?? context.errorColor;
            badgeTextColor = textColor ?? Colors.white;
            break;
          case BadgeType.warning:
            badgeColor = backgroundColor ?? context.warningColor;
            badgeTextColor = textColor ?? Colors.white;
            break;
          case BadgeType.neutral:
            badgeColor = backgroundColor ?? context.badgeBackgroundColor;
            badgeTextColor = textColor ?? context.bodyTextColor;
            break;
        }

        return Badge(
          backgroundColor: badgeColor,
          label: label != null
              ? Text(
                  label!,
                  style: TextStyle(
                    color: badgeTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                )
              : null,
          child: child,
        );
      },
    );
  }
}

/// Badge types for ThemeAwareBadge
enum BadgeType {
  primary,
  success,
  error,
  warning,
  neutral,
}

/// A theme-aware status indicator that automatically adapts to the current theme
class ThemeAwareStatusIndicator extends StatelessWidget {
  final StatusType status;
  final String? text;
  final double size;
  final bool showText;

  const ThemeAwareStatusIndicator({
    super.key,
    required this.status,
    this.text,
    this.size = 12.0,
    this.showText = true,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        Color statusColor;
        String statusText;

        switch (status) {
          case StatusType.online:
            statusColor = context.successColor;
            statusText = text ?? 'Online';
            break;
          case StatusType.offline:
            statusColor = context.disabledColor;
            statusText = text ?? 'Offline';
            break;
          case StatusType.busy:
            statusColor = context.errorColor;
            statusText = text ?? 'Busy';
            break;
          case StatusType.away:
            statusColor = context.warningColor;
            statusText = text ?? 'Away';
            break;
          case StatusType.pending:
            statusColor = context.warningColor;
            statusText = text ?? 'Pending';
            break;
          case StatusType.completed:
            statusColor = context.successColor;
            statusText = text ?? 'Completed';
            break;
          case StatusType.failed:
            statusColor = context.errorColor;
            statusText = text ?? 'Failed';
            break;
        }

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
            ),
            if (showText && text != null) ...[
              const SizedBox(width: 8),
              Text(
                statusText,
                style: TextStyle(
                  color: context.bodyTextColor,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}

/// Status types for ThemeAwareStatusIndicator
enum StatusType {
  online,
  offline,
  busy,
  away,
  pending,
  completed,
  failed,
}
