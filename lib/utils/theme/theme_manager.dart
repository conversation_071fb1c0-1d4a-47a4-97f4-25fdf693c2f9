import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/theme/theme_bloc.dart';
import 'theme_extensions.dart';

/// Comprehensive theme management utility
/// This class provides methods to ensure consistent theme application across the app
class ThemeManager {
  /// Get the current theme state
  static ThemeState getCurrentTheme(BuildContext context) {
    return context.read<ThemeBloc>().state;
  }

  /// Check if the current theme is dark mode
  static bool isDarkMode(BuildContext context) {
    return getCurrentTheme(context).isDarkMode;
  }

  /// Toggle the theme between light and dark
  static void toggleTheme(BuildContext context) {
    context.read<ThemeBloc>().add(ToggleThemeEvent());
  }

  /// Set the theme to light mode
  static void setLightTheme(BuildContext context) {
    context.read<ThemeBloc>().add(SetLightThemeEvent());
  }

  /// Set the theme to dark mode
  static void setDarkTheme(BuildContext context) {
    context.read<ThemeBloc>().add(SetDarkThemeEvent());
  }

  /// Load the saved theme from preferences
  static void loadSavedTheme(BuildContext context) {
    context.read<ThemeBloc>().add(LoadSavedThemeEvent());
  }

  /// Get theme-aware colors for any context
  static AppColors getColors(BuildContext context) {
    return context.appColors;
  }

  /// Get theme-aware text styles
  static TextStyle getTextStyle(
    BuildContext context, {
    required TextStyleType type,
    Color? color,
    FontWeight? fontWeight,
    double? fontSize,
  }) {
    return ThemeHelper.getTextStyle(
      context,
      type: type,
      color: color,
      fontWeight: fontWeight,
      fontSize: fontSize,
    );
  }

  /// Get theme-aware card decoration
  static BoxDecoration getCardDecoration(
    BuildContext context, {
    double borderRadius = 8.0,
    bool withShadow = true,
  }) {
    return ThemeHelper.getCardDecoration(
      context,
      borderRadius: borderRadius,
      withShadow: withShadow,
    );
  }

  /// Get theme-aware gradient decoration
  static BoxDecoration getGradientDecoration(
    BuildContext context, {
    double borderRadius = 8.0,
    List<Color>? colors,
  }) {
    return ThemeHelper.getGradientDecoration(
      context,
      borderRadius: borderRadius,
      colors: colors,
    );
  }

  /// Wrap a widget to make it theme-aware
  static Widget wrapWithThemeBuilder({
    required Widget child,
    required BuildContext context,
  }) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) => child,
    );
  }

  /// Create a theme-aware color based on the current theme
  static Color getThemeAwareColor(
    BuildContext context, {
    required Color lightColor,
    required Color darkColor,
  }) {
    return isDarkMode(context) ? darkColor : lightColor;
  }

  /// Get system status bar style based on current theme
  static SystemUiOverlayStyle getSystemUiOverlayStyle(BuildContext context) {
    return isDarkMode(context)
        ? SystemUiOverlayStyle.light
        : SystemUiOverlayStyle.dark;
  }

  /// Apply theme to the entire app
  static ThemeData getAppTheme(BuildContext context) {
    final themeState = getCurrentTheme(context);
    return themeState.themeData;
  }

  /// Validate that a widget is properly using theme colors
  /// This is a development helper to identify hardcoded colors
  static void validateThemeUsage(BuildContext context, Widget widget) {
    // This would be used in development to scan for hardcoded colors
    // Implementation would involve widget tree traversal
    if (kDebugMode) {
      debugPrint('Theme validation: Checking widget for proper theme usage');
    }
  }

  /// Get theme-aware elevation for cards and surfaces
  static double getElevation(BuildContext context,
      {double defaultElevation = 2.0}) {
    return isDarkMode(context) ? defaultElevation + 1 : defaultElevation;
  }

  /// Get theme-aware border radius
  static BorderRadius getBorderRadius({double radius = 8.0}) {
    return BorderRadius.circular(radius);
  }

  /// Get theme-aware shadow
  static List<BoxShadow> getBoxShadow(BuildContext context,
      {double blurRadius = 4.0}) {
    return [
      BoxShadow(
        color: context.appColors.shadow,
        offset: const Offset(0, 2),
        blurRadius: blurRadius,
      ),
    ];
  }

  /// Create a theme-aware gradient
  static LinearGradient getLinearGradient(
    BuildContext context, {
    List<Color>? colors,
    AlignmentGeometry begin = Alignment.centerLeft,
    AlignmentGeometry end = Alignment.centerRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors ?? context.gradientColors,
    );
  }

  /// Get theme-aware input decoration
  static InputDecoration getInputDecoration(
    BuildContext context, {
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool filled = true,
  }) {
    return InputDecoration(
      hintText: hintText,
      labelText: labelText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: filled,
      fillColor: context.appColors.textFieldBackground,
      hintStyle: TextStyle(color: context.hintTextColor),
      labelStyle: TextStyle(color: context.bodyTextColor),
      border: OutlineInputBorder(
        borderRadius: getBorderRadius(),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: getBorderRadius(),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: getBorderRadius(),
        borderSide: BorderSide(color: context.primaryDark),
      ),
    );
  }

  /// Get theme-aware button style
  static ButtonStyle getElevatedButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: context.primaryDark,
      foregroundColor: context.primaryForegroundColor,
      elevation: getElevation(context),
      shape: RoundedRectangleBorder(borderRadius: getBorderRadius()),
    );
  }

  /// Get theme-aware text button style
  static ButtonStyle getTextButtonStyle(BuildContext context) {
    return TextButton.styleFrom(
      foregroundColor: context.primaryDark,
    );
  }

  /// Get theme-aware outlined button style
  static ButtonStyle getOutlinedButtonStyle(BuildContext context) {
    return OutlinedButton.styleFrom(
      foregroundColor: context.primaryDark,
      side: BorderSide(color: context.primaryDark),
    );
  }

  /// Get theme-aware divider
  static Widget getDivider(BuildContext context,
      {double? height, double? thickness}) {
    return Divider(
      color: context.dividerColor,
      height: height,
      thickness: thickness ?? 1,
    );
  }

  /// Get theme-aware list tile theme
  static ListTileThemeData getListTileTheme(BuildContext context) {
    return ListTileThemeData(
      textColor: context.bodyTextColor,
      iconColor: context.iconColor,
      tileColor: context.surfaceColor,
    );
  }

  /// Get theme-aware bottom navigation bar theme
  static BottomNavigationBarThemeData getBottomNavigationBarTheme(
      BuildContext context) {
    return BottomNavigationBarThemeData(
      backgroundColor: context.appColors.bottomNavBackground,
      selectedItemColor: context.appColors.bottomNavSelected,
      unselectedItemColor: context.appColors.bottomNavUnselected,
      type: BottomNavigationBarType.fixed,
    );
  }

  /// Get theme-aware tab bar theme
  static TabBarTheme getTabBarTheme(BuildContext context) {
    return TabBarTheme(
      labelColor: context.primaryDark,
      unselectedLabelColor: context.bodyTextColor,
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(color: context.primaryDark, width: 2),
      ),
    );
  }

  /// Get theme-aware switch theme
  static SwitchThemeData getSwitchTheme(BuildContext context) {
    return SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return context.primaryDark;
        }
        return Colors.grey;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return context.primaryDark.withValues(alpha: 0.5);
        }
        return Colors.grey.withValues(alpha: 0.3);
      }),
    );
  }
}

/// Extension to add theme management methods to BuildContext
extension ThemeManagerExtension on BuildContext {
  /// Quick access to theme manager methods
  ThemeManager get themeManager => ThemeManager();

  /// Quick toggle theme
  void toggleTheme() => ThemeManager.toggleTheme(this);

  /// Quick check if dark mode
  bool get isCurrentlyDarkMode => ThemeManager.isDarkMode(this);

  /// Quick access to theme colors
  AppColors get themeColors => ThemeManager.getColors(this);
}
