import 'package:hive/hive.dart';
import 'package:masterg/utils/offline_data/url_model.dart';

class URLModelAdapter extends TypeAdapter<URLModel> {
  @override
  final int typeId = 0;

  @override
  URLModel read(BinaryReader reader) {
    return URLModel(
        reader.readString(),
        reader.readString(),
        reader.readString(), 
        reader.readString(),
      reader.readInt()
    );
  }

  @override
  void write(BinaryWriter writer, URLModel obj) {
    writer.writeString(obj.url);
    writer.writeString(obj.thumbnail);
    writer.writeString(obj.view);
    writer.writeString(obj.des);
    writer.writeInt(obj.postid);
  }
}