import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class TakePictureScreen extends StatefulWidget {
  const TakePictureScreen({
    super.key,
    required this.camera,
    required this.cameras,
    this.frontCamera = false,
    this.checkPermission = false,
  });

  final CameraDescription camera;
  final List<CameraDescription> cameras;
  final bool frontCamera;
  final checkPermission;

  @override
  TakePictureScreenState createState() => TakePictureScreenState();
}

class TakePictureScreenState extends State<TakePictureScreen>
    with WidgetsBindingObserver {
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;
  bool frontEnable = false;

  @override
  void initState() {
    super.initState();
    frontEnable = widget.frontCamera;
    _controller = CameraController(
      widget.cameras[widget.frontCamera == true ? 1 : 0],
      ResolutionPreset.high,
    );

    _initializeControllerFuture = _controller.initialize();

    checkCameraPermission();
    WidgetsBinding.instance.addObserver(this);
  }

  Future<bool> checkCameraPermission() async {
    if(!widget.checkPermission) return false;
    try {
      await _initializeControllerFuture;
      return false;
    } on CameraException catch (e) {
      print('exception is ${e.description}');

      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text("permission_required").tr(),
            content: Text("grant_permision_camera_microphone").tr(),
            actions: [
              TextButton(
                child: Text("cancel").tr(),
                onPressed: () {
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
              ),
              TextButton(
                child: Text("open_setting").tr(),
                onPressed: () {
                  openAppSettings();
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
              ),
            ],
          );
        },
      );
      return true;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void switchCamera() {
    setState(() {
      _controller = CameraController(
          frontEnable == true ? widget.cameras[0] : widget.cameras[1],
          ResolutionPreset.high);
      frontEnable = !frontEnable;
    });
    _initializeControllerFuture = _controller.initialize();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // App is in the foreground
      print('App is in the foreground');
      checkCameraPermission();
    } else if (state == AppLifecycleState.paused) {
      // App is in the background
      print('App is in the background');
    }
    final CameraController? cameraController = _controller;

    if (cameraController == null || !cameraController.value.isInitialized) {
      return;
    }
    if (state == AppLifecycleState.inactive) {
      cameraController.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('take_picture').tr(),
        actions: [
          IconButton(
              onPressed: () {
                switchCamera();
              },
              icon: Icon(
                Icons.flip_camera_android,
              ))
        ],
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        child: FutureBuilder<void>(
          future: _initializeControllerFuture,
          builder: (context, snapshot) {
            // return Center(child: Text('${snapshot.connectionState} and ${_controller.description}'),);
            if (snapshot.connectionState == ConnectionState.done) {
              return CameraPreview(_controller);
            } else {
              return const Center(child: CircularProgressIndicator());
            }
          },
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          try {
            await _initializeControllerFuture;

            final image = await _controller.takePicture();

            if (!mounted) return;

            Navigator.pop(context, image.path);
          } on CameraException catch (e) {
            print('exception is ${e.description}');
          }
        },
        child: const Icon(Icons.camera_alt),
      ),
    );
  }
}
