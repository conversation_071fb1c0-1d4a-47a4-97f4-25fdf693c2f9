import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:masterg/data/api/api_response.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/models/response/home_response/get_content_resp.dart';
import 'package:masterg/data/providers/home_provider.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/routes/notification_route.dart';
import 'package:masterg/utils/Log.dart';

import '../routes/local_notification.dart';

class NotificationHelper {
  static NotificationHelper? notificationHelper;
  static FirebaseMessaging? firebaseMessaging;
  static late BuildContext buildContext;
  static bool notificationsEnabled = false;
  FlutterLocalNotificationsPlugin? flutterLocalNotificationsPlugin;
  static bool localNotificationShowing = false;

  static getInstance(BuildContext context) {
    if (notificationHelper == null) {
      firebaseMessaging = FirebaseMessaging.instance;
      notificationHelper = new NotificationHelper._();
    }
    buildContext = context;
    return notificationHelper;
  }

  NotificationHelper._();

  void getFcmToken() async {
    var token = await FirebaseMessaging.instance.getToken();
    Log.v("Firebase Token : $token");

    print("Firebase Token ----- : $token");
    UserSession.firebaseToken = token;
    Preference.setString(Preference.FIREBASE_TOKEN, token!);
  }

  void setFcm() {
    FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    FirebaseMessaging.instance.requestPermission();
    FirebaseMessaging.onMessage.listen((message) {
      Log.v('onMessage called --> ${message.notification!.title}');
      NotificationService()
                  .showNotification(title: '${message.notification?.title}', body: '${message.notification?.body}', payLoad: message.data);
      // if (Platform.isAndroid && localNotificationShowing == false) _showNotification(message);
      // try {
      //   // BuildContext context = Application.getContext()!;
      //   NotificationRoute().open(message.data);
      // } catch (e) {
      //   print('exception in route $e');
      // }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      Log.v('onMessageOpened called --> $message');
      notificationClickAction(message.data);
    });

  }

  Future<void> _isAndroidPermissionGranted() async {
    if (Platform.isAndroid) {
      final bool granted = await flutterLocalNotificationsPlugin
              ?.resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin>()
              ?.areNotificationsEnabled() ??
          false;
      notificationsEnabled = granted;
    }
  }

  static Future<dynamic> myBackgroundMessageHandler(
      Map<String, dynamic> message) async {
    return Future<void>.value();
  }

  Future<void> _showNotification(RemoteMessage message) async {
    Log.v("notification check ");
    try{
      const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails('com.singulariswow', 'singulariswow',
            channelDescription: 'your channel description',
            importance: Importance.max,
            priority: Priority.high,
            ticker: 'ticker');
    const NotificationDetails notificationDetails =
        NotificationDetails(android: androidNotificationDetails);
    final json = jsonEncode(message.data);
    var id = DateTime.now().millisecondsSinceEpoch.toString();
 localNotificationShowing = true;

    await flutterLocalNotificationsPlugin?.show(
        int.parse(id.substring(id.length - 6)), // notification id
        message.notification?.title,
        message.notification?.body,
        notificationDetails,
        payload: json);
 localNotificationShowing = false;

    }
    catch(e){
      Log.v("exception is $e");
    }
  }

  notificationClickAction(Map<String, dynamic> message) {
    try {
      // BuildContext context = Application.getContext()!;
      NotificationRoute().open(message);
    } catch (e) {
      print('exception in route $e');
    } 
  }

  Future<void> _onSelectNotification(String json) async {
    final data = jsonDecode(json);
    _handleNotification(data);
  }

  void _handleNotification(Map<String, dynamic> json) async {
    Map<String, dynamic> payload = json;
    ApiResponse? res =
        await HomeProvider(api: ApiService()).getContentDetails(id: int.parse(payload['id']));
    if (res!.success) {
      var context = Application.getContext();
      if (payload['content_type'] == 'content') {
        GetContentResp resp = GetContentResp.fromJson(res.body);
        ListData data = resp.data!.list!.first;
        if (resp.data!.list!.first.categoryId == 8) {}
        if (resp.data?.list?.first.categoryId == 9) {}
        if (resp.data?.list?.first.categoryId == 10) {}
      }
      if (payload['content_type'] == 'meeting') {}
      if (payload['content_type'] == 'training') {}
    }
  }
}
