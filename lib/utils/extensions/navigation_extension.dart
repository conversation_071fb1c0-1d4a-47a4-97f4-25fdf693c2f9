import 'package:flutter/material.dart';

extension NavigationExtension on BuildContext {
  /// Push to new screen
  Future<T?> pushTo<T>(Widget page) {
    return Navigator.push<T>(
      this,
      MaterialPageRoute(builder: (context) => page),
    );
  }

  /// Push and replace current screen
  Future<T?> pushReplacementTo<T>(Widget page) {
    return Navigator.pushReplacement(
      this,
      MaterialPageRoute(builder: (context) => page),
    );
  }

  /// Push and remove all previous screens
  Future<T?> pushAndRemoveUntil<T>(Widget page) {
    return Navigator.pushAndRemoveUntil(
      this,
      MaterialPageRoute(builder: (context) => page),
      (route) => false,
    );
  }

  /// Pop current screen
  void pop<T>([T? result]) {
    Navigator.pop(this, result);
  }

  /// Pop until specific route name
  void popUntil(String routeName) {
    Navigator.popUntil(this, ModalRoute.withName(routeName));
  }

  /// Check if can pop
  bool canPop() {
    return Navigator.canPop(this);
  }

  /// Pop multiple screens
  void popTimes(int times) {
    int count = 0;
    Navigator.popUntil(this, (route) {
      return count++ == times;
    });
  }

  /// Push named route
  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) {
    return Navigator.pushNamed<T>(this, routeName, arguments: arguments);
  }

  /// Push named route and replace current screen
  Future<T?> pushReplacementNamed<T>(String routeName, {Object? arguments}) {
    return Navigator.pushReplacementNamed<T, dynamic>(
      this,
      routeName,
      arguments: arguments,
    );
  }

  /// Push named route and remove all previous screens
  Future<T?> pushNamedAndRemoveUntil<T>(String routeName, {Object? arguments}) {
    return Navigator.pushNamedAndRemoveUntil(
      this,
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }
}
