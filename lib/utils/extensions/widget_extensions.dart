import 'package:flutter/material.dart';

extension WidgetExtension on Widget {
  /// Wraps widget with Expanded
  Widget expanded({int flex = 1}) => Expanded(
        flex: flex,
        child: this,
      );

  /// Wraps widget with Flexible
  Widget flexible({int flex = 1, FlexFit fit = FlexFit.loose}) => Flexible(
        flex: flex,
        fit: fit,
        child: this,
      );

  /// Wraps widget with Center
  Widget get centered => Center(child: this);

  /// Wraps widget with Padding
  Widget padding({EdgeInsetsGeometry padding = const EdgeInsets.all(8.0)}) =>
      Padding(
        padding: padding,
        child: this,
      );

  /// Wraps widget with <PERSON><PERSON><PERSON><PERSON> for width
  Widget width(double width) => SizedBox(width: width, child: this);

  /// Wraps widget with <PERSON><PERSON><PERSON>ox for height
  Widget height(double height) => SizedBox(height: height, child: this);

  /// Wraps widget with Container
  Widget container({
    Color? color,
    BoxDecoration? decoration,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
  }) =>
      Container(
        color: color,
        decoration: decoration,
        padding: padding,
        margin: margin,
        width: width,
        height: height,
        child: this,
      );

  /// Wraps widget with ClipRRect for rounded corners
  Widget roundedCorners([double radius = 8.0]) => ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: this,
      );

  /// Wraps widget with Visibility
  Widget visible(bool visible) => Visibility(
        visible: visible,
        child: this,
      );
}
