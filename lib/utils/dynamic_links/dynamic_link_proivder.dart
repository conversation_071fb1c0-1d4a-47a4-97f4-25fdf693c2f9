import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:masterg/utils/Log.dart';
import 'package:share_plus/share_plus.dart';

class DynamicLinkProvider {
  ///create dynamic link
  Future<String> createLink(String refCode, String fallBackUrl) async {
    print('create link');
    try {
      final String url = "https://singulariswow.page.link/ref=$refCode";
      final DynamicLinkParameters parameters = DynamicLinkParameters(
          androidParameters: AndroidParameters(
              fallbackUrl: Uri.parse(fallBackUrl),
              packageName: "com.singulariswow",
              minimumVersion: 0),
          iosParameters: IOSParameters(
              fallbackUrl: Uri.parse(fallBackUrl),
              bundleId: "com.singulariswow",
              minimumVersion: "0"),
          link: Uri.parse(url),
          uriPrefix: "https://singulariswow.page.link");
      final FirebaseDynamicLinks link = FirebaseDynamicLinks.instance;
      // await link.buildLink(parameters)
      final refLink = await link.buildShortLink(parameters);
      return refLink.shortUrl.toString();
    } catch (e) {
      print('somethign wen $e');
    }
    return "eh;l";
  }

  void initDynamicLinks() async {
    final instanceLink = await FirebaseDynamicLinks.instance.getInitialLink();
    if (instanceLink != null) {
      final Uri refLink = instanceLink.link;
      Map<String, String> queryParameters = refLink.queryParameters;
      Log.v('the resutl is ${queryParameters['ref']}');
      // Get.to(NewPortfolioPage());
      await Future.delayed(Duration(seconds: 5));
      Share.share('share url is ${queryParameters['ref']}');
      FirebaseAnalytics.instance.logEvent(name: 'share_url', parameters: {
        "type": "dynamic_link_generate",
      });
    }
  }
}
