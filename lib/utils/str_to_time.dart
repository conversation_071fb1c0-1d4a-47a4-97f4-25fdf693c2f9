import 'package:flutter/material.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'dart:ui' as ui;

import 'package:shimmer/shimmer.dart';

class StrToTime extends StatefulWidget {
  final String time;
  final String dateFormat;
  final String? appendString;
  final TextStyle? textStyle;
  const StrToTime(
      {Key? key,
      required this.time,
      required this.dateFormat,
      required this.appendString,
      this.textStyle})
      : super(key: key);

  @override
  State<StrToTime> createState() => _StrToTimeState();
}

class _StrToTimeState extends State<StrToTime> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<int>(
      future: Utility.strtotime(widget.time),
      builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
        switch (snapshot.connectionState) {
          case ConnectionState.waiting:
            return Shimmer.fromColors(
              baseColor: Color(0xffe6e4e6),
              highlightColor: Color(0xffeaf0f3),
              child: Container(
                  height: 14,
                  // margin: EdgeInsets.only(left: 2),
                  width: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                  )),
            );
          default:
            if (snapshot.hasError)
              return Text('');
            else
              // return Text('${snapshot}');
              return Text(
                '${Utility.getTime(dateFormat: widget.dateFormat, timestamp: snapshot.data! * 1000)}${widget.appendString ?? ''}',
                style:
                    Styles.regular(color: ColorConstants.BODY_TEXT, size: 12),
                textDirection: ui.TextDirection.ltr,
              );
        }
      },
    );
  }
}

class DateToTimeago extends StatefulWidget {
  final String time;
  const DateToTimeago({Key? key, required this.time}) : super(key: key);

  @override
  State<DateToTimeago> createState() => _DateToTimeagoState();
}

class _DateToTimeagoState extends State<DateToTimeago> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<int>(
      future: Utility.strtotime(widget.time),
      builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
        switch (snapshot.connectionState) {
          case ConnectionState.waiting:
            return Text('');
          default:
            if (snapshot.hasError)
              return Text('');
            else {
              DateTime date = DateTime.fromMillisecondsSinceEpoch(
                snapshot.data! * 1000,
              );
              var now = DateTime.now();

              return Text(
                  '${Utility().calculateTimeDifferenceBetween(DateTime.parse(date.toString().substring(0, 19)), now, context)}');
            }
        }
      },
    );
  }
}
