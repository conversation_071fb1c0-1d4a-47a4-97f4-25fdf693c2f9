import 'dart:developer';

import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;
  final bool maintainAspectRatio;
  final bool autoMute;
  final bool withControllers;
  final Function(VideoPlayerController? controller)? handleController;

  VideoPlayerWidget(
      {Key? key,
      required this.videoUrl,
      this.maintainAspectRatio = false,
      this.autoMute = true,
      this.handleController,
      this.withControllers = false})
      : super(key: key);

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget>
    with AutomaticKeepAliveClientMixin {
  VideoPlayerController? _controller;
  FlickManager? _flickManager;
  bool _isPlaying = false;
  bool _showIcon = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl))
      ..addListener(() {
        setState(() {});
      })
      ..initialize().then((_) {
        setState(() {});
      });
    _controller?.play();
    _flickManager = FlickManager(
        videoPlayerController: _controller!,
        autoPlay: false,
        autoInitialize: false);
    if (widget.autoMute) _controller?.setVolume(0.0);
  }

  @override
  void dispose() {
    log("init is called dispose");
    _controller?.dispose();
    _flickManager?.dispose();
    log("play vidoe url disspose");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          if (notification is ScrollEndNotification) {
            final RenderObject? renderObject = context.findRenderObject();
            final RenderAbstractViewport? viewport =
                RenderAbstractViewport.of(renderObject);
            final double offset =
                viewport!.getOffsetToReveal(renderObject!, 0.0).offset;

            if (offset < 0.0) {
              _controller?.pause();
              _isPlaying = false;
              _showIcon = true;
              Future.delayed(Duration(seconds: 2), () {
                setState(() {
                  _showIcon = false;
                });
              });
            } else {
              _controller?.play();

              _isPlaying = true;
              _showIcon = true;
              Future.delayed(Duration(seconds: 2), () {
                setState(() {
                  _showIcon = false;
                });
              });
            }
          }
          return true;
        },
        child: GestureDetector(
          onTap: () {
            setState(() {
              _isPlaying = !_isPlaying;
              _showIcon = true;
              Future.delayed(Duration(seconds: 2), () {
                setState(() {
                  _showIcon = false;
                });
              });
              if (_isPlaying) {
                _controller?.play();
              } else {
                _controller?.pause();
              }
            });
          },
          child: VisibilityDetector(
              key: ObjectKey(_controller),
              onVisibilityChanged: (visibility) {
                if (widget.withControllers == true) {
                  return;
                }
                if (visibility.visibleFraction == 0 && this.mounted) {
                  _controller?.pause();
                } else {
                  _controller?.play();
                  if (widget.handleController != null)
                    widget.handleController!(_controller);
                }
              },
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (_controller != null)
                    widget.maintainAspectRatio
                        ? AspectRatio(
                            aspectRatio: _controller!.value.aspectRatio,
                            child: ClipRRect(
                                borderRadius: BorderRadius.circular(0),
                                child: widget.withControllers
                                    ? FlickVideoPlayer(
                                        flickManager: _flickManager!)
                                    : VideoPlayer(_controller!)),
                          )
                        : SizedBox(
                            height: 300,
                            child: AspectRatio(
                              aspectRatio:
                                  MediaQuery.of(context).size.width / 300,
                              child: ClipRRect(
                                  borderRadius: BorderRadius.circular(0),
                                  child: widget.withControllers
                                      ? FlickVideoPlayer(
                                          flickManager: _flickManager!)
                                      : VideoPlayer(_controller!)),
                            ),
                          ),
                  Align(
                    alignment: Alignment.center,
                    child: _showIcon
                        ? Icon(
                            _isPlaying ? Icons.pause : Icons.play_arrow,
                            size: 50.0,
                            color: Colors.white,
                          )
                        : SizedBox(),
                  ),
                  if (!widget.withControllers)
                    Positioned(
                        right: 8,
                        bottom: 6,
                        child: IconButton(
                          onPressed: () {
                            _controller?.value.volume == 0.0
                                ? _controller?.setVolume(1.0)
                                : _controller?.setVolume(0.0);
                            setState(() {});
                          },
                          icon: Icon(
                            _controller?.value.volume == 1.0
                                ? Icons.volume_up
                                : Icons.volume_off_outlined,
                            size: 35.0,
                            //color: Color.fromARGB(255, 28, 21, 21),
                            color: Colors.white,
                          ),
                        )),
                ],
              )),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
