import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:masterg/utils/resource/colors.dart';

class NetworkController extends GetxController {
  //final InternetConnectionChecker _connectivity = InternetConnectionChecker();
  final _connectivity = InternetConnectionChecker.instance; //Add new 13 Aug 2025
  @override
  void onInit() {
    super.onInit();

    _connectivity.onStatusChange.listen((status) {
      switch (status) {
        case InternetConnectionStatus.connected:
          log('InternetCheck: Data connection is available.');
          if (Get.isSnackbarOpen) {
            Get.closeCurrentSnackbar();
          }
          break;
        case InternetConnectionStatus.disconnected:
          log('InternetCheck: You are disconnected from the internet.');
          if (!Get.isSnackbarOpen)
            Get.rawSnackbar(
                messageText: const Text('please_connect_internet',
                        style: TextStyle(color: Colors.white, fontSize: 14))
                    .tr(),
                isDismissible: false,
                duration: const Duration(days: 1),
                backgroundColor: ColorConstants().gradientRight(),
                icon: const Icon(
                  Icons.wifi_off,
                  color: Colors.white,
                  size: 35,
                ),
                margin: EdgeInsets.zero,
                snackStyle: SnackStyle.GROUNDED);
          break;
        case InternetConnectionStatus.slow:
          // TODO: Handle this case.
          throw UnimplementedError();
      }
    });
  }
}
