const APK_DETAILS = {
  "full_screen_preboarding": "1",
  "gradient_icon": "0",
  "app_name": "MECFuture",
  "package_name": "com.singulariswow.mec",
  "appId": "6458731018",
  "logo_url": "singularis_logo_appbar.png",
  "theme_image_url": "mec_login_logo.svg",
  "theme_image_url2": "common_theme_2.png",
  "theme_color": "3CA4D2",
  "gradient_right": "3CA4D2",
  "gradient_left": "3CA4D2",
  "theme_color_gradient": "3CA4D2",
  "theme_forground_color": "FFFFFF",
  "element_color": "FFFFFF",
  "nlms_api_key": "0612b32b39f4b29f48c5c5363028ee916bb99Mec",
  "domain_url": "https://mecfuture.mec.edu.om/",
  //"domain_url": "http://mecstage.singulariswow.com/",
  "enable_boarding_screen": "1",
  "splash_image": "mec_splash.svg",
  "isBrandEnabled": "1",
  "faqEnabled": "0",
  "college_modules": "1", //yes
  "login_by_pass": "1",
  "register_in_app": "1", //no
  "login_toggle": "0",
  "register_now": "1",
  "offline_video_download": "1",
  "offline_pdf_download_learn": "1",
  "set_goal": "1",
  "bot_floating": "0",
  "set_goal_dashboard": "0",
  "set_goal_skill_data_dashboard": "0",
  "bottom_sheet_login": "true",
  "qr_code_enable": "1",
  "help_emable": "1",
  "notification_enable": "1",
  "forgot_cange_pass": "https://www.itservices.mec.edu.om/password",
  "policy_url": "https://mecfuture.mec.edu.om/policy",
  "about_url": "https://mec.edu.om/en/About-MEC",
  "preboarding1": "assets/images/preboarding/mec_preboarding1.png",
  "preboarding2": "assets/images/preboarding/mec_preboarding2.png",
  "preboarding3": "assets/images/preboarding/mec_preboarding3.png",
  "preboarding_web1": "assets/images/preboarding/mec_web_preboarding1.png",
  "preboarding_web2": "assets/images/preboarding/mec_web_preboarding2.png",
  "preboarding_web3": "assets/images/preboarding/mec_web_preboarding3.png",
  "preboarding_title1": "mec_preboarding_title1",
  "preboarding_title2": "mec_preboarding_title2",
  "preboarding_title3": "mec_preboarding_title3",
  "preboarding_desc1": "mec_preboarding_desc1",
  "preboarding_desc2": "mec_preboarding_desc2",
  "preboarding_desc3": "mec_preboarding_desc3",
  "time_table_url": "https://mecfuture.mec.edu.om/time-table-webview?user_id=",
  "course_plan":
  "https://mecfuture.mec.edu.om/course-plan-webview?user_id=",
  "current_sem":
  "https://mecfuture.mec.edu.om/current-session-webview?user_id=",
  "module_registration":
  "https://mecfuture.mec.edu.om/module-registration-web?user_id=",
  "pay_fees_online": "https://www.tasdeed.om/EFportal/SignIn",
  "my_profile_url":
  "https://mecfuture.mec.edu.om/sis-myprofile-webview?user_id=",
  "sis_fee_scholarship_url":
  "https://mecfuture.mec.edu.om/sis-fee-scholarship-webview?user_id=",
  "help_mec_future": "https://mecfuture.mec.edu.om/help-mecfuture?user_id=",
  "my_event_participation":
  "https://sis.mec.edu.om/M/EventCertificateM.aspx?t=",
  "wow-dashboard": "https://mecfuture.mec.edu.om/wow-dashboard/?user_id=",
  "faculty_events_console":
  "https://mecfuture.mec.edu.om/faculty-events-webview?user_id=",

  "event_admin_console":
  "https://mecfuture.mec.edu.om/manager-events?is_webview=1&user_id=",
  "job_opportunity":
  "https://mecfuture.mec.edu.om/job-opportunity-dashboard?is_webview=1&user_id=",
  "portfolios":
  "https://mecfuture.mec.edu.om/list-portfolio?is_webview=1&user_id=",
  "company": "https://mecfuture.mec.edu.om/company?is_webview=1&user_id=",
  "emploability_coordinators":
  "https://mecfuture.mec.edu.om/p_coordinators?is_webview=1&user_id=",

  "carrier_insights":
  "https://mecfuture.mec.edu.om/career_insight?is_webview=1&user_id=",

  "module_waiting_list":
  "https://mecfuture.mec.edu.om/module-waiting-list-web?user_id==",
  "prerequisite_waiver":
  "https://mecfuture.mec.edu.om//prerequisite-waiver-web?user_id=",
  "personality": "https://mecfuture.mec.edu.om/personality_assessment_list?user_id="

};
