import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as Math;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:http/http.dart' as http;
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_constants.dart';
import 'package:masterg/data/api/api_response.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/dealer/submit_reward_req.dart';
import 'package:masterg/data/models/request/home_request/poll_submit_req.dart';
import 'package:masterg/data/models/request/home_request/submit_feedback_req.dart';
import 'package:masterg/data/models/request/home_request/submit_survey_req.dart';
import 'package:masterg/data/models/request/home_request/track_announcement_request.dart';
import 'package:masterg/data/models/request/home_request/user_program_subscribe.dart';
import 'package:masterg/data/models/request/home_request/user_tracking_activity.dart';
import 'package:masterg/data/models/request/save_answer_request.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/providers/reel_controller.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/utility.dart';
import 'dart:ui' as ui;

import 'package:video_player/video_player.dart';

import '../../main.dart';

class HomeProvider {
  HomeProvider({required this.api});

  ApiService api;

  Map<String, dynamic> get defaultParams => {
        "key": api.env.apiKey,
      };

  Future<ApiResponse?> getMyAssignmentList({int? contentType}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.GET_ASSIGNMENT_API,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getContentList({int? contentType}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.GET_CONTENT_API,
          queryParameters: {
            'id': 1,
            'category_id': '$contentType',
            'language': UserSession.userContentLanguageId
          },
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getMyAssessmentList(
      {int? contentType, String? interestID, int? jobRoleID, int? skillID}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          ApiConstants.GET_ASSESSMENT_API + '?industry_domain_id=$interestID &jobrole_id=$jobRoleID &skill_id=$skillID',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getContentDetails({int? id}) async {
    final _url = ApiConstants().PRODUCTION_BASE_URL() +
        "api/joy/contents/" +
        id.toString();

    try {
      final response = await api.dio.get(_url,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getSwayamUserProfile() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.USER_PROFILE_SWAYAM_API,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.statusCode);
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> addPortfolioProfile({Map<String, dynamic>? data}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.ADD_PORTFOLIO_PROFILE,
          data: FormData.fromMap(data!),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> singularisDeletePortfolio(int portfolioId) async {
    try {
      Map<String, dynamic> data = Map();
      data['portfolio_id'] = portfolioId;

      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.PORTFOLIO_DELETE,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.statusCode);

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getUserProfile() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.USER_PROFILE_API,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.statusCode);

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> updateUserProfileImage(
      String? filePath, String? name, String? email) async {
    try {
      Map<String, dynamic> data = Map();
      if (filePath != null && filePath.isNotEmpty) {
        String fileName = filePath.split('/').last;
        data['profile_pic'] =
            await MultipartFile.fromFile(filePath, filename: fileName);
      } else {
        data['first_name'] = name;
        data['email_address'] = email;
      }
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.USER_PROFILE_IMAGE_API,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              responseType: ResponseType.json));
      Log.v(response.statusCode);

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> likeContent(
      int? contentId, String? type, int? like) async {
    try {
      Map<String, dynamic> data = Map();
      data["content_id"] = contentId;
      data['type'] = type;
      data['like'] = like;

      final response = await api.dio.post(ApiConstants.LIKE_CONTENT,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCertificatesList() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.CERTIFICATES_LIST,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getModuleLeaderboardList(String moduleId,
      {int type = 0}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          (type == 0
                  ? ApiConstants.MODULE_WISE_LEADERBOARD_LIST
                  : ApiConstants.REPORT_MODULE_WISE_LEADERBOARD_LIST) +
              '/' +
              moduleId,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> submitFeedback({FeedbackReq? req}) async {
    try {
      var formData = FormData.fromMap({
        "title": req?.title ?? "",
        "description": req?.description ?? "",
        "topic": req?.topic ?? "",
        "type": req?.type ?? "",
        "email": req?.email,
        "file": req?.filePath != ""
            ? await MultipartFile.fromFile('${req?.filePath}',
                filename: req!.filePath?.split("/").last)
            : ""
      });

      var header;
      if (UserSession.userToken == null) {
        header = {ApiConstants.API_KEY: ApiConstants().APIKeyValue()};
      } else {
        header = {
          "Authorization": "Bearer ${UserSession.userToken}",
          ApiConstants.API_KEY: ApiConstants().APIKeyValue()
        };
      }
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.FEEDBACK_API,
          data: formData,
          options: Options(
              method: 'POST',
              headers: header,
              contentType: "multipart/form-data"));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getTopicsList() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.TOPIC_API,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getFeedbackList() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.FEEDBACK_API,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getAssessmentReports({int? assessmentId}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio
          .get(ApiConstants.ASSESSMENT_REPORTS + 'assessment_id=$assessmentId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e, and stacktrace is $stackTrace');
    }
    return null;
  }

  Future<ApiResponse?> getAssignLearner(
      {int? isFaculty, int? programId}) async {
    try {
      Map<String, dynamic> data = Map();
      data['is_faculty'] = isFaculty;
      data["p_id"] = programId;

      final response = await api.dio.post(ApiConstants.ASSIGN_LEARNER,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      log('LOG:statusCode-----   ${response.statusCode}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        log('getAssignLearner====1');
        return ApiResponse.success(response);
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e, and stacktrace is $stackTrace');
    }
    return null;
  }

  Future<ApiResponse?> getContentTagsList({int? categoryType}) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    try {
      final response =
          await api.dio.get(ApiConstants.TAGS_API + "/$categoryType",
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCourseLeaderboardList(String courseId,
      {int type = 0}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          (type == 0
                  ? ApiConstants.LEADERBOARD_LIST
                  : ApiConstants.REPORT_LEADERBOARD_LIST) +
              '/' +
              courseId,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCourseModulesList(String courseId,
      {int type = 0}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          (type == 0
              ? ApiConstants.PROGRAMS_LIST + '/' + courseId
              : ApiConstants.REPORT_PROGRAMS_LIST),
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e and stacktrace is $stackTrace');
    }
    return null;
  }

  Future<ApiResponse?> getKPIAnalysisList() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.KPI_LIST,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCoursesList({required int type}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          type == 0
              ? ApiConstants.PROGRAMS_LIST
              : ApiConstants.REPORT_PROGRAMS_LIST,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> reportContent(
      String? status, int? contentId, String? category, String? comment) async {
    try {
      Map<String, dynamic> data = Map();
      data['status'] = status;
      data["user_id"] = Preference.getInt(Preference.USER_ID).toString();
      data['post_id'] = contentId;
      data['category'] = category;
      data['comments'] = comment;
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.REPORT_CONTENT,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              responseType: ResponseType.json));
      Log.v("DAta response is ${response.statusCode}");

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      } else {
        return ApiResponse.success(response);
      }
    } catch (e) {
      Log.v('exception is $e');
    }
    return null;
  }

  Future<ApiResponse?> getLanguage(int? languageType) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          languageType == 1
              ? ApiConstants.APP_LANGUAGE_API
              : languageType == 2
                  ? ApiConstants.LANGUAGE_API
                  : ApiConstants.APP_LANGUAGE_API,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getMasterLanguage() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.MASTER_LANGUAGE_API,
          options: Options(
              method: 'GET',
              headers: {
                //"Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
               debugPrint('language response is  $response');
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          Log.v("====> ${response.statusCode}");
          return ApiResponse.error(response.data);
        } else {
          Log.v("====> ${response.statusCode}");
          return ApiResponse.success(response);
        }
      }
    } catch (e,stacktrace) {
      debugPrint('exception is $e and stacktrace is $stacktrace');
    }
    return null;
  }

  Future<ApiResponse?> deletePost(int? postId) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    try {
      final response =
          await api.dio.get(ApiConstants.DELETE_POST + postId.toString(),
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          Log.v("====> ${response.statusCode}");
          return ApiResponse.error(response.data);
        } else {
          Log.v("====> ${response.statusCode}");
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> openToWork(int? openToWork) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    Map<String, dynamic> data = Map();
    data['open_to_work'] = openToWork.toString();
    try {
      final response = await api.dio.post(ApiConstants.OPEN_TO_WORK,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          Log.v("====> ${response.statusCode}");
          return ApiResponse.error(response.data);
        } else {
          Log.v("====> ${response.statusCode}");
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getDeleteSkill(int? skillId) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    Map<String, dynamic> data = Map();
    data['id'] = skillId;

    try {
      final response = await api.dio.post(ApiConstants.DELETE_SKILL,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          Log.v("====> ${response.statusCode}");
          return ApiResponse.error(response.data);
        } else {
          Log.v("====> ${response.statusCode}");
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getjoyCategory() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          ApiConstants.JOY_CATEGORY +
              '?language_id=${Preference.getInt(Preference.APP_LANGUAGE) ?? 1}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getJobList() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.USER_JOBS_LIST,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getComment(int? postId) async {
    Map<String, dynamic> data = Map();
    data['post_id'] = postId;
    data['show_all'] = 1;
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.COMMENT_LIST,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> postComment(
      int? postId, int? parentId, String? comment) async {
    Map<String, dynamic> data = Map();
    data['post_id'] = postId;
    data['parent_id'] = parentId;
    data['content'] = comment;
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.POST_COMMENT_LIST,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getjoyContentList(JoyContentListEvent event) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    try {
      final response =
          //await api.dio.get(ApiConstants.JOY_CONTENT_LIST + '?is_featured=2',
          await api.dio.get(
              ApiConstants.JOY_CONTENT_LIST +
                  '?category_id=${event.fetchAll == true ? '' : Preference.getString('interestCategory')}',
              // '?language_id=${Preference.getInt(Preference.APP_LANGUAGE)}',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getjoyContentByPostId({int? postId}) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    try {
      final response =
          //await api.dio.get(ApiConstants.JOY_CONTENT_LIST + '?is_featured=2',
          await api.dio.get(ApiConstants.JOY_CONTENT_LIST + '?joy_id=$postId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> generateCertificate(
      int certificateId, int programId, String programName) async {
    try {
      final response = await api.dio.get(
          ApiConstants.GENERATE_CERTIFICATE +
              '?certificate_id=$certificateId&p_name=$programName&content_id=$programId',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants.API_KEY_VALUE
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getLiveClasses() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.LIVECLASSLIST,
          data: {'date': ''},
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      Log.v("oboardsessions");
    }
    return null;
  }

  // ignore: missing_return
  Future<ApiResponse?> getPrograms() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.COURSE_CATEGORY,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }


  //Add NEW
  Future<ApiResponse?> getSemesterList() async {
    try {
      final response = await api.dio.get(ApiConstants.SEMESTER_LIST,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));


      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCourseWithId(int? id, int? semesterID) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response =
          await api.dio.get(ApiConstants.COURSE_LIST + '?category_id=$id &semester_id=$semesterID',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getDashboardIsVisible() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get('${ApiConstants.settings}?type=1',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              // contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getOrganizationProgram(int? fetchGoalList) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get('${ApiConstants.ORG_PROGRAM_LIST}?fetch_goal_list='+'${fetchGoalList}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              // contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getDasboardList() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.DASHBOARD_CONTENT,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              // contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      // log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      log('response chek is $response');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, stacktrace) {
      log('response chek is $stacktrace');
    }
    return null;
  }

  // ignore: missing_return
  Future<ApiResponse?> getFeaturedVideo() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.FEATURED_VIDEOS,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getInterestPrograms() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          ApiConstants.INTEREST_PROGRAM_LIST +
              '?language_id=${Preference.getInt(Preference.APP_LANGUAGE) ?? 1}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> mapInterest(String? parameter, String? mapType) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      //final response = await api.dio.
      //get(ApiConstants.MAP_INTEREST + '?category_ids=$parameter', //This is for Old interest area  save data

      final response = await api.dio.get(
          mapType == 'InterestArea'
              ? ApiConstants.MAP_INTEREST + '?interestarea_ids=$parameter'
              : mapType == 'set_goal' ? ApiConstants.MAP_INTEREST +
              '?jobrole_id=$parameter' : ApiConstants.MAP_INTEREST +
                  '?category_ids=$parameter', //This is for new interest area  save data
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getPopularCourses() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.POPULARCOURSES,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> bottombarResponse() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;

      //handle is skill enable or not

      final skillEnableResponse =
          await api.dio.get('${ApiConstants.settings}?type=5',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  // contentType: "application/json",
                  responseType: ResponseType.json));

      if (skillEnableResponse.statusCode == 200 ||
          skillEnableResponse.statusCode == 201) {
        Preference.setBool(
            Preference.ENABLE_SKILL,
            (int.tryParse(
                        '${ApiResponse.success(skillEnableResponse).body['data']['enable_skill']}') ??
                    0) ==
                1);
      }

      final response = await api.dio.get('${ApiConstants.settings}?type=7',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              // contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
      Preference.setString('region', currentTimeZone);
      // print('date time now is $currentTimeZone is set');
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        log('Menu--${response.data}');
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getFilteredPopularCourses() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          ApiConstants.POPULARCOURSES +
              '?types=short_term,recommended,most_viewed,highly_rated,other_learners',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                //"Authorization":"bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9iODBmLTE4Mi03Ni00MS01OS5uZ3Jvay5pb1wvYXBpXC9sb2dpbiIsImlhdCI6MTY0NjY0NTgzOCwiZXhwIjoxNjUwMjQ1ODM4LCJuYmYiOjE2NDY2NDU4MzgsImp0aSI6Ild6b0oxeGlub3FSdktXT3AiLCJzdWIiOjE2LCJwcnYiOiI4N2UwYWYxZWY5ZmQxNTgxMmZkZWM5NzE1M2ExNGUwYjA0NzU0NmFhIn0.r8mR2JABQ7jMZRs-Rg1iRZ1SsckjSIyD1bp2L58uZdc",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> GCarvaanPost(
      int callCount, int? postId, bool userActivity) async {
    try {
      int from = ((callCount - 1) * 10) + 1;
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          userActivity
              ? ApiConstants.GCARVAAN_POST +
                  '?from=$from&count=10&content_type=0&created_by_id=${Preference.getInt(Preference.USER_ID)}'
              : postId != null
                  ? ApiConstants.GCARVAAN_POST +
                      '?from=1&count=1&content_type=0&id=$postId'
                  : ApiConstants.GCARVAAN_POST +
                      '?from=$from&count=10&content_type=0',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCarvaanPost(int? postId) async {
    try {
      final response = await api.dio.get(
          ApiConstants.GCARVAAN_POST +
              '?from=1&count=1&content_type=0&id=$postId',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> GReelsPost(GReelsPostEvent event) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    try {
      final response = await api.dio.get(
          event.userActivity
              ? ApiConstants.GREELS_POST +
                  '?from=1&count=20&created_by_id=${Preference.getInt(Preference.USER_ID)}'
              : ApiConstants.GREELS_POST +
                  '?from=${event.from}&count=${event.count}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      print("the ressponse now iss now ${response.data}");

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, stackTrace) {
      print("the ressponse now iss now $e and $stackTrace");
    }
    return null;
  }

  Future<ApiResponse?> getSingleReel(int reelId) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    try {
      final response = await api.dio.get(
          ApiConstants.GREELS_POST +
              '?from=1&count=1&content_type=0&id=$reelId',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getPartners() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.GET_PARTNER_API,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCategorys() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.CATEGORY_API,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> submitReward(
      {required SubmitRewardReq submitRewardReq}) async {
    try {
      Log.v("ERROR DATA : $submitRewardReq");
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.SUBMIT_REWARD_API,
          data: json.encode(submitRewardReq.toJson()),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> activityAttempt(
      String? filePath, int? contentType, int? contentId) async {
    try {
      Map<String, dynamic> data = Map();
      if (filePath != null && filePath.isNotEmpty) {
        String fileName = filePath.split('/').last;
        data['file'] =
            await MultipartFile.fromFile(filePath, filename: fileName);
      }
      Log.v(data);
      data['content_id'] = contentId;
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.ACTIVITY_ATTEMPT_API,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      Log.v(e);
    }
    return null;
  }

  String getFileExtension(String filePath) {
    // Get the last portion of the path, which contains the file name
    final fileName = filePath.split('/').last;

    // Split the file name to get the name and extension
    final parts = fileName.split('.');
    if (parts.length > 1) {
      // Return the extension (the last part after the last dot)
      return parts.last.toLowerCase();
    } else {
      // If there is no extension, return an empty string or null based on your preference
      return '';
    }
  }

  bool isVideoFile(String filePath) {
    final extension = getFileExtension(filePath);
    return ['mp4', 'avi', 'mov', 'wmv', 'mkv'].contains(extension);
  }

  Future<double> getFileAspectRatio(File file) async {
    double ratio = 1.0;
    if (isVideoFile(file.path)) {
      final videoController = VideoPlayerController.file(file);

      // Initialize the video controller
      await videoController.initialize();

      // Calculate the ratio of the video
      double videoRatio = videoController.value.aspectRatio;

      videoController.dispose();
      ratio = videoRatio;

      // Dispose of the video controller when it's no longer needed
    } else {
      Image image = new Image.file(file);
      Completer<ui.Image> completer = new Completer<ui.Image>();
      image.image
          .resolve(new ImageConfiguration())
          .addListener(ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(info.image);

        ratio = info.image.width / info.image.height;
      }));
    }
    print('ratio is $ratio');

    return ratio;
  }

  Future<ApiResponse?> createPost(
    String? thumbnail,
    int? contentType,
    String? postType,
    String? title,
    String? description,
    List<String?>? filepaths,
  ) async {
    try {
      Log.v("upload file data is}");
      Map<String, dynamic> data = Map();
      List<MultipartFile> files = [];
      List<double> ratio = [];

      if (postType == 'caravan') {
        for (var item in filepaths!) {
          files.add(await MultipartFile.fromFile(item!,
              filename: item.split('/').last));

          // ratio.add(await getFileAspectRatio(File(item)));
          // new Image.network('https://i.stack.imgur.com/lkd0a.png');

        }
        int? height = 0;
        int? width = 0;
        await Utility.getSize(File('${filepaths.first}'))
            ?.then((List<int?>? value) {
          height = value?.last;
          width = value?.first;
        });
        data['file[]'] = files;
        data['aspect_ratio[]'] = ratio;
        data['dimension'] = '{"height":$height,"width":$width}';
      } else {
        files.add(await MultipartFile.fromFile(filepaths![0]!,
            filename: filepaths[0]!.split('/').last));
        data['thumbnail'] = await MultipartFile.fromFile(thumbnail!,
            filename: thumbnail.split('/').last);

        data['file'] = files;
      }

      data['post_type'] = postType;
      data['title'] = "";
      data['description'] = description;
      data['content_type'] = contentType;
      data['status'] = '1';
      Log.v("upload file data is $data");
      uploadProgressController.add(0);
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.CREATE_POST,
          data: FormData.fromMap(data), onSendProgress: (int sent, int total) {
        uploadProgressController.add(sent / total * 100);
      },
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      Log.v(e);
    }
    return null;
  }

  Future<ApiResponse?> getUserAnalytics() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.USER_ANALYTICS,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> listVideoResume(int? index) async {
    try {
      Map<String, dynamic> data = Map();
      if (index != null) {
        data['index_no'] = index;
      }
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.LIST_VIDEO_RESUME,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> anlyseVideoResume(AnalyseVideoResumeEvent event) async {
    try {
      Map<String, dynamic> data = Map();
      data['video_text'] = event.videoText;
      data['index_no'] = Math.max(0, event.index - 1);
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.ANALYSE_VIDEO_RESUME,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> deleteVideoResume(DeleteVideoResumeEvent event) async {
    try {
      Map<String, dynamic> data = Map();

      data['index_no'] = event.videoIndex;
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(
          event.isSetPrimary == true
              ? ApiConstants.SET_PRIMART_VIDEO_RESUME
              : ApiConstants.DELETE_VIDEO_RESUME,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> addResume(Map<String, dynamic> data) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.ADD_RESUME,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> addSocial(Map<String, dynamic> data) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.ADD_SOCIAL,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri} and response is ${response.data}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> uploadProfile(Map<String, dynamic> data) async {
    try {
      print('porifle video add data $data');
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.UPDATE_PROFILE,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getLearningSpaceData() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.LEARNINGSPACE_DATA,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getPortfolioCompetition(int? userId) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          ApiConstants.GET_PORTFOLIO_COMPETITION +
              '${userId != null ? '?user_id_for_webview=$userId' : ''}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCompetitionMyActivity(
      {String? competitionType}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio
          .get(ApiConstants.COMPETITION_MY_ACTIVITY + '/?type=$competitionType',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('$stackTrace and $e');
    }
    return null;
  }

  Future<ApiResponse?> trackAnnouncment(
      {required TrackAnnouncementReq submitRewardReq}) async {
    try {
      Log.v("ERROR DATA : $submitRewardReq");
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.TRACK_ANNOUNCMENT_API,
          data: json.encode(submitRewardReq.toJson()),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> UserSubscribe(
      {required UserProgramSubscribeReq subrReq}) async {
    try {
      Log.v("ERROR DATA : $subrReq");
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.SUBSCRIBE_PROGRAM,
          data: json.encode(subrReq.toJson()),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> UserTrackingActivityReq(
      {required UserTrackingActivity submitRewardReq}) async {
    try {
      Log.v("ERROR DATA9999999 : ${submitRewardReq.toJson()}");
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.TRACK_USER_ACTIVITY,
          data: json.encode(submitRewardReq.toJson()),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> attemptTest({required String request}) async {
    Utility.hideKeyboard();
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
        ApiConstants.ATTEMPT_ASSESSMENT + request,
        options: Options(
          method: 'GET',
          contentType: "application/json",
          headers: {
            "Authorization": "bearer ${UserSession.userToken}",
            ApiConstants.API_KEY: ApiConstants().APIKeyValue()
          },
          responseType: ResponseType.json,
        ),
      );
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
    }
    return null;
  }

  Future<ApiResponse?> saveAnswer({required SaveAnswerRequest request}) async {
    Utility.hideKeyboard();
    try {
      Map<String, dynamic> data = Map();
      // List<int?> id = [1];

      /*data = {
        ...request.toJson(),
        'option_id': [request.optionId]
      };*/
      data = {
        ...request.toJson(),
        'option_id': request.optionId
      };
      try {
        data = {
          ...data,
          'user_file': await MultipartFile.fromFile('${request.userFile}',
              filename: request.userFile?.split("/").last),
        };
      } catch (e) {}
      log("form data is singh-- $data");
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(
        ApiConstants.SAVE_ANSWER,
        //data: FormData.fromMap(data),
        data: request.toJson(),
        options: Options(
          method: 'POST',
          contentType: "application/json",
          headers: {
            "Authorization": "bearer ${UserSession.userToken}",
            ApiConstants.API_KEY: ApiConstants().APIKeyValue()
          },
          responseType: ResponseType.json,
        ),
      );

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data sdf==> ${e.response!.statusCode}");
      }
    }
    return null;
  }

  Future<ApiResponse?> submitAnswer({String? request}) async {
    Utility.hideKeyboard();
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(
        ApiConstants.SUBMMIT_ANSWER,
        data: {"content_id": request},
        options: Options(
          method: 'POST',
          contentType: "application/json",
          headers: {
            "Authorization": "bearer ${UserSession.userToken}",
            ApiConstants.API_KEY: ApiConstants().APIKeyValue()
          },
          responseType: ResponseType.json,
        ),
      );
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
    }
    return null;
  }

  Future<ApiResponse?> getParticipate(
      {String? name,
      String? email,
      String? mobileNo,
      int? isMobile,
      int? programId,
      int? countryCode}) async {
    Utility.hideKeyboard();
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      Map<String, dynamic> data = Map();
      data['name'] = name;
      data['email'] = email;
      data['mobile'] = mobileNo;
      data['pid'] = programId;
      data['is_mobile'] = isMobile;
      data['country_code'] = countryCode;
      final response = await api.dio.post(
        ApiConstants.EVENT_PARTICIAPTION,
        data: FormData.fromMap(data),
        options: Options(
          method: 'POST',
          contentType: "application/json",
          headers: {
            "Authorization": "bearer ${UserSession.userToken}",
            ApiConstants.API_KEY: ApiConstants().APIKeyValue()
          },
          responseType: ResponseType.json,
        ),
      );
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
    }
    return null;
  }

  ///code send on Email
  Future<ApiResponse?> emailSendCode(
      {String? email, int? isSignup, int? forgotPass}) async {
    Utility.hideKeyboard();
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(
        ApiConstants.SEND_EMAIL_CODE,
        data: {
          "email": email,
          "is_signup": isSignup,
          "forgot_pass": forgotPass ?? 0,
          "locale": Preference.getString(Preference.APP_ENGLISH_NAME).toString()
        },
        options: Options(
          method: 'POST',
          contentType: "application/json",
          headers: UserSession.userToken != null
              ? {
                  "Authorization": "bearer ${UserSession.userToken}",
                  ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                }
              : {ApiConstants.API_KEY: ApiConstants().APIKeyValue()},
          responseType: ResponseType.json,
        ),
      );
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
    }
    return null;
  }

  Future<ApiResponse?> verifyEmailCode({String? email, String? eCode}) async {
    Utility.hideKeyboard();
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(
        ApiConstants.VERIFY_EMAIL_CODE,
        data: {
          "principal_email": email,
          "hscode": eCode,
        },
        options: Options(
          method: 'POST',
          contentType: "application/json",
          headers: {
            "Authorization": "bearer ${UserSession.userToken}",
            ApiConstants.API_KEY: ApiConstants().APIKeyValue()
          },
          responseType: ResponseType.json,
        ),
      );
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
    }
    return null;
  }

  Future<ApiResponse?> passwordUpdate(
      {String? email, String? pass, String? locale}) async {
    Utility.hideKeyboard();
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(
        ApiConstants.PASSWORD_UPDATE,
        data: {"email": email, "password": pass, "locale": locale},
        options: Options(
          method: 'POST',
          contentType: "application/json",
          headers: {
            "Authorization": "bearer ${UserSession.userToken}",
            ApiConstants.API_KEY: ApiConstants().APIKeyValue()
          },
          responseType: ResponseType.json,
        ),
      );
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
    }
    return null;
  }

  ///

  Future<ApiResponse?> reviewTest({required String request}) async {
    Utility.hideKeyboard();
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
        ApiConstants.ASSESSMENT_REVIEW + request,
        options: Options(
          method: 'GET',
          contentType: "application/json",
          headers: {
            "Authorization": "bearer ${UserSession.userToken}",
            ApiConstants.API_KEY: ApiConstants().APIKeyValue()
          },
          responseType: ResponseType.json,
        ),
      );
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getSubmissions({int? request}) async {
    Utility.hideKeyboard();
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
        ApiConstants.ASSIGNMENT_SUBMISSION_DETAILS + "/$request",
        options: Options(
          method: 'GET',
          contentType: "application/json",
          headers: {
            "Authorization": "bearer ${UserSession.userToken}",
            ApiConstants.API_KEY: ApiConstants().APIKeyValue()
          },
          responseType: ResponseType.json,
        ),
      );
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> createPortfolio(Map<String, dynamic> data) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.CREATE_PORTFOLIO,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.statusCode);

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
      //return ApiResponse.failure(e, message: e.response.data["message"]);
    }
    return null;
  }

  Future<ApiResponse?> masterBrandCreate(Map<String, dynamic> data) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.MASTER_BRAND_CREATE,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.statusCode);

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);

        /*if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }*/

      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
      //return ApiResponse.failure(e, message: e.response.data["message"]);
    }
    return null;
  }

  Future<ApiResponse?> userBrandCreate(Map<String, dynamic> data) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.USER_BRAND_CREATE,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.statusCode);

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
      //return ApiResponse.failure(e, message: e.response.data["message"]);
    }
    return null;
  }

  Future<ApiResponse?> deletePortfolio(int id) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.delete(ApiConstants.PORTFOLIO + '/$id',
          options: Options(
              method: 'DELETE',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.statusCode);

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
      //return ApiResponse.failure(e, message: e.response.data["message"]);
    }
    return null;
  }

  Future<ApiResponse?> listPortfolio(String type, int userId) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio
          .get(ApiConstants.PORTFOLIO + '?type=$type&user_id=$userId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      Log.v(response.statusCode);

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      if (e is DioError) {
        Log.v("data ==> ${e.response!.statusCode}");
      }
      //return ApiResponse.failure(e, message: e.response.data["message"]);
    }
    return null;
  }

  Future<ApiResponse?> getSurveyDataList({int? contentId, int? type}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          (type == 1 ? ApiConstants.SURVEY_API : ApiConstants.POLL_API) +
              "/$contentId",
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> submitSurvey({SubmitSurveyReq? req}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.SURVEY_API,
          data: json.encode(req?.toJson()),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.data);
      Log.v(response.statusCode);
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> removeAccount({String? type}) async {
    try {
      Map<String, dynamic> data = Map();
      data['type'] = type;
      data['locale'] =
          Preference.getString(Preference.APP_ENGLISH_NAME).toString();
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.REMOVE_ACCOUNT,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      Log.v(response.data);
      Log.v(response.statusCode);
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getNotifications() async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.NOTIFICATION_API,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getNotificationsList({int? fromValue, int? toValue}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(ApiConstants.NOTIFICATION_LIST+'?from=$fromValue&to=$toValue',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              //contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e, and stacktrace is $stackTrace');
    }
    return null;
  }

  Future<ApiResponse?> getNotificationsRead(
      {String? id, int? notiId, String? type, String? isRead}) async {
    try {
      Map<String, dynamic>? data = Map();
      data['id'] = id;
      data['notifiable_id'] = notiId;
      data['type'] = type;
      data['read_content'] = isRead;

      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.NOTIFICATION_READ,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e, and stacktrace is $stackTrace');
    }
    return null;
  }

  Future<ApiResponse?> submitPoll({PollSubmitRequest? req}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.POLL_API,
          data: json.encode(req?.toJson()),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getFilterDomainList(String ids) async {
    try {
      Map<String, dynamic>? data = Map();
      data['type'] = 'job';
      data['domain_id'] = ids;
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.DOMAIN_LIST,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (error) {
      return ApiResponse.error((error as DioError).response);
    }
    return null;
  }

  Future<ApiResponse?> getDomainList() async {
    try {
      Map<String, dynamic>? data = Map();
      data['type'] = 'domain';
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.DOMAIN_LIST,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> jobDomainDetail(int domainId) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;

    try {
      final response =
          await api.dio.get(ApiConstants.JOB_DOMAIN_DETAIL + '$domainId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCompetitionDetail({int? moduleId}) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    try {
      final response =
          await api.dio.get(ApiConstants.COMPETITION_MODULE_DATA + '$moduleId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getCompetitionList(
      {bool? isPopular,
      bool? isFiltter,
      String? jobIds,
      String? domainId}) async {
    try {
      String url = ApiConstants.COMPETITION_MODULE_DATA;

      if (isFiltter == false) {
        if (isPopular == true) url += '?is_popular=1';
      } else if (isFiltter == true) {
        if (isPopular == true)
          url = url +
              '?job_ids=$jobIds' +
              '&is_popular=1' +
              '&domain_id=$domainId';
        else
          url += '?job_ids=$jobIds' + '&domain_id=$domainId';
      }
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(url,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  ///JOB Show Singh
  Future<ApiResponse?> getJobCompApiList(
      {bool? isPopular,
      bool? isFiltter,
      String? jobIds,
      int? isJob,
      int? myJob,
      String? domainId,
      String? widgetType}) async {
    try {
      String url = ApiConstants.COMPETITION_MODULE_DATA;

      if (isFiltter == false) {
        if (isPopular == true) {
          url += '?is_popular=1' + '&is_job=$isJob'; //dashboard
        } else {
          if (widgetType == 'allJob') {
            url += '?is_job=$isJob';
          } else if (widgetType == 'myJob') {
            url += '?is_job=$isJob' + '&my_jobs=$myJob';
          } else if (widgetType == 'recomJob') {
            url += '?is_featured=1&is_job=1';
          }

          //url += '?is_popular='+'&is_job=$isJob' +'&my_jobs=$myJob';
        }
      } else if (isFiltter == true) {
        if (isPopular == true)
          url = url +
              '?job_ids=$jobIds' +
              '&is_popular=1' +
              '&is_job=1' +
              '&domain_id=$domainId';
        else {
          if (jobIds == '') {
            if (widgetType == 'recomJob') {
              url += '?&is_job=1' + '&domain_id=$domainId' + '&is_featured=1';
            } else {
              url += '?&is_job=1' + '&domain_id=$domainId';
            }
          } else if (widgetType == 'recomJob') {
            if (jobIds == '') {
              url += '?&is_job=1' + '&domain_id=$domainId' + '&is_featured=1';
            } else {
              url += '?job_ids=$jobIds' +
                  '&is_job=1' +
                  '&domain_id=$domainId' +
                  '&is_featured=1';
            }
          } else
            url += '?job_ids=$jobIds' + '&is_job=1' + '&domain_id=$domainId';
        }
      }
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(url,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<dynamic> getTrainingDetail(int? programId) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    try {
      final response =
          await api.dio.get(ApiConstants.PROGRAMS_LIST + '/$programId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
  }

  Future<ApiResponse?> getPortfolio(int? userId) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          ApiConstants.USER_PORTFOLIO +
              '${userId != null ? '?user_id_for_webview=$userId' : ''}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: our  ${response.data}');

      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      print('the excception is $e');
    }
    return null;
  }

  Future<ApiResponse?> getCompetitionContentList(
      {int? competitionId, int? isApplied}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          ApiConstants.COMPETITION_CONTENT_LIST +
              '$competitionId' +
              '?is_applied=$isApplied',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }
  //leaderboard

  Future<ApiResponse?> getLeaderboard(
      {int? id, String? type, int? skipotherUser, int? skipcurrentUser}) async {
    try {
      Map<String, dynamic> data = Map();
      data['id'] = id;
      data['type'] = type;
      data['skipotherUser'] = skipotherUser;
      data['skipcurrentUser'] = skipcurrentUser;
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.LEADERBOARD,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> topScoringUser(
      {int? userId, bool? skipCurrentUser}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.get(
          userId == null
              ? ApiConstants.TOP_SCORING_USER
              : ApiConstants.TOP_SCORING_USER + '?id=' + userId.toString(),
          queryParameters: {
            'skip_current_user': skipCurrentUser == true ? 1 : 0
          },
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> addPortfolio({Map<String, dynamic>? data}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.ADD_PORTFOLIO,
          data: FormData.fromMap(data!),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> addProfessional({Map<String, dynamic>? data}) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.ADD_PROFESSIONAL,
          data: FormData.fromMap(data!),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> updateVideoCompletion(
      int bookmark, int contentId, int completionPercent) async {
    try {
      Map<String, dynamic> data = Map();
      data['bookmark'] = bookmark;
      data['content_id'] = contentId;
      // data['per_comp'] = completionPercent;

      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.UPDATE_COURSE_COMPLETION,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> piDetail(int userId) async {
    try {
      Map<String, dynamic> data = Map();
      data['ref_id'] = userId;
      data['organization_id'] =
          Preference.getString(Preference.ORGANIZATION_ID);
      final dio = Dio();

      print('make api call $data');
      final response =
          await dio.post(ApiConstants.PI_DETAIL, data: FormData.fromMap(data));
      print('make api call ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getZoomOpenUrl(int contentId) async {
    try {
      Map<String, dynamic> data = Map();
      data['content_id'] = contentId;

      final startTime = DateTime.now().millisecondsSinceEpoch;
      final response = await api.dio.post(ApiConstants.GET_OPEN_URL,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $duration ms and url is  ${response.requestOptions.uri}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> popularjobInternship() async {
    try {
      final response = await api.dio.get(ApiConstants.POPULAR_JOB_INTERNSHIP,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> popularInternship() async {
    try {
      final response = await api.dio.get(ApiConstants.INTERNSHIP_LIST,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> companyJobs(String name) async {
    try {
      final response = await api.dio.get(ApiConstants.COMPANY_JOB_LIST + name,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getAssessmentDetails({int? contentId}) async {
    try {
      final response =
          await api.dio.get(ApiConstants.Assessment_Details + '$contentId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getAssessmentCertificate(
      {int? certificateId, int? contentId}) async {
    try {
      final response = await api.dio.get(
          ApiConstants.Assessment_certificate +
              '?certificate_id=$certificateId&p_name=program&content_id=$contentId',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> topCompanies() async {
    try {
      final response = await api.dio.get(ApiConstants.COMPANY_LIST,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getFacultyBatchDetails(int? courseId) async {
    try {
      final response = await api.dio.get(
          ApiConstants.FACULTY_BATCH_DETAILS +
              '${courseId != null ? '?course_id=${courseId}' : ''}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {}
      }
    } catch (e, stacktrace) {
      Log.v('the stack trace is $stacktrace');
    }
    return null;
  }

  Future<ApiResponse?> getFacultyCourseDetails({int? programId}) async {
    try {
      final response = await api.dio
          .get(ApiConstants.FACULTY_BATCH_DETAILS + '?course_id=$programId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {}
      }
    } catch (e, stacktrace) {
      Log.v('the stack trace is $stacktrace');
    }
    return null;
  }

  Future<ApiResponse?> getMatchingJobs() async {
    try {
      final response = await api.dio.get(ApiConstants.MATCHING_JOB,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {}
      }
    } catch (e, stacktrace) {
      Log.v('the stack trace is $stacktrace');
    }
    return null;
  }

  Future<ApiResponse?> getFacultyBatchClass({String? selectedDate}) async {
    try {
      final response = await api.dio.get(
          selectedDate != null
              ? ApiConstants.FACULTY_BATCH_CLASS + '?class_date=$selectedDate'
              : ApiConstants.FACULTY_BATCH_CLASS,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getWowDashboard() async {
    try {
      //final response = await api.dio.get('https://mecdev.learningoxygen.com/api/wow-details',
      final response = await api.dio.get(ApiConstants.WOW_DASHBOARD,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {}
      }
    } catch (e, stackTrace) {
      debugPrint('exception is $e and $stackTrace');
    }
    return null;
  }

  Future<ApiResponse?> getFacultyModuleList({int? courseId}) async {
    try {
      final response = await api.dio.get(
          ApiConstants.FACULTY_MODULE_LIST +
              '${courseId != null ? '?course_id=${courseId}' : ''}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e,stacktrace) {
      debugPrint('$stacktrace and $e');
    }
    return null;
  }

  Future<ApiResponse?> getModuleLeaderProgramList({int? courseId}) async {
    try {
      final response = await api.dio.get(
          ApiConstants.MODULE_LEADER_PROGRAM_LIST +
              '${courseId != null ? '?course_id=${courseId}' : ''}',
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e,stacktrace) {
      debugPrint('stacktrace is $stacktrace and exception is $e');
    }
    return null;
  }

  Future<ApiResponse?> getHodModuleList() async {
    try {
      final response = await api.dio.get(ApiConstants.HOD_PROGRAM_LIST,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getFacultyBatchAssignment(
      {int? courseId, String? selectedDate}) async {
    try {
      final response = await api.dio.get(
          courseId == null && selectedDate != null
              ? ApiConstants.FACULTY_BATCH_ASSIGNMENT +
                  '?class_date=$selectedDate'
              : ApiConstants.FACULTY_BATCH_ASSIGNMENT,
          // ApiConstants.FACULTY_BATCH_ASSIGNMENT,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getFacultyBatchAssessment(
      {int? courseId, String? selectedDate}) async {
    try {
      final response = await api.dio.get(
          courseId != null && selectedDate != null
              ? ApiConstants.FACULTY_BATCH_ASSESSMENT +
                  '?class_date=$selectedDate'
              : ApiConstants.FACULTY_BATCH_ASSESSMENT,
          // ApiConstants.FACULTY_BATCH_ASSESSMENT,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getProgramCompletion({dynamic programCompId}) async {
    try {
      final response =
          await api.dio.get(ApiConstants.PROGRAM_COMPLETION + '$programCompId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getMarkAttendance({int? batchId, int? classId}) async {
    try {
      final response = await api.dio
          .get(ApiConstants.MARK_ATTENDANCE + '$classId' + '?batch_id=$batchId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getUpdateAttendance(
      {int? contentId, String? attendance, List<int>? users}) async {
    try {
      Map<String, dynamic> queryParameters = {
        'contentId': contentId,
        'attendance': attendance,
        'users[]': users,
      };

      final response = await api.dio.get(ApiConstants.UPDATE_ATTENDANCE,
          queryParameters: queryParameters,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> get({int? programCompId}) async {
    try {
      final response =
          await api.dio.get(ApiConstants.PROGRAM_COMPLETION + '$programCompId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getAttendancePercentage(
      {int? programCompletionId}) async {
    try {
      final response = await api.dio
          .get(ApiConstants.ATTENDANCE_PERCENTAGE + '$programCompletionId',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e, Stacktrace) {}
    return null;
  }

  Future<ApiResponse?> getSkillSuggestion() async {
    try {
      final response = await api.dio.get(ApiConstants.SKILL_SUGGESTION,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getSkillRating() async {
    try {
      final response = await api.dio.get(ApiConstants.MY_SKILL,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> addSkill(Map<String, dynamic> data) async {
    try {
      final response = await api.dio.post(ApiConstants.ADD_SKILL,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getExploreJobList(
      {int? indexNo, bool? doRefresh = false, String? resumeUrl}) async {
    log('do indexNo is $indexNo');
    log('do resumeUrl is $resumeUrl');
    log('do refresh is $doRefresh');
    try {
      Map<String, dynamic> data = Map();
      data['index_no'] = indexNo ;
      if (resumeUrl != null) data['resumeUrl'] = resumeUrl;
      final startTime = DateTime.now().millisecondsSinceEpoch;

      if (doRefresh == true) {
        await api.dio.post(ApiConstants.EXPLORE_JOBS_REFRESH,
            data: FormData.fromMap(data),
            options: Options(
                method: 'POST',
                headers: {
                  "Authorization": "Bearer ${UserSession.userToken}",
                  ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                },
                contentType: "application/json",
                responseType: ResponseType.json));
      }
      final response = await api.dio.post(ApiConstants.EXPLORE_JOBS,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      final endTime = DateTime.now().millisecondsSinceEpoch;

      final duration = endTime - startTime;
      log('LOG: duration: $data ms and url is  ${response.data}');
      if (response.statusCode == 200 || response.statusCode == 201) {
         return ApiResponse.success(response);
        // if (response.data.containsKey('error') &&
        //     (response.data["error"] as List).length != 0) {
        //   return ApiResponse.error(response.data);
        // } else {
        //   return ApiResponse.success(response);
        // }
      }
    } catch (e,stackTrace){
      debugPrint('exception is $e and $stackTrace');
    }
    return null;
  }

  Future<ApiResponse?> getExploreJobDetails(String jobId) async {
    try {
      final response =
          await api.dio.get(ApiConstants.JOB_DETAILS_COMPLETE + '?job=${jobId}',
              options: Options(
                  method: 'GET',
                  headers: {
                    "Authorization": "Bearer ${UserSession.userToken}",
                    ApiConstants.API_KEY: ApiConstants().APIKeyValue()
                  },
                  contentType: "application/json",
                  responseType: ResponseType.json));

      print('response=== ${response.data}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getApplyJobEntry({int? jobId, String? jobType}) async {
    try {
      Map<String, dynamic> data = Map();
      data['job_id'] = jobId;
      data['job_type'] = jobType;
      final response = await api.dio.post(ApiConstants.EXPLORE_APPLY_JOB,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).length != 0) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> generateSimilarity({int? submissionId}) async {
    try {
      Map<String, dynamic> data = Map();
      data['submission_id'] = submissionId;
      final response = await api.dio.post(ApiConstants.GENERATE_SIMILARITY,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));


      Log.v('response33=====${response.statusCode}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        //Log.v('response3344=====${response.data['message']}');
        return ApiResponse.success(response.data['message']);
      }
    } catch (e) {}
    //return null;
  }

}
